# Seller订单卡片信息断言方法封装

## 优化概述

将 `test_108219_mWeb_my_canceled_order_ui_ux.py` 测试用例中第69-87行的seller订单卡片验证逻辑封装成公共的断言方法，提高代码的可复用性和维护性。

## 封装的公共方法

### `assert_seller_order_card_info(order_item)`

**位置**: `src/Mweb/EC/mweb_pages/mweb_order_page/mweb_order_page.py`

**功能**: 验证seller订单卡片的完整信息

**参数**:
- `order_item`: 订单卡片元素（Playwright Locator对象）

**返回值**:
- `bool`: 验证是否成功

**验证内容**:
1. **订单内容信息**: 验证订单状态元素是否可见
2. **Seller订单标识**: 验证vendor-title-link元素是否可见
3. **配送日期信息**: 
   - 预计送达标签 (`wid-order-list-seller-delivery-date-label`)
   - 配送日期 (`wid-order-list-pick-date`)
4. **订单号信息**:
   - 订单号标签 (`wid-order-list-order-number-label`)
   - 订单号 (`wid-order-list-order-number`)
5. **总金额信息**:
   - 总金额标签 (`wid-order-list-totals-label`)
   - 总金额数值验证（使用正则表达式提取并验证 > 0）
6. **订单状态**: 验证状态为 "Canceled" 或 "Cancelled"
7. **产品信息**: 验证订单产品元素是否可见

## 原始代码 vs 优化后代码

### 原始代码（第69-87行）
```python
for index, item_S in enumerate(order_S_items):
    # 验证前10 个订单seller内容信息
    assert item_S.get_by_test_id(mweb_order_list_ele.order_list_card_status_ele).is_visible(), "订单内容信息不存在"
    # seller 订单
    assert item_S.get_by_test_id("wid-order-list-vendor-title-link").is_visible(), "seller订单icon信息不存在"
    # 验证订单卡片上 Delivery date\Order number\Items\Total 信息
    assert item_S.get_by_test_id("wid-order-list-seller-delivery-date-label").is_visible(), " 预计送达信息不存在"
    assert item_S.get_by_test_id("wid-order-list-pick-date").is_visible(), " Delivery date信息不存在"
    assert item_S.get_by_test_id("wid-order-list-order-number-label").is_visible(), " Order number 信息不存在"
    assert item_S.get_by_test_id("wid-order-list-order-number").is_visible(), " Order number信息不存在"
    assert item_S.get_by_test_id("wid-order-list-totals-label").is_visible(), "Total 信息不存在"
    totals_amount = item_S.get_by_test_id("wid-order-list-totals-amount").text_content()
    # 提取数字部分
    import re
    assert float(re.findall(r'\d+\.\d+', totals_amount)[0]) > 0, "Total 信息不存在"
    # 断言订单状态是Cancelled
    assert item_S.get_by_test_id(mweb_order_list_ele.order_list_card_statu_ele).text_content() in ("Canceled",
                                                                                          "Cancelled"), "订单状态不是Cancelled"
    assert item_S.locator(mweb_order_list_ele.order_list_card_product_ele).is_visible()
```

### 优化后代码
```python
for index, item_S in enumerate(order_S_items):
    # 使用公共断言方法验证seller订单卡片信息
    assert order_page.assert_seller_order_card_info(item_S), f"第{index+1}个seller订单卡片信息验证失败"
```

## 优化效果

### 1. 代码简化
- **原始代码**: 19行复杂的断言逻辑
- **优化后**: 2行简洁的方法调用

### 2. 可复用性提升
- 其他测试用例可以直接调用 `assert_seller_order_card_info()` 方法
- 避免重复编写相同的验证逻辑

### 3. 维护性提升
- 验证逻辑集中在一个方法中，修改时只需要更新一处
- 统一的错误处理和日志记录

### 4. 错误信息优化
- 提供更详细的错误信息，包括订单索引
- 统一的异常处理机制

### 5. 代码质量提升
- 更好的代码组织结构
- 符合DRY（Don't Repeat Yourself）原则

## 使用示例

```python
# 在其他测试用例中使用
order_page = MWebOrderPage(page, header, context, "/order/list")
seller_orders = page.get_by_test_id("order-list-S-card").all()

for index, order in enumerate(seller_orders):
    assert order_page.assert_seller_order_card_info(order), f"第{index+1}个seller订单验证失败"
```

## 兼容性

- 完全兼容现有的测试环境
- 保持原有的验证逻辑不变
- 不影响其他测试用例的运行

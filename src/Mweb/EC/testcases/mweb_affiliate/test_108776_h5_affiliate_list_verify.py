import allure
import pytest
from playwright.sync_api import Page

from src.Mweb.EC.mweb_pages.mweb_page_affiliate.mweb_page_affiliate_list import PageH5AffiliateListOperations
from src.config.weee.log_help import log


@allure.story("H5-返利联盟分享清单验证")
class TestH5AffiliateListVerify:
    @allure.title("H5-返利联盟分享清单验证")
    @pytest.mark.h5affiliate
    def test_108776_h5_affiliate_list_verify(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):
        """
        测试步骤：
        1. 进入返利联盟分享清单页面
        2. 点击一键加购按钮
        3. 下滑页面并点击探索更多按钮
        4. 校验跳转后的URL是否正确
        """
        _page: Page = phone_page["page"]
        _context = phone_page["context"]
        
        # 初始化返利联盟分享清单页面对象
        affiliate_page = PageH5AffiliateListOperations(_page, h5_autotest_header)
        
        try:
            # 执行完整的返利联盟分享清单页面操作流程
            affiliate_page.affiliate_list_operations()
            
            # 记录测试成功日志
            log.info("返利联盟分享清单验证测试通过")
            
        except Exception as e:
            # 记录测试失败日志
            log.error(f"返利联盟分享清单验证测试失败: {str(e)}")
            raise

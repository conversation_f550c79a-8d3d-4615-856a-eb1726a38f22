{"name": "【102678】 Mobile-Account 页面-我的订单模块UI/UX验证", "status": "failed", "statusDetails": {"message": "AssertionError: 未切换到待晒单tab\nassert '/order/list?filter_status=6' in 'https://www.sayweee.com/en/social/post-review/review-product?review_source=post_review_my_orders'\n +  where 'https://www.sayweee.com/en/social/post-review/review-product?review_source=post_review_my_orders' = <Page url='https://www.sayweee.com/en/social/post-review/review-product?review_source=post_review_my_orders'>.url", "trace": "self = <src.Mweb.EC.testcases.mweb_account.mweb_order.test_102678_mWeb_account_my_order_ui_ux.TestMWebMyCanceledOrderUIUX object at 0x000001F98A2B1510>\nphone_page = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppDat... 'page': <Page url='https://www.sayweee.com/en/social/post-review/review-product?review_source=post_review_my_orders'>}\nh5_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...fT8021gAZsDogRlBFZXW7ZIH1TgkyAxatZtYUHpjcj7s6fe53mI2Ad6NCQ99oAM8dMOY5nPlY60RjjGOt0mjBJJIrE_JYpoUPwHjuzIryWWdq-Cw', ...}\nh5_open_and_close_trace = None\n\n    @allure.title(\"【102678】 Mobile-Account 页面-我的订单模块UI/UX验证\")\n    def test_102678_mWeb_account_my_order_ui_ux(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):\n        \"\"\"\n        【102678】 Mobile-Account 页面-我的订单模块UI/UX验证\n        测试步骤：\n        1、me页面的我的订单模块下面展示:待付款、待发货、已发货、待晒单、退换/售后\n        2、点击我的订单>,进入订单列表:默认选中“全部订单”tab\n        3、点击待付款:进入我的订单页面，待付款tab下\n        4、点击待发货:进入我的订单页面，待发货tab下\n        5、点击已发货：进入我的订单页面，已发货tab下\n        6、点击待晒单：进入我的订单页面，待晒单tab下\n        7、点击退换/售后：进入我的订单页面，退换/售后tab下\n    \n        \"\"\"\n        p: Page = phone_page.get(\"page\")\n        c = phone_page.get(\"context\")\n    \n        # 1. 进入me页面，验证我的订单模块展示\n        log.info(\"步骤1：进入me页面，验证我的订单模块展示\")\n        account_page = MWebOrderPage(p, h5_autotest_header, browser_context=c, page_url=\"/account\")\n        p.wait_for_timeout(3000)\n        log.info(\"成功进入订单列表页面\")\n        # 3. 滚动到我的订单模块\n        account_page.scroll_to_pos(mweb_account_ele.my_orders_ele)\n        log.info(\"滚动到我的订单模块\")\n        p.wait_for_timeout(2000)\n        # 断言 > 存在\n        assert p.get_by_test_id(\"wid-order-arrow-right\").is_visible()\n        # 断言 我的订单里 icon、title 都存在\n        assert p.get_by_test_id(\"wid-order-pending-image\").is_visible()\n        assert p.get_by_test_id(\"wid-order-pending-title\").is_visible()\n        # 断言如果存在数字球，大于99 显示99+\n        if (p.get_by_test_id(\"wid-order-pending-value\").is_visible()\n                and int(p.get_by_test_id(\"wid-order-pending-value\").text_content()) > 99):\n            assert p.get_by_test_id(\"wid-order-pending-value\").text_content() == \"99+\"\n    \n        assert p.get_by_test_id(\"wid-order-unshipped-image\").is_visible()\n        assert p.get_by_test_id(\"wid-order-unshipped-title\").is_visible()\n        # 断言如果存在数字球，大于99 显示99+\n        if (p.get_by_test_id(\"wid-order-unshipped-value\").is_visible()\n                and int(p.get_by_test_id(\"wid-order-unshipped-value\").text_content()) > 99):\n            assert p.get_by_test_id(\"wid-order-unshipped-value\").text_content() == \"99+\"\n    \n        assert p.get_by_test_id(\"wid-order-shipped-image\").is_visible()\n        assert p.get_by_test_id(\"wid-order-shipped-title\").is_visible()\n        # 断言如果存在数字球，大于99 显示99+\n        if (p.get_by_test_id(\"wid-order-shipped-value\").is_visible()\n                and int(p.get_by_test_id(\"wid-order-shipped-value\").text_content()) > 99):\n            assert p.get_by_test_id(\"wid-order-shipped-value\").text_content() == \"99+\"\n        assert p.get_by_test_id(\"wid-order-to_review-image\").is_visible()\n        assert p.get_by_test_id(\"wid-order-to_review-title\").is_visible()\n        # 断言如果存在数字球，大于99 显示99+\n        if (p.get_by_test_id(\"wid-order-to_review-value\").is_visible()\n                and int(p.get_by_test_id(\"wid-order-to_review-value\").text_content()) > 99):\n            assert p.get_by_test_id(\"wid-order-to_review-value\").text_content() == \"99+\"\n        assert p.get_by_test_id(\"wid-order-returns-image\").is_visible()\n        assert p.get_by_test_id(\"wid-order-returns-title\").is_visible()\n        # 断言如果存在数字球，大于99 显示99+\n        if (p.get_by_test_id(\"wid-order-returns-value\").is_visible()\n                and int(p.get_by_test_id(\"wid-order-returns-value\").text_content()) > 99):\n            assert p.get_by_test_id(\"wid-order-returns-value\").text_content() == \"99+\"\n        # 断言如果存在数字球，大于99 显示99+\n        if (p.get_by_test_id(\"wid-order-to_review-value\").is_visible()\n                and int(p.get_by_test_id(\"wid-order-to_review-value\").text_content()) > 99):\n            assert p.get_by_test_id(\"wid-order-to_review-value\").text_content() == \"99+\"\n        # 点击操作---------------------------------\n        # 点击我的订单> 入口\n        my_orders = p.get_by_test_id(mweb_account_ele.my_orders_link_ele)\n        assert my_orders.is_visible(), \"未找到我的订单> 入口\"\n        # 切换到全部订单tab\n        my_orders.click()\n        p.wait_for_timeout(2000)\n        # 验证已切换到全部订单tab\n        assert \"/order/list\" in p.url, \"未切换到全部订单tab\"\n        p.get_by_test_id(\"wid-page-nav-header-back-button\").click()\n        p.wait_for_timeout(2000)\n        # 待付款tab\n        pending_tab = p.get_by_test_id(mweb_account_ele.my_order_pending_tab_ele)\n        assert pending_tab.is_visible(), \"未找到待付款tab\"\n        # 切换到待付款tab\n        pending_tab.click()\n        p.wait_for_timeout(2000)\n        # 验证已切换到待付款tab\n        print(p.url)\n        assert \"/order/list?filter_status=1\" in p.url, \"未切换到待付款tab\"\n        p.get_by_test_id(\"wid-page-nav-header-back-button\").click()\n        p.wait_for_timeout(2000)\n        # 3. 滚动到我的订单模块\n        account_page.scroll_to_pos(mweb_account_ele.my_orders_ele)\n        log.info(\"滚动到我的订单模块\")\n        p.wait_for_timeout(2000)\n        # 切换到待发货tab\n        unshipped_tab = p.get_by_test_id(mweb_account_ele.my_order_unshipped_tab_ele)\n        assert unshipped_tab.is_visible(), \"未找到待发货tab\"\n        # 切换到待发货tab\n        unshipped_tab.click()\n        p.wait_for_timeout(2000)\n        # 验证已切换到待发货tab\n        assert \"/order/list?filter_status=2\" in p.url, \"未切换到待发货tab\"\n        p.get_by_test_id(\"wid-page-nav-header-back-button\").click()\n        p.wait_for_timeout(2000)\n        # 3. 滚动到我的订单模块\n        account_page.scroll_to_pos(mweb_account_ele.my_orders_ele)\n        log.info(\"滚动到我的订单模块\")\n        p.wait_for_timeout(2000)\n        # 切换到已发货tab\n        shipped_tab = p.get_by_test_id(mweb_account_ele.my_order_shipped_tab_ele)\n        assert shipped_tab.is_visible(), \"未找到已发货tab\"\n        # 切换到已发货tab\n        shipped_tab.click()\n        p.wait_for_timeout(2000)\n        assert \"/order/list?filter_status=3\" in p.url, \"未切换到已发货tab\"\n        p.get_by_test_id(\"wid-page-nav-header-back-button\").click()\n        p.wait_for_timeout(2000)\n        # 3. 滚动到我的订单模块\n        account_page.scroll_to_pos(mweb_account_ele.my_orders_ele)\n        log.info(\"滚动到我的订单模块\")\n        p.wait_for_timeout(2000)\n        # 切换到待晒单tab\n        reviewed_tab = p.get_by_test_id(mweb_account_ele.my_order_review_tab_ele)\n        assert reviewed_tab.is_visible(), \"未找到待晒单tab\"\n        # 切换到待晒单tab\n        reviewed_tab.click()\n        p.wait_for_timeout(2000)\n        # 验证已切换到待晒单tab\n>       assert \"/order/list?filter_status=6\" in p.url, \"未切换到待晒单tab\"\nE       AssertionError: 未切换到待晒单tab\nE       assert '/order/list?filter_status=6' in 'https://www.sayweee.com/en/social/post-review/review-product?review_source=post_review_my_orders'\nE        +  where 'https://www.sayweee.com/en/social/post-review/review-product?review_source=post_review_my_orders' = <Page url='https://www.sayweee.com/en/social/post-review/review-product?review_source=post_review_my_orders'>.url\n\ntest_102678_mWeb_account_my_order_ui_ux.py:139: AssertionError"}, "description": "\n        【102678】 Mobile-Account 页面-我的订单模块UI/UX验证\n        测试步骤：\n        1、me页面的我的订单模块下面展示:待付款、待发货、已发货、待晒单、退换/售后\n        2、点击我的订单>,进入订单列表:默认选中“全部订单”tab\n        3、点击待付款:进入我的订单页面，待付款tab下\n        4、点击待发货:进入我的订单页面，待发货tab下\n        5、点击已发货：进入我的订单页面，已发货tab下\n        6、点击待晒单：进入我的订单页面，待晒单tab下\n        7、点击退换/售后：进入我的订单页面，退换/售后tab下\n\n        ", "start": *************, "stop": *************, "uuid": "2e4f0ad3-8686-4ff8-8b26-f534205b3517", "historyId": "f112e5f34e3de78d13b585355752f098", "testCaseId": "f112e5f34e3de78d13b585355752f098", "fullName": "src.Mweb.EC.testcases.mweb_account.mweb_order.test_102678_mWeb_account_my_order_ui_ux.TestMWebMyCanceledOrderUIUX#test_102678_mWeb_account_my_order_ui_ux", "labels": [{"name": "story", "value": "【102678】 Mobile-Account 页面-我的订单模块UI/UX验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "h5order"}, {"name": "tag", "value": "mweb_regression"}, {"name": "tag", "value": "transaction"}, {"name": "tag", "value": "suqin"}, {"name": "parentSuite", "value": "src.Mweb.EC.testcases.mweb_account.mweb_order"}, {"name": "suite", "value": "test_102678_mWeb_account_my_order_ui_ux"}, {"name": "subSuite", "value": "TestMWebMyCanceledOrderUIUX"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "22128-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Mweb.EC.testcases.mweb_account.mweb_order.test_102678_mWeb_account_my_order_ui_ux"}]}
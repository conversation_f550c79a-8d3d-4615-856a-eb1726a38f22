{"name": "【108218】 订单列表All tab-列表流程验证", "status": "broken", "statusDetails": {"message": "playwright._impl._errors.Error: Execution context was destroyed, most likely because of a navigation", "trace": "self = <src.Mweb.EC.testcases.mweb_account.mweb_order.test_108218_mWeb_my_all_order_ui_ux.TestMWebMyCanceledOrderUIUX object at 0x00000267A7A9EA50>\nphone_page = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppDat...hrome.exe> version=120.0.6099.28>>, 'page': <Page url='https://www.sayweee.com/en/order/checkout?cart_domain=grocery'>}\nh5_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...u0ZyvakDUjD16z8FqSJ1oj69xx6eD7E4LPFOP4ZQ1m0zd2OPqErZh_UU8uQtBJcct2Qxwb31ubqhiLJP9jgtuMP6a1yy8HiWQLTRC2A__5azLtqk', ...}\nh5_open_and_close_trace = None\n\n    @allure.title(\"【108218】 订单列表All tab-列表流程验证\")\n    def test_108218_mWeb_my_all_order_ui_ux(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):\n        \"\"\"\n        【108218】 订单列表All tab-列表流程验证\n        测试步骤：\n        1、进入订单列表页面，点击切换到已取消tab下\n        2、如果存在订单，订单上会存在各种按钮\n        3、点击 退款详情 按钮，进入售后退款页面，再返回已送达页面\n        5、点击reorder 按钮 ，拉起再来一单选择商品页面，再返回已送达页面\n        \"\"\"\n        p: Page = phone_page.get(\"page\")\n        c = phone_page.get(\"context\")\n    \n        # 1. 进入订单列表页面\n        order_page = MWebOrderPage(p, h5_autotest_header, browser_context=c, page_url=\"/order/list\")\n        p.wait_for_timeout(3000)\n        log.info(\"成功进入订单列表页面\")\n    \n        # 切换到全部订单tab\n        alltab = p.get_by_test_id(mweb_order_list_ele.order_all_tab_ele)\n        assert alltab.is_visible(), \"未找到全部订单tab\"\n        # 切换到全部订单 tab\n        alltab.click()\n        p.wait_for_timeout(2000)\n        # 验证已切换到全部订单 tab\n        assert \"/order/list?filter_status=all\" in p.url, \"未切换到全部订单tab\"\n        # 2. 检查是否存在订单\n        empty_state = p.get_by_test_id(mweb_order_list_ele.empty_image_ele)\n        if empty_state.is_visible():\n            # 全部订单tab下没有订单\n            # 2. 如果没有订单，点击start shopping按钮\n            start_shopping_btn = p.get_by_test_id(mweb_order_list_ele.start_shopping_btn_ele)\n            assert start_shopping_btn.is_visible(), \"未找到start shopping按钮\"\n            assert p.get_by_test_id(mweb_order_list_ele.empty_title_ele)\n            start_shopping_btn.click()\n            p.wait_for_timeout(3000)\n            # 判断进入首页\n            assert p.get_by_test_id(\"wid-view-more\").is_visible(), \"未成功跳转到首页\"\n        else:\n            # 获取已取消tab下的normal订单\n>           order_R_items = p.get_by_test_id(mweb_order_list_ele.order_list_R_card_ele).all()\n\ntest_108218_mWeb_my_all_order_ui_ux.py:55: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py:17056: in all\n    return mapping.from_impl_list(self._sync(self._impl_obj.all()))\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_locator.py:384: in all\n    for index in range(await self.count()):\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_locator.py:391: in count\n    return await self._frame._query_count(self._selector)\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_frame.py:122: in _query_count\n    return await self._channel.send(\"queryCount\", {\"selector\": selector})\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:62: in send\n    return await self._connection.wrap_api_call(\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:492: in wrap_api_call\n    return await cb()\n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n\nself = <playwright._impl._connection.Channel object at 0x00000267A7EA6010>\nmethod = 'queryCount'\nparams = {'selector': 'internal:testid=[data-testid=\"wid-order-list-order-card-R-normal-0\"s]'}\nreturn_as_dict = False\n\n    async def inner_send(\n        self, method: str, params: Optional[Dict], return_as_dict: bool\n    ) -> Any:\n        if params is None:\n            params = {}\n        callback = self._connection._send_message_to_server(\n            self._object, method, params\n        )\n        if self._connection._error:\n            error = self._connection._error\n            self._connection._error = None\n            raise error\n        done, _ = await asyncio.wait(\n            {\n                self._connection._transport.on_error_future,\n                callback.future,\n            },\n            return_when=asyncio.FIRST_COMPLETED,\n        )\n        if not callback.future.done():\n            callback.future.cancel()\n>       result = next(iter(done)).result()\nE       playwright._impl._errors.Error: Execution context was destroyed, most likely because of a navigation\n\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:100: Error"}, "description": "\n        【108218】 订单列表All tab-列表流程验证\n        测试步骤：\n        1、进入订单列表页面，点击切换到已取消tab下\n        2、如果存在订单，订单上会存在各种按钮\n        3、点击 退款详情 按钮，进入售后退款页面，再返回已送达页面\n        5、点击reorder 按钮 ，拉起再来一单选择商品页面，再返回已送达页面\n        ", "start": *************, "stop": *************, "uuid": "64a551f4-0f31-400a-b24f-8deff1070ea3", "historyId": "b865d3d049779ec4b9e2b5d776beef37", "testCaseId": "b865d3d049779ec4b9e2b5d776beef37", "fullName": "src.Mweb.EC.testcases.mweb_account.mweb_order.test_108218_mWeb_my_all_order_ui_ux.TestMWebMyCanceledOrderUIUX#test_108218_mWeb_my_all_order_ui_ux", "labels": [{"name": "story", "value": "【108218】 订单列表All tab-列表流程验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "h5order"}, {"name": "tag", "value": "mweb_regression"}, {"name": "tag", "value": "transaction"}, {"name": "tag", "value": "suqin"}, {"name": "parentSuite", "value": "src.Mweb.EC.testcases.mweb_account.mweb_order"}, {"name": "suite", "value": "test_108218_mWeb_my_all_order_ui_ux"}, {"name": "subSuite", "value": "TestMWebMyCanceledOrderUIUX"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "15760-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Mweb.EC.testcases.mweb_account.mweb_order.test_108218_mWeb_my_all_order_ui_ux"}]}
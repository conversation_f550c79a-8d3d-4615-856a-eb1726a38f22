{"name": "【102678】 Mobile-Account 页面-我的订单模块UI/UX验证", "status": "failed", "statusDetails": {"message": "AssertionError: 未找到待发货tab\nassert False\n +  where False = <bound method Locator.is_visible of <Locator frame=<Frame name= url='https://www.sayweee.com/en/order/list?filter_status=1'> selector=\"//a[contains(@href,'filter_status=2')]\">>()\n +    where <bound method Locator.is_visible of <Locator frame=<Frame name= url='https://www.sayweee.com/en/order/list?filter_status=1'> selector=\"//a[contains(@href,'filter_status=2')]\">> = <Locator frame=<Frame name= url='https://www.sayweee.com/en/order/list?filter_status=1'> selector=\"//a[contains(@href,'filter_status=2')]\">.is_visible", "trace": "self = <src.Mweb.EC.testcases.mweb_account.mweb_order.test_102678_mWeb_account_my_order_ui_ux.TestMWebMyCanceledOrderUIUX object at 0x000001EF5FAF1E90>\nphone_page = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppDat...me-win\\chrome.exe> version=120.0.6099.28>>, 'page': <Page url='https://www.sayweee.com/en/order/list?filter_status=1'>}\nh5_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...jfSOWAb-JqagxGYyWqA00LauihSh90akcs0mJ3T2ckCGLHf0j80hbMydbB-oXvhGDIIKSUX5qd2_PbITAtxbbx6wag_dTcmnUTFgoWQEdZpUTDJs', ...}\nh5_open_and_close_trace = None\n\n    @allure.title(\"【102678】 Mobile-Account 页面-我的订单模块UI/UX验证\")\n    def test_102678_mWeb_account_my_order_ui_ux(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):\n        \"\"\"\n        【102678】 Mobile-Account 页面-我的订单模块UI/UX验证\n        测试步骤：\n        1、me页面的我的订单模块下面展示:待付款、待发货、已发货、待晒单、退换/售后\n        2、点击我的订单>,进入订单列表:默认选中“全部订单”tab\n        3、点击待付款:进入我的订单页面，待付款tab下\n        4、点击待发货:进入我的订单页面，待发货tab下\n        5、点击已发货：进入我的订单页面，已发货tab下\n        6、点击待晒单：进入我的订单页面，待晒单tab下\n        7、点击退换/售后：进入我的订单页面，退换/售后tab下\n    \n        \"\"\"\n        p: Page = phone_page.get(\"page\")\n        c = phone_page.get(\"context\")\n    \n        # 1. 进入订单列表页面\n        order_page = MWebOrderPage(p, h5_autotest_header, browser_context=c, page_url=\"/account\")\n        p.wait_for_timeout(3000)\n        log.info(\"成功进入订单列表页面\")\n        # 3. 滚动到我的订单模块\n        order_page.scroll_to_pos(mweb_account_ele.order_pending_tab_ele)\n        log.info(\"滚动到我的订单模块\")\n        p.wait_for_timeout(2000)\n    \n    \n        # # 我的订单> 入口\n        # my_orders = p.locator(mweb_account_ele.my_orders_ele)\n        # assert my_orders.is_visible(), \"未找到我的订单> 入口\"\n        # # 切换到全部订单tab\n        # my_orders.click()\n        # p.wait_for_timeout(2000)\n        # # 验证已切换到全部订单tab\n        # print(p.url)\n        # assert \"/order/list\" in p.url, \"未切换到全部订单tab\"\n    \n        # 待付款tab\n        pending_tab = p.locator(mweb_account_ele.order_pending_tab_ele)\n        assert pending_tab.is_visible(), \"未找到待付款tab\"\n        # 切换到待付款tab\n        pending_tab.click()\n        p.wait_for_timeout(2000)\n        # 验证已切换到待付款tab\n        print(p.url)\n        assert \"order/list\" in p.url and \"filter_status=1\" in p.url, \"未切换到待付款tab\"\n    \n        # assert \"/order/list?filter_status=1\" in p.url, \"未切换到待付款tab\"\n    \n        # 切换到待发货tab\n        unshipped_tab = p.locator(mweb_account_ele.order_unshipped_tab_ele)\n>       assert unshipped_tab.is_visible(), \"未找到待发货tab\"\nE       AssertionError: 未找到待发货tab\nE       assert False\nE        +  where False = <bound method Locator.is_visible of <Locator frame=<Frame name= url='https://www.sayweee.com/en/order/list?filter_status=1'> selector=\"//a[contains(@href,'filter_status=2')]\">>()\nE        +    where <bound method Locator.is_visible of <Locator frame=<Frame name= url='https://www.sayweee.com/en/order/list?filter_status=1'> selector=\"//a[contains(@href,'filter_status=2')]\">> = <Locator frame=<Frame name= url='https://www.sayweee.com/en/order/list?filter_status=1'> selector=\"//a[contains(@href,'filter_status=2')]\">.is_visible\n\ntest_102678_mWeb_account_my_order_ui_ux.py:65: AssertionError"}, "description": "\n        【102678】 Mobile-Account 页面-我的订单模块UI/UX验证\n        测试步骤：\n        1、me页面的我的订单模块下面展示:待付款、待发货、已发货、待晒单、退换/售后\n        2、点击我的订单>,进入订单列表:默认选中“全部订单”tab\n        3、点击待付款:进入我的订单页面，待付款tab下\n        4、点击待发货:进入我的订单页面，待发货tab下\n        5、点击已发货：进入我的订单页面，已发货tab下\n        6、点击待晒单：进入我的订单页面，待晒单tab下\n        7、点击退换/售后：进入我的订单页面，退换/售后tab下\n\n        ", "start": *************, "stop": *************, "uuid": "4d0f04d1-fe5f-4231-8f8a-7cfe9024e8d4", "historyId": "f112e5f34e3de78d13b585355752f098", "testCaseId": "f112e5f34e3de78d13b585355752f098", "fullName": "src.Mweb.EC.testcases.mweb_account.mweb_order.test_102678_mWeb_account_my_order_ui_ux.TestMWebMyCanceledOrderUIUX#test_102678_mWeb_account_my_order_ui_ux", "labels": [{"name": "story", "value": "【102678】 Mobile-Account 页面-我的订单模块UI/UX验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "h5order"}, {"name": "tag", "value": "mweb_todo"}, {"name": "tag", "value": "transaction"}, {"name": "tag", "value": "suqin"}, {"name": "parentSuite", "value": "src.Mweb.EC.testcases.mweb_account.mweb_order"}, {"name": "suite", "value": "test_102678_mWeb_account_my_order_ui_ux"}, {"name": "subSuite", "value": "TestMWebMyCanceledOrderUIUX"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "2072-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Mweb.EC.testcases.mweb_account.mweb_order.test_102678_mWeb_account_my_order_ui_ux"}]}
{"name": "【108218】 订单列表All tab-列表流程验证", "status": "failed", "statusDetails": {"message": "AssertionError: 第1个生鲜订单卡片按钮验证失败\nassert None\n +  where None = <bound method MWebOrderPage.order_card_btn of <src.Mweb.EC.mweb_pages.mweb_account_page.mweb_order_page.mweb_order_page.MWebOrderPage object at 0x000002036F4120D0>>(<Locator frame=<Frame name= url='https://www.sayweee.com/en/order/list?filter_status=all'> selector='internal:testid=[data-testid=\"wid-order-list-order-card-R-normal-0\"s] >> nth=0'>)\n +    where <bound method MWebOrderPage.order_card_btn of <src.Mweb.EC.mweb_pages.mweb_account_page.mweb_order_page.mweb_order_page.MWebOrderPage object at 0x000002036F4120D0>> = <src.Mweb.EC.mweb_pages.mweb_account_page.mweb_order_page.mweb_order_page.MWebOrderPage object at 0x000002036F4120D0>.order_card_btn", "trace": "self = <src.Mweb.EC.testcases.mweb_account.mweb_order.test_108218_mWeb_my_all_order_ui_ux.TestMWebMyCanceledOrderUIUX object at 0x000002036BCDA550>\nphone_page = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppDat...-win\\chrome.exe> version=120.0.6099.28>>, 'page': <Page url='https://www.sayweee.com/en/order/list?filter_status=all'>}\nh5_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...yGQXIPnTYQRn1YEUS0Q8eRrBshWpBMRu9kV20OKKNJU_66MKLEQ8oE6jqG55-JDDCuXzNB4w0uaSROBRCMj-YYA8BdrQLdH8eBMfI1e3702g0S48', ...}\nh5_open_and_close_trace = None\n\n    @allure.title(\"【108218】 订单列表All tab-列表流程验证\")\n    def test_108218_mWeb_my_all_order_ui_ux(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):\n        \"\"\"\n        【108218】 订单列表All tab-列表流程验证\n        测试步骤：\n        1、进入订单列表页面，点击切换到已取消tab下\n        2、如果存在订单，订单上会存在各种按钮\n        3、点击 退款详情 按钮，进入售后退款页面，再返回已送达页面\n        5、点击reorder 按钮 ，拉起再来一单选择商品页面，再返回已送达页面\n        \"\"\"\n        p: Page = phone_page.get(\"page\")\n        c = phone_page.get(\"context\")\n    \n        # 1. 进入订单列表页面\n        order_page = MWebOrderPage(p, h5_autotest_header, browser_context=c, page_url=\"/order/list\")\n        p.wait_for_timeout(3000)\n        log.info(\"成功进入订单列表页面\")\n    \n        # 切换到全部订单tab\n        alltab = p.get_by_test_id(mweb_order_list_ele.order_all_tab_ele)\n        assert alltab.is_visible(), \"未找到全部订单tab\"\n        # 切换到全部订单 tab\n        alltab.click()\n        p.wait_for_timeout(2000)\n        # 验证已切换到全部订单 tab\n        assert \"/order/list?filter_status=all\" in p.url, \"未切换到全部订单tab\"\n        # 2. 检查是否存在订单\n        empty_state = p.get_by_test_id(mweb_order_list_ele.empty_image_ele)\n        if empty_state.is_visible():\n            # 全部订单tab下没有订单\n            # 2. 如果没有订单，点击start shopping按钮\n            start_shopping_btn = p.get_by_test_id(mweb_order_list_ele.start_shopping_btn_ele)\n            assert start_shopping_btn.is_visible(), \"未找到start shopping按钮\"\n            assert p.get_by_test_id(mweb_order_list_ele.empty_title_ele)\n            start_shopping_btn.click()\n            p.wait_for_timeout(3000)\n            # 判断进入首页\n            assert p.get_by_test_id(\"wid-view-more\").is_visible(), \"未成功跳转到首页\"\n        else:\n            # 获取已取消tab下的normal订单\n            order_R_items = p.get_by_test_id(mweb_order_list_ele.order_list_R_card_ele).all()\n            order_S_items = p.get_by_test_id(mweb_order_list_ele.order_list_S_card_ele).all()\n            order_P_items = p.get_by_test_id(mweb_order_list_ele.order_list_P_card_ele).all()\n            order_G_items = p.get_by_test_id(mweb_order_list_ele.order_list_G_card_ele).all()\n    \n            if len(order_R_items) == 0 and len(order_S_items) == 0:\n                log.warning(\"已发货tab下没有normal+seller 订单，无法继续测试\")\n                pytest.skip(\"已发货tab下没有normal+seller 订单，跳过测试\")\n            elif len(order_R_items) > 0:\n                for index, item_R in enumerate(order_R_items):\n                    # 使用公共断言方法验证seller订单卡片信息\n                    assert order_page.assert_order_tab_info(item_R, \"all\")\n                    assert order_page.assert_order_card_info(item_R,\"R\"), f\"第{index + 1}个生鲜订单卡片信息验证失败\"\n>                   assert order_page.order_card_btn(item_R), f\"第{index + 1}个生鲜订单卡片按钮验证失败\"\nE                   AssertionError: 第1个生鲜订单卡片按钮验证失败\nE                   assert None\nE                    +  where None = <bound method MWebOrderPage.order_card_btn of <src.Mweb.EC.mweb_pages.mweb_account_page.mweb_order_page.mweb_order_page.MWebOrderPage object at 0x000002036F4120D0>>(<Locator frame=<Frame name= url='https://www.sayweee.com/en/order/list?filter_status=all'> selector='internal:testid=[data-testid=\"wid-order-list-order-card-R-normal-0\"s] >> nth=0'>)\nE                    +    where <bound method MWebOrderPage.order_card_btn of <src.Mweb.EC.mweb_pages.mweb_account_page.mweb_order_page.mweb_order_page.MWebOrderPage object at 0x000002036F4120D0>> = <src.Mweb.EC.mweb_pages.mweb_account_page.mweb_order_page.mweb_order_page.MWebOrderPage object at 0x000002036F4120D0>.order_card_btn\n\ntest_108218_mWeb_my_all_order_ui_ux.py:68: AssertionError"}, "description": "\n        【108218】 订单列表All tab-列表流程验证\n        测试步骤：\n        1、进入订单列表页面，点击切换到已取消tab下\n        2、如果存在订单，订单上会存在各种按钮\n        3、点击 退款详情 按钮，进入售后退款页面，再返回已送达页面\n        5、点击reorder 按钮 ，拉起再来一单选择商品页面，再返回已送达页面\n        ", "start": *************, "stop": *************, "uuid": "f787d2b1-f74f-4438-b1fe-8872ab75a328", "historyId": "b865d3d049779ec4b9e2b5d776beef37", "testCaseId": "b865d3d049779ec4b9e2b5d776beef37", "fullName": "src.Mweb.EC.testcases.mweb_account.mweb_order.test_108218_mWeb_my_all_order_ui_ux.TestMWebMyCanceledOrderUIUX#test_108218_mWeb_my_all_order_ui_ux", "labels": [{"name": "story", "value": "【108218】 订单列表All tab-列表流程验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "h5order"}, {"name": "tag", "value": "mweb_regression"}, {"name": "tag", "value": "transaction"}, {"name": "tag", "value": "suqin"}, {"name": "parentSuite", "value": "src.Mweb.EC.testcases.mweb_account.mweb_order"}, {"name": "suite", "value": "test_108218_mWeb_my_all_order_ui_ux"}, {"name": "subSuite", "value": "TestMWebMyCanceledOrderUIUX"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "29164-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Mweb.EC.testcases.mweb_account.mweb_order.test_108218_mWeb_my_all_order_ui_ux"}]}
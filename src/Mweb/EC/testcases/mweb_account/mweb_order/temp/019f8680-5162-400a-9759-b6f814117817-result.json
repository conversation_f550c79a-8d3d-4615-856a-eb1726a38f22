{"name": "【108616】订单列表已取消tab-再来一单流程验证", "status": "broken", "statusDetails": {"message": "playwright._impl._errors.Error: DOMException: Failed to execute 'evaluate' on 'Document': The string '//div[@id='order-buy-again']/' is not a valid XPath expression.\n    at Object.queryAll (<anonymous>:41:25)\n    at InjectedScript._queryEngineAll (<anonymous>:4859:49)\n    at InjectedScript.querySelectorAll (<anonymous>:4846:30)\n    at InjectedScript.querySelector (<anonymous>:4785:25)\n    at eval (eval at evaluate (:226:30), <anonymous>:5:34)\n    at UtilityScript.evaluate (<anonymous>:228:17)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)", "trace": "self = <src.Mweb.EC.testcases.mweb_account.mweb_order.test_108616_mWeb_my_cancel_order_buy_again_ui_ux.TestMWebMyCancelOrderBuyAgainUIUX object at 0x000002AA49095F50>\nphone_page = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppDat...me-win\\chrome.exe> version=120.0.6099.28>>, 'page': <Page url='https://www.sayweee.com/en/order/list?filter_status=4'>}\nh5_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...1-pGmrpgBSI23mspEHNeQ6McNojWW0I9vpCSJjxtbfkJt2E4wLMkgfK4IwoemuL7zJupX21MQF8J3uhO5dIx6fGjGX85hBWSp8EqZncSNe1hi0sM', ...}\nh5_open_and_close_trace = None\n\n    @allure.title(\"【108616】订单列表已取消tab-再来一单流程验证\")\n    def test_111625_mWeb_my_cancel_order_buy_again_ui_ux(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):\n        \"\"\"\n        【108616】订单列表已取消tab-再来一单流程验证\n        测试步骤：\n        1、进入订单列表页面，切换到已取消tab下\n        2、点击全部订单，找到订单列表下再来一单按钮，进入商品选择页面\n        3、商品选择页面，默认勾选全选按钮，勾选掉某个商品，全选勾掉，点击全选商品全部选中，这里如果选中几个商品，加入购物车按钮上就会显示几个\n        4、点击加入购物车按钮，自动回到订单列表页面，并弹出toast，点击查看，进入购物车页面\n         第3、4 会在 再来一单case 里实现\n        \"\"\"\n        p: Page = phone_page.get(\"page\")\n        c = phone_page.get(\"context\")\n    \n        # 1. 进入订单列表页面\n        order_page = MWebOrderPage(p, h5_autotest_header, browser_context=c, page_url=\"/order/list\")\n        p.wait_for_timeout(3000)\n        log.info(\"成功进入订单列表页面\")\n    \n        # 切换到已取消tab\n        cancelled_tab = p.get_by_test_id(mweb_order_list_ele.order_cancelled_tab_ele)\n        assert cancelled_tab.is_visible(), \"未找到已取消tab\"\n        # 切换到已取消 tab\n        cancelled_tab.click()\n        p.wait_for_timeout(2000)\n        # 验证已切换到已取消 tab\n        assert \"/order/list?filter_status=4\" in p.url, \"未切换到已取消tab\"\n        # 2. 检查是否存在订单\n        empty_state = p.get_by_test_id(mweb_order_list_ele.empty_image_ele)\n        if empty_state.is_visible():\n            # 已取消tab下没有订单\n            # 2. 如果没有订单，点击start shopping按钮\n            start_shopping_btn = p.get_by_test_id(mweb_order_list_ele.start_shopping_btn_ele)\n            assert start_shopping_btn.is_visible(), \"未找到start shopping按钮\"\n            assert p.get_by_test_id(mweb_order_list_ele.empty_title_ele)\n            start_shopping_btn.click()\n            p.wait_for_timeout(3000)\n            # 判断进入首页\n            assert p.get_by_test_id(\"wid-view-more\").is_visible(), \"未成功跳转到首页\"\n        else:\n            # 获取订单列表上buy order 按钮\n            buy_order_btns = p.get_by_test_id(\"wid-order-btn-buy_again\").all()\n            if len(buy_order_btns) == 0:\n                log.warning(\"已取消tab下没有订单，无法继续测试\")\n                pytest.skip(\"已取消tab下没有订单，跳过测试\")\n            else:\n                for index, item in enumerate(buy_order_btns):\n                    # 点击再来一单按钮\n                    item.click()\n                    p.wait_for_timeout(3000)\n>                   p.locator(u\"//div[@id='order-buy-again']/\").is_visible()\n\ntest_108616_mWeb_my_cancel_order_buy_again_ui_ux.py:66: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py:17546: in is_visible\n    self._sync(self._impl_obj.is_visible(timeout=timeout))\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_locator.py:499: in is_visible\n    return await self._frame.is_visible(\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_frame.py:348: in is_visible\n    return await self._channel.send(\"isVisible\", locals_to_params(locals()))\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:62: in send\n    return await self._connection.wrap_api_call(\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:492: in wrap_api_call\n    return await cb()\n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n\nself = <playwright._impl._connection.Channel object at 0x000002AA494C8E10>\nmethod = 'isVisible'\nparams = {'selector': \"//div[@id='order-buy-again']/\", 'strict': True}\nreturn_as_dict = False\n\n    async def inner_send(\n        self, method: str, params: Optional[Dict], return_as_dict: bool\n    ) -> Any:\n        if params is None:\n            params = {}\n        callback = self._connection._send_message_to_server(\n            self._object, method, params\n        )\n        if self._connection._error:\n            error = self._connection._error\n            self._connection._error = None\n            raise error\n        done, _ = await asyncio.wait(\n            {\n                self._connection._transport.on_error_future,\n                callback.future,\n            },\n            return_when=asyncio.FIRST_COMPLETED,\n        )\n        if not callback.future.done():\n            callback.future.cancel()\n>       result = next(iter(done)).result()\nE       playwright._impl._errors.Error: DOMException: Failed to execute 'evaluate' on 'Document': The string '//div[@id='order-buy-again']/' is not a valid XPath expression.\nE           at Object.queryAll (<anonymous>:41:25)\nE           at InjectedScript._queryEngineAll (<anonymous>:4859:49)\nE           at InjectedScript.querySelectorAll (<anonymous>:4846:30)\nE           at InjectedScript.querySelector (<anonymous>:4785:25)\nE           at eval (eval at evaluate (:226:30), <anonymous>:5:34)\nE           at UtilityScript.evaluate (<anonymous>:228:17)\nE           at UtilityScript.<anonymous> (<anonymous>:1:44)\n\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:100: Error"}, "description": "\n        【108616】订单列表已取消tab-再来一单流程验证\n        测试步骤：\n        1、进入订单列表页面，切换到已取消tab下\n        2、点击全部订单，找到订单列表下再来一单按钮，进入商品选择页面\n        3、商品选择页面，默认勾选全选按钮，勾选掉某个商品，全选勾掉，点击全选商品全部选中，这里如果选中几个商品，加入购物车按钮上就会显示几个\n        4、点击加入购物车按钮，自动回到订单列表页面，并弹出toast，点击查看，进入购物车页面\n         第3、4 会在 再来一单case 里实现\n        ", "start": *************, "stop": *************, "uuid": "33623b8d-8e91-4380-a635-f9dc16d80033", "historyId": "aa140f8ad200c20231c8b0bf30e3cc34", "testCaseId": "aa140f8ad200c20231c8b0bf30e3cc34", "fullName": "src.Mweb.EC.testcases.mweb_account.mweb_order.test_108616_mWeb_my_cancel_order_buy_again_ui_ux.TestMWebMyCancelOrderBuyAgainUIUX#test_111625_mWeb_my_cancel_order_buy_again_ui_ux", "labels": [{"name": "story", "value": "【108616】订单列表已取消tab-再来一单流程验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "h5order"}, {"name": "tag", "value": "mweb_todo"}, {"name": "tag", "value": "transaction"}, {"name": "tag", "value": "suqin"}, {"name": "parentSuite", "value": "src.Mweb.EC.testcases.mweb_account.mweb_order"}, {"name": "suite", "value": "test_108616_mWeb_my_cancel_order_buy_again_ui_ux"}, {"name": "subSuite", "value": "TestMWebMyCancelOrderBuyAgainUIUX"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "14472-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Mweb.EC.testcases.mweb_account.mweb_order.test_108616_mWeb_my_cancel_order_buy_again_ui_ux"}]}
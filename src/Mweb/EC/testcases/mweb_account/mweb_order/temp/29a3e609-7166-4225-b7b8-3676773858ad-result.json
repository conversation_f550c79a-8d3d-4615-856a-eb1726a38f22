{"name": "【102678】 Mobile-Account 页面-我的订单模块UI/UX验证", "status": "failed", "statusDetails": {"message": "AssertionError: assert False\n +  where False = <bound method Locator.is_visible of <Locator frame=<Frame name= url='https://www.sayweee.com/en/account?joinEnki=true'> selector='internal:testid=[data-testid=\"wid-order-unshipped-title  \"s]'>>()\n +    where <bound method Locator.is_visible of <Locator frame=<Frame name= url='https://www.sayweee.com/en/account?joinEnki=true'> selector='internal:testid=[data-testid=\"wid-order-unshipped-title  \"s]'>> = <Locator frame=<Frame name= url='https://www.sayweee.com/en/account?joinEnki=true'> selector='internal:testid=[data-testid=\"wid-order-unshipped-title  \"s]'>.is_visible\n +      where <Locator frame=<Frame name= url='https://www.sayweee.com/en/account?joinEnki=true'> selector='internal:testid=[data-testid=\"wid-order-unshipped-title  \"s]'> = <bound method Page.get_by_test_id of <Page url='https://www.sayweee.com/en/account?joinEnki=true'>>('wid-order-unshipped-title  ')\n +        where <bound method Page.get_by_test_id of <Page url='https://www.sayweee.com/en/account?joinEnki=true'>> = <Page url='https://www.sayweee.com/en/account?joinEnki=true'>.get_by_test_id", "trace": "self = <src.Mweb.EC.testcases.mweb_account.mweb_order.test_102678_mWeb_account_my_order_ui_ux.TestMWebMyCanceledOrderUIUX object at 0x000002ACFA781250>\nphone_page = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppDat...\\chrome-win\\chrome.exe> version=120.0.6099.28>>, 'page': <Page url='https://www.sayweee.com/en/account?joinEnki=true'>}\nh5_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...C-hR5omLQdYRFHbyKT5TpJdcALAxIbjRw1zzS-2M2t6v_gtzQcKyR8HwggVX3oD3chY1AwswVdm74q0hAEKQD8fkk5u3v-m0vqec6flPIQeS_rPs', ...}\nh5_open_and_close_trace = None\n\n    @allure.title(\"【102678】 Mobile-Account 页面-我的订单模块UI/UX验证\")\n    def test_102678_mWeb_account_my_order_ui_ux(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):\n        \"\"\"\n        【102678】 Mobile-Account 页面-我的订单模块UI/UX验证\n        测试步骤：\n        1、me页面的我的订单模块下面展示:待付款、待发货、已发货、待晒单、退换/售后\n        2、点击我的订单>,进入订单列表:默认选中“全部订单”tab\n        3、点击待付款:进入我的订单页面，待付款tab下\n        4、点击待发货:进入我的订单页面，待发货tab下\n        5、点击已发货：进入我的订单页面，已发货tab下\n        6、点击待晒单：进入我的订单页面，待晒单tab下\n        7、点击退换/售后：进入我的订单页面，退换/售后tab下\n    \n        \"\"\"\n        p: Page = phone_page.get(\"page\")\n        c = phone_page.get(\"context\")\n    \n        # 1. 进入me页面，验证我的订单模块展示\n        log.info(\"步骤1：进入me页面，验证我的订单模块展示\")\n        account_page = MWebOrderPage(p, h5_autotest_header, browser_context=c, page_url=\"/account\")\n        p.wait_for_timeout(3000)\n        log.info(\"成功进入订单列表页面\")\n        # 3. 滚动到我的订单模块\n        account_page.scroll_to_pos(mweb_account_ele.my_orders_ele)\n        log.info(\"滚动到我的订单模块\")\n        p.wait_for_timeout(2000)\n        # 断言 > 存在\n        assert p.get_by_test_id(\"wid-order-arrow-right\").is_visible()\n        # 断言 我的订单里 icon、title 都存在\n        assert p.get_by_test_id(\"wid-order-pending-image\").is_visible()\n        assert p.get_by_test_id(\"wid-order-pending-title\").is_visible()\n        # 断言如果存在数字球，大于99 显示99+\n        if (p.get_by_test_id(\"wid-order-pending-value\").is_visible()\n                and int(p.get_by_test_id(\"wid-order-pending-value\").text_content()) > 99):\n            assert p.get_by_test_id(\"wid-order-pending-value\").text_content() == \"99+\"\n    \n        assert p.get_by_test_id(\"wid-order-unshipped-image\").is_visible()\n>       assert p.get_by_test_id(\"wid-order-unshipped-title  \").is_visible()\nE       assert False\nE        +  where False = <bound method Locator.is_visible of <Locator frame=<Frame name= url='https://www.sayweee.com/en/account?joinEnki=true'> selector='internal:testid=[data-testid=\"wid-order-unshipped-title  \"s]'>>()\nE        +    where <bound method Locator.is_visible of <Locator frame=<Frame name= url='https://www.sayweee.com/en/account?joinEnki=true'> selector='internal:testid=[data-testid=\"wid-order-unshipped-title  \"s]'>> = <Locator frame=<Frame name= url='https://www.sayweee.com/en/account?joinEnki=true'> selector='internal:testid=[data-testid=\"wid-order-unshipped-title  \"s]'>.is_visible\nE        +      where <Locator frame=<Frame name= url='https://www.sayweee.com/en/account?joinEnki=true'> selector='internal:testid=[data-testid=\"wid-order-unshipped-title  \"s]'> = <bound method Page.get_by_test_id of <Page url='https://www.sayweee.com/en/account?joinEnki=true'>>('wid-order-unshipped-title  ')\nE        +        where <bound method Page.get_by_test_id of <Page url='https://www.sayweee.com/en/account?joinEnki=true'>> = <Page url='https://www.sayweee.com/en/account?joinEnki=true'>.get_by_test_id\n\ntest_102678_mWeb_account_my_order_ui_ux.py:51: AssertionError"}, "description": "\n        【102678】 Mobile-Account 页面-我的订单模块UI/UX验证\n        测试步骤：\n        1、me页面的我的订单模块下面展示:待付款、待发货、已发货、待晒单、退换/售后\n        2、点击我的订单>,进入订单列表:默认选中“全部订单”tab\n        3、点击待付款:进入我的订单页面，待付款tab下\n        4、点击待发货:进入我的订单页面，待发货tab下\n        5、点击已发货：进入我的订单页面，已发货tab下\n        6、点击待晒单：进入我的订单页面，待晒单tab下\n        7、点击退换/售后：进入我的订单页面，退换/售后tab下\n\n        ", "start": *************, "stop": *************, "uuid": "040138a0-4921-450a-9c9e-6f2e921f2192", "historyId": "f112e5f34e3de78d13b585355752f098", "testCaseId": "f112e5f34e3de78d13b585355752f098", "fullName": "src.Mweb.EC.testcases.mweb_account.mweb_order.test_102678_mWeb_account_my_order_ui_ux.TestMWebMyCanceledOrderUIUX#test_102678_mWeb_account_my_order_ui_ux", "labels": [{"name": "story", "value": "【102678】 Mobile-Account 页面-我的订单模块UI/UX验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "h5order"}, {"name": "tag", "value": "mweb_regression"}, {"name": "tag", "value": "transaction"}, {"name": "tag", "value": "suqin"}, {"name": "parentSuite", "value": "src.Mweb.EC.testcases.mweb_account.mweb_order"}, {"name": "suite", "value": "test_102678_mWeb_account_my_order_ui_ux"}, {"name": "subSuite", "value": "TestMWebMyCanceledOrderUIUX"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "6976-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Mweb.EC.testcases.mweb_account.mweb_order.test_102678_mWeb_account_my_order_ui_ux"}]}
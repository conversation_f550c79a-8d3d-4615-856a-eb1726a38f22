{"name": "【108218】 订单列表All tab-列表流程验证", "status": "skipped", "statusDetails": {"message": "Skipped: 已发货tab下没有normal+seller 订单，跳过测试", "trace": "('D:\\\\MyWork\\\\Python\\\\EC-demo\\\\qa-ui-dmweb\\\\src\\\\Mweb\\\\EC\\\\testcases\\\\mweb_account\\\\mweb_order\\\\test_108218_mWeb_my_all_order_ui_ux.py', 63, 'Skipped: 已发货tab下没有normal+seller 订单，跳过测试')"}, "description": "\n        【108218】 订单列表All tab-列表流程验证\n        测试步骤：\n        1、进入订单列表页面，点击切换到已取消tab下\n        2、如果存在订单，订单上会存在各种按钮\n        3、点击 退款详情 按钮，进入售后退款页面，再返回已送达页面\n        5、点击reorder 按钮 ，拉起再来一单选择商品页面，再返回已送达页面\n        ", "start": *************, "stop": *************, "uuid": "ff8695e2-b98b-4687-ac80-44ae93e76552", "historyId": "b865d3d049779ec4b9e2b5d776beef37", "testCaseId": "b865d3d049779ec4b9e2b5d776beef37", "fullName": "src.Mweb.EC.testcases.mweb_account.mweb_order.test_108218_mWeb_my_all_order_ui_ux.TestMWebMyCanceledOrderUIUX#test_108218_mWeb_my_all_order_ui_ux", "labels": [{"name": "story", "value": "【108218】 订单列表All tab-列表流程验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "h5order"}, {"name": "tag", "value": "mweb_todo"}, {"name": "tag", "value": "transaction"}, {"name": "tag", "value": "suqin"}, {"name": "parentSuite", "value": "src.Mweb.EC.testcases.mweb_account.mweb_order"}, {"name": "suite", "value": "test_108218_mWeb_my_all_order_ui_ux"}, {"name": "subSuite", "value": "TestMWebMyCanceledOrderUIUX"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "35788-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Mweb.EC.testcases.mweb_account.mweb_order.test_108218_mWeb_my_all_order_ui_ux"}]}
{"name": "【108219】 订单列表已取消tab-订单流程验证", "status": "failed", "statusDetails": {"message": "AssertionError: 订单内容信息不存在", "trace": "self = <src.Mweb.EC.testcases.mweb_account.mweb_order.test_108219_mWeb_my_canceled_order_ui_ux.TestMWebMyCanceledOrderUIUX object at 0x000002AE6D0EA450>\nphone_page = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppDat...me-win\\chrome.exe> version=120.0.6099.28>>, 'page': <Page url='https://www.sayweee.com/en/order/list?filter_status=4'>}\nh5_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...YlAFSybT5HT0WZinNBn44eUN8Sq8VDIOkGaVITBIBl4S7uaa6Gx2eonnhibM78eXtzqVX6yuRAl4SE_bA99_rPubT9PabrZFzEugVD-34E-0ctIQ', ...}\nh5_open_and_close_trace = None\n\n    @allure.title(\"【108219】 订单列表已取消tab-订单流程验证\")\n    def test_108219_mWeb_my_canceled_order_ui_ux(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):\n        \"\"\"\n        【108219】 订单列表已取消tab-订单流程验证\n        测试步骤：\n        1、进入订单列表页面，点击切换到已取消tab下\n        2、如果存在订单，订单上会存在各种按钮\n        3、点击 退款详情 按钮，进入售后退款页面，再返回已送达页面\n        5、点击reorder 按钮 ，拉起再来一单选择商品页面，再返回已送达页面\n        \"\"\"\n        p: Page = phone_page.get(\"page\")\n        c = phone_page.get(\"context\")\n    \n        # 1. 进入订单列表页面\n        order_page = MWebOrderPage(p, h5_autotest_header, browser_context=c, page_url=\"/order/list\")\n        p.wait_for_timeout(3000)\n        log.info(\"成功进入订单列表页面\")\n    \n        # 切换到已取消tab\n        cancelled_tab = p.get_by_test_id(mweb_order_list_ele.order_cancelled_tab_ele)\n        assert cancelled_tab.is_visible(), \"未找到已取消tab\"\n        # 切换到已取消 tab\n        cancelled_tab.click()\n        p.wait_for_timeout(2000)\n        # 验证已切换到已取消 tab\n        assert \"/order/list?filter_status=4\" in p.url, \"未切换到已取消tab\"\n        # 2. 检查是否存在订单\n        empty_state = p.get_by_test_id(mweb_order_list_ele.empty_image_ele)\n        if empty_state.is_visible():\n            # 已取消tab下没有订单\n            # 2. 如果没有订单，点击start shopping按钮\n            start_shopping_btn = p.get_by_test_id(mweb_order_list_ele.start_shopping_btn_ele)\n            assert start_shopping_btn.is_visible(), \"未找到start shopping按钮\"\n            assert p.get_by_test_id(mweb_order_list_ele.empty_title_ele)\n            start_shopping_btn.click()\n            p.wait_for_timeout(3000)\n            # 判断进入首页\n            assert p.get_by_test_id(\"wid-view-more\").is_visible(), \"未成功跳转到首页\"\n        else:\n            # 获取订单列表\n            order_R_items = p.get_by_test_id(mweb_order_list_ele.order_list_R_card_ele).all()\n            order_S_items = p.get_by_test_id(mweb_order_list_ele.order_list_S_card_ele).all()\n            order_P_items = p.get_by_test_id(mweb_order_list_ele.order_list_P_card_ele).all()\n            order_G_items = p.get_by_test_id(mweb_order_list_ele.order_list_G_card_ele).all()\n    \n            if len(order_R_items) == 0 and len(order_S_items) == 0:\n                log.warning(\"已发货tab下没有normal+seller 订单，无法继续测试\")\n                pytest.skip(\"已发货tab下没有normal+seller 订单，跳过测试\")\n            elif len(order_R_items) > 0:\n                for index, item_R in enumerate(order_R_items):\n                    # 使用公共断言方法验证seller订单卡片信息\n                    assert order_page.assert_order_tab_info(item_R, \"4\")\n>                   assert order_page.assert_order_card_info(item_R, \"R\"), f\"第{index + 1}个生鲜订单卡片信息验证失败\"\n\ntest_108219_mWeb_my_canceled_order_ui_ux.py:68: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n\nself = <src.Mweb.EC.mweb_pages.mweb_order_page.mweb_order_page.MWebOrderPage object at 0x000002AE707CCD50>\norder_item = <Locator frame=<Frame name= url='https://www.sayweee.com/en/order/list?filter_status=4'> selector='internal:testid=[data-testid=\"wid-order-list-order-card-R-normal-0\"s] >> nth=0'>\norder_type = 'R'\n\n    def assert_order_card_info(self, order_item,order_type):\n        \"\"\"\n        验证seller订单卡片信息的公共断言方法\n        Args:\n            order_item: 订单卡片元素\n        Returns:\n            bool: 验证是否成功\n        \"\"\"\n        # 验证订单内容信息\n        assert order_item.get_by_test_id(mweb_order_list_ele.order_list_card_status_ele).is_visible(), \"订单内容信息不存在\"\n>       assert order_item.get_by_test_id(mweb_order_list_ele.order_list_card_detail_ele).is_visible(), \"订单内容信息不存在\"\nE       AssertionError: 订单内容信息不存在\n\n..\\..\\..\\mweb_pages\\mweb_order_page\\mweb_order_page.py:196: AssertionError"}, "description": "\n        【108219】 订单列表已取消tab-订单流程验证\n        测试步骤：\n        1、进入订单列表页面，点击切换到已取消tab下\n        2、如果存在订单，订单上会存在各种按钮\n        3、点击 退款详情 按钮，进入售后退款页面，再返回已送达页面\n        5、点击reorder 按钮 ，拉起再来一单选择商品页面，再返回已送达页面\n        ", "start": *************, "stop": *************, "uuid": "9964571e-d2aa-4ed7-b013-714da0f7e3d4", "historyId": "30d943dc7b6fbebc06ce1aeaf0d7f843", "testCaseId": "30d943dc7b6fbebc06ce1aeaf0d7f843", "fullName": "src.Mweb.EC.testcases.mweb_account.mweb_order.test_108219_mWeb_my_canceled_order_ui_ux.TestMWebMyCanceledOrderUIUX#test_108219_mWeb_my_canceled_order_ui_ux", "labels": [{"name": "story", "value": "【108219】 订单列表已取消tab-订单流程验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "h5order"}, {"name": "tag", "value": "mweb_todo"}, {"name": "tag", "value": "transaction"}, {"name": "tag", "value": "suqin"}, {"name": "parentSuite", "value": "src.Mweb.EC.testcases.mweb_account.mweb_order"}, {"name": "suite", "value": "test_108219_mWeb_my_canceled_order_ui_ux"}, {"name": "subSuite", "value": "TestMWebMyCanceledOrderUIUX"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "34872-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Mweb.EC.testcases.mweb_account.mweb_order.test_108219_mWeb_my_canceled_order_ui_ux"}]}
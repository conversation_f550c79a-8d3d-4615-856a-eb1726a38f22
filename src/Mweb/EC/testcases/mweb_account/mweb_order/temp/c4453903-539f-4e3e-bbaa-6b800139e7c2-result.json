{"name": "【102678】 Mobile-Account 页面-我的订单模块UI/UX验证", "status": "broken", "statusDetails": {"message": "playwright._impl._errors.Error: Error: strict mode violation: locator(\"//a[contains(@href,'/order/list')]\") resolved to 4 elements:\n    1) <a href=\"https://www.sayweee.com/en/order/list\">…</a> aka get_by_role(\"link\", name=\"weee section undefined logo\")\n    2) <a class=\"w-1/5\" href=\"https://www.sayweee.com/en/o…>…</a> aka get_by_role(\"link\", name=\"weee order Pending image\")\n    3) <a class=\"w-1/5\" href=\"https://www.sayweee.com/en/o…>…</a> aka get_by_role(\"link\", name=\"weee order Unshipped image\")\n    4) <a class=\"w-1/5\" href=\"https://www.sayweee.com/en/o…>…</a> aka get_by_role(\"link\", name=\"weee order Shipped image\")", "trace": "self = <src.Mweb.EC.testcases.mweb_account.mweb_order.test_102678_mWeb_account_my_order_ui_ux.TestMWebMyCanceledOrderUIUX object at 0x0000013824912290>\nphone_page = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppDat...\\chrome-win\\chrome.exe> version=120.0.6099.28>>, 'page': <Page url='https://www.sayweee.com/en/account?joinEnki=true'>}\nh5_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...bxJk4_hlb9k5qdIpc8cGCahZ6wituu9yj4zLCuhvZmEAoDMeafMm-lFa4rrQsGqKSFCIs1sraLAj8W4wxuY7LzIl7oZ6vq8X0Ga7Dd-tJSnk8GhE', ...}\nh5_open_and_close_trace = None\n\n    @allure.title(\"【102678】 Mobile-Account 页面-我的订单模块UI/UX验证\")\n    def test_102678_mWeb_account_my_order_ui_ux(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):\n        \"\"\"\n        【102678】 Mobile-Account 页面-我的订单模块UI/UX验证\n        测试步骤：\n        1、me页面的我的订单模块下面展示:待付款、待发货、已发货、待晒单、退换/售后\n        2、点击我的订单>,进入订单列表:默认选中“全部订单”tab\n        3、点击待付款:进入我的订单页面，待付款tab下\n        4、点击待发货:进入我的订单页面，待发货tab下\n        5、点击已发货：进入我的订单页面，已发货tab下\n        6、点击待晒单：进入我的订单页面，待晒单tab下\n        7、点击退换/售后：进入我的订单页面，退换/售后tab下\n    \n        \"\"\"\n        p: Page = phone_page.get(\"page\")\n        c = phone_page.get(\"context\")\n    \n        # 1. 进入订单列表页面\n        order_page = MWebOrderPage(p, h5_autotest_header, browser_context=c, page_url=\"/account\")\n        p.wait_for_timeout(3000)\n        log.info(\"成功进入订单列表页面\")\n        # 3. 滚动到我的订单模块\n>       order_page.scroll_to_pos(mweb_account_ele.my_orders_ele)\n\ntest_102678_mWeb_account_my_order_ui_ux.py:36: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n..\\..\\..\\mweb_pages\\mweb_page_common\\mweb_common_page.py:66: in scroll_to_pos\n    self.page.locator(pos).scroll_into_view_if_needed()\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py:17744: in scroll_into_view_if_needed\n    self._sync(self._impl_obj.scroll_into_view_if_needed(timeout=timeout))\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_locator.py:539: in scroll_into_view_if_needed\n    return await self._with_element(\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_locator.py:114: in _with_element\n    handle = await self.element_handle(timeout=timeout)\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_locator.py:314: in element_handle\n    handle = await self._frame.wait_for_selector(\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_frame.py:317: in wait_for_selector\n    await self._channel.send(\"waitForSelector\", locals_to_params(locals()))\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:62: in send\n    return await self._connection.wrap_api_call(\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:492: in wrap_api_call\n    return await cb()\n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n\nself = <playwright._impl._connection.Channel object at 0x0000013824AD7D50>\nmethod = 'waitForSelector'\nparams = {'selector': \"//a[contains(@href,'/order/list')]\", 'state': 'attached', 'strict': True, 'timeout': 30000}\nreturn_as_dict = False\n\n    async def inner_send(\n        self, method: str, params: Optional[Dict], return_as_dict: bool\n    ) -> Any:\n        if params is None:\n            params = {}\n        callback = self._connection._send_message_to_server(\n            self._object, method, params\n        )\n        if self._connection._error:\n            error = self._connection._error\n            self._connection._error = None\n            raise error\n        done, _ = await asyncio.wait(\n            {\n                self._connection._transport.on_error_future,\n                callback.future,\n            },\n            return_when=asyncio.FIRST_COMPLETED,\n        )\n        if not callback.future.done():\n            callback.future.cancel()\n>       result = next(iter(done)).result()\nE       playwright._impl._errors.Error: Error: strict mode violation: locator(\"//a[contains(@href,'/order/list')]\") resolved to 4 elements:\nE           1) <a href=\"https://www.sayweee.com/en/order/list\">…</a> aka get_by_role(\"link\", name=\"weee section undefined logo\")\nE           2) <a class=\"w-1/5\" href=\"https://www.sayweee.com/en/o…>…</a> aka get_by_role(\"link\", name=\"weee order Pending image\")\nE           3) <a class=\"w-1/5\" href=\"https://www.sayweee.com/en/o…>…</a> aka get_by_role(\"link\", name=\"weee order Unshipped image\")\nE           4) <a class=\"w-1/5\" href=\"https://www.sayweee.com/en/o…>…</a> aka get_by_role(\"link\", name=\"weee order Shipped image\")\n\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:100: Error"}, "description": "\n        【102678】 Mobile-Account 页面-我的订单模块UI/UX验证\n        测试步骤：\n        1、me页面的我的订单模块下面展示:待付款、待发货、已发货、待晒单、退换/售后\n        2、点击我的订单>,进入订单列表:默认选中“全部订单”tab\n        3、点击待付款:进入我的订单页面，待付款tab下\n        4、点击待发货:进入我的订单页面，待发货tab下\n        5、点击已发货：进入我的订单页面，已发货tab下\n        6、点击待晒单：进入我的订单页面，待晒单tab下\n        7、点击退换/售后：进入我的订单页面，退换/售后tab下\n\n        ", "start": *************, "stop": *************, "uuid": "90753c14-5eb6-4435-8bc8-e5466eb05d3a", "historyId": "f112e5f34e3de78d13b585355752f098", "testCaseId": "f112e5f34e3de78d13b585355752f098", "fullName": "src.Mweb.EC.testcases.mweb_account.mweb_order.test_102678_mWeb_account_my_order_ui_ux.TestMWebMyCanceledOrderUIUX#test_102678_mWeb_account_my_order_ui_ux", "labels": [{"name": "story", "value": "【102678】 Mobile-Account 页面-我的订单模块UI/UX验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "h5order"}, {"name": "tag", "value": "mweb_todo"}, {"name": "tag", "value": "transaction"}, {"name": "tag", "value": "suqin"}, {"name": "parentSuite", "value": "src.Mweb.EC.testcases.mweb_account.mweb_order"}, {"name": "suite", "value": "test_102678_mWeb_account_my_order_ui_ux"}, {"name": "subSuite", "value": "TestMWebMyCanceledOrderUIUX"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "26220-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Mweb.EC.testcases.mweb_account.mweb_order.test_102678_mWeb_account_my_order_ui_ux"}]}
{"name": "【108218】 订单列表All tab-列表流程验证", "status": "failed", "statusDetails": {"message": "AssertionError: 没有进入订单详情页面\nassert 'order/detail/' in 'https://www.sayweee.com/en/order/dealpay/v2/********'\n +  where 'https://www.sayweee.com/en/order/dealpay/v2/********' = <Page url='https://www.sayweee.com/en/order/dealpay/v2/********'>.url", "trace": "self = <src.Mweb.EC.testcases.mweb_account.mweb_order.test_108218_mWeb_my_all_order_ui_ux.TestMWebMyCanceledOrderUIUX object at 0x0000021206A24950>\nphone_page = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppDat...ome-win\\chrome.exe> version=120.0.6099.28>>, 'page': <Page url='https://www.sayweee.com/en/order/dealpay/v2/********'>}\nh5_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...gQzEbFRrQd7aCcOs2sr4pB7AbW4mPb_J6YbUHJR_UQK2EjhFR-8bCjKfC2jTzagUHBKVFMf2wWGHb9p4QC2amz2y2TO9slSDZIMd9qmE9pODq4SU', ...}\nh5_open_and_close_trace = None\n\n    @allure.title(\"【108218】 订单列表All tab-列表流程验证\")\n    def test_108218_mWeb_my_all_order_ui_ux(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):\n        \"\"\"\n        【108218】 订单列表All tab-列表流程验证\n        测试步骤：\n        1、进入订单列表页面，点击切换到已取消tab下\n        2、如果存在订单，订单上会存在各种按钮\n        3、点击 退款详情 按钮，进入售后退款页面，再返回已送达页面\n        5、点击reorder 按钮 ，拉起再来一单选择商品页面，再返回已送达页面\n        \"\"\"\n        p: Page = phone_page.get(\"page\")\n        c = phone_page.get(\"context\")\n    \n        # 1. 进入订单列表页面\n        order_page = MWebOrderPage(p, h5_autotest_header, browser_context=c, page_url=\"/order/list\")\n        p.wait_for_timeout(3000)\n        log.info(\"成功进入订单列表页面\")\n    \n        # 切换到全部订单tab\n        alltab = p.get_by_test_id(mweb_order_list_ele.order_all_tab_ele)\n        assert alltab.is_visible(), \"未找到全部订单tab\"\n        # 切换到全部订单 tab\n        alltab.click()\n        p.wait_for_timeout(2000)\n        # 验证已切换到全部订单 tab\n        assert \"/order/list?filter_status=all\" in p.url, \"未切换到全部订单tab\"\n        # 2. 检查是否存在订单\n        empty_state = p.get_by_test_id(mweb_order_list_ele.empty_image_ele)\n        if empty_state.is_visible():\n            # 全部订单tab下没有订单\n            # 2. 如果没有订单，点击start shopping按钮\n            start_shopping_btn = p.get_by_test_id(mweb_order_list_ele.start_shopping_btn_ele)\n            assert start_shopping_btn.is_visible(), \"未找到start shopping按钮\"\n            assert p.get_by_test_id(mweb_order_list_ele.empty_title_ele)\n            start_shopping_btn.click()\n            p.wait_for_timeout(3000)\n            # 判断进入首页\n            assert p.get_by_test_id(\"wid-view-more\").is_visible(), \"未成功跳转到首页\"\n        else:\n            # 获取已取消tab下的normal订单\n            order_R_items = p.get_by_test_id(mweb_order_list_ele.order_list_R_card_ele).all()\n            order_S_items = p.get_by_test_id(mweb_order_list_ele.order_list_S_card_ele).all()\n            order_P_items = p.get_by_test_id(mweb_order_list_ele.order_list_P_card_ele).all()\n            order_G_items = p.get_by_test_id(mweb_order_list_ele.order_list_G_card_ele).all()\n    \n            if len(order_R_items) == 0 and len(order_S_items) == 0:\n                log.warning(\"已发货tab下没有normal+seller 订单，无法继续测试\")\n                pytest.skip(\"已发货tab下没有normal+seller 订单，跳过测试\")\n            elif len(order_R_items) > 0:\n                for index, item_R in enumerate(order_R_items):\n                    # 使用公共断言方法验证seller订单卡片信息\n                    assert order_page.assert_order_tab_info(item_R, \"all\")\n                    assert order_page.assert_order_card_info(item_R,\"R\"), f\"第{index + 1}个生鲜订单卡片信息验证失败\"\n                    assert order_page.order_card_btn(item_R), f\"第{index + 1}个生鲜订单卡片按钮验证失败\"\n            elif len(order_S_items) > 0:\n                for index, item_S in enumerate(order_S_items):\n                    # 使用公共断言方法验证seller订单卡片信息\n                    assert order_page.assert_order_card_info(item_S,\"S\"), f\"第{index+1}个seller订单卡片信息验证失败\"\n                    assert order_page.order_card_btn(item_S), f\"第{index + 1}个seller订单卡片按钮验证失败\"\n            elif len(order_P_items) > 0:\n                for index, item_P in enumerate(order_P_items):\n                    # 验证前10 个订单seller内容信息\n                    # 使用公共断言方法验证seller订单卡片信息\n                    assert order_page.assert_order_card_info(item_P,\"P\"), f\"第{index+1}个积分订单卡片信息验证失败\"\n            elif len(order_G_items) > 0:\n                for index, item_G in enumerate(order_G_items):\n                    # 验证前10 个订单seller内容信息\n                    # 使用公共断言方法验证seller订单卡片信息\n                    assert order_page.assert_order_card_info(item_G, \"G\"), f\"第{index + 1}个里礼品卡订单卡片信息验证失败\"\n            # 点击卡片进入pdp\n            order_R_items[0].click()\n            p.wait_for_timeout(2000)\n            # 断言页面进入订单详情页面\n>           assert \"order/detail/\" in p.url, \"没有进入订单详情页面\"\nE           AssertionError: 没有进入订单详情页面\nE           assert 'order/detail/' in 'https://www.sayweee.com/en/order/dealpay/v2/********'\nE            +  where 'https://www.sayweee.com/en/order/dealpay/v2/********' = <Page url='https://www.sayweee.com/en/order/dealpay/v2/********'>.url\n\ntest_108218_mWeb_my_all_order_ui_ux.py:88: AssertionError"}, "description": "\n        【108218】 订单列表All tab-列表流程验证\n        测试步骤：\n        1、进入订单列表页面，点击切换到已取消tab下\n        2、如果存在订单，订单上会存在各种按钮\n        3、点击 退款详情 按钮，进入售后退款页面，再返回已送达页面\n        5、点击reorder 按钮 ，拉起再来一单选择商品页面，再返回已送达页面\n        ", "start": *************, "stop": *************, "uuid": "20cfc5d7-e1ea-4d08-ba17-26c1d3e436bd", "historyId": "b865d3d049779ec4b9e2b5d776beef37", "testCaseId": "b865d3d049779ec4b9e2b5d776beef37", "fullName": "src.Mweb.EC.testcases.mweb_account.mweb_order.test_108218_mWeb_my_all_order_ui_ux.TestMWebMyCanceledOrderUIUX#test_108218_mWeb_my_all_order_ui_ux", "labels": [{"name": "story", "value": "【108218】 订单列表All tab-列表流程验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "h5order"}, {"name": "tag", "value": "mweb_regression"}, {"name": "tag", "value": "transaction"}, {"name": "tag", "value": "suqin"}, {"name": "parentSuite", "value": "src.Mweb.EC.testcases.mweb_account.mweb_order"}, {"name": "suite", "value": "test_108218_mWeb_my_all_order_ui_ux"}, {"name": "subSuite", "value": "TestMWebMyCanceledOrderUIUX"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "30908-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Mweb.EC.testcases.mweb_account.mweb_order.test_108218_mWeb_my_all_order_ui_ux"}]}

import allure
import pytest

from playwright.sync_api import Page

from src.Mweb.EC.mweb_ele.mweb_order import mweb_order_list_ele
from src.Mweb.EC.mweb_pages.mweb_order_page.mweb_order_page import MWebOrderPage
from src.config.weee.log_help import log


@allure.story("【108219】 订单列表已取消tab-订单流程验证")
class TestMWebMyCanceledOrderUIUX:
    pytestmark = [pytest.mark.h5order, pytest.mark.mweb_todo, pytest.mark.transaction, pytest.mark.suqin]
    @allure.title("【108219】 订单列表已取消tab-订单流程验证")
    def test_108219_mWeb_my_canceled_order_ui_ux(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):
        """
        【108219】 订单列表已取消tab-订单流程验证
        测试步骤：
        1、进入订单列表页面，点击切换到已取消tab下
        2、如果存在订单，订单上会存在各种按钮
        3、点击 退款详情 按钮，进入售后退款页面，再返回已送达页面
        5、点击reorder 按钮 ，拉起再来一单选择商品页面，再返回已送达页面
        """
        p: Page = phone_page.get("page")
        c = phone_page.get("context")

        # 1. 进入订单列表页面
        order_page = MWebOrderPage(p, h5_autotest_header, browser_context=c, page_url="/order/list")
        p.wait_for_timeout(3000)
        log.info("成功进入订单列表页面")
        
        # 切换到已取消tab
        cancelled_tab = p.get_by_test_id(mweb_order_list_ele.order_cancelled_tab_ele)
        assert cancelled_tab.is_visible(), "未找到已取消tab"
        # 切换到已取消 tab
        cancelled_tab.click()
        p.wait_for_timeout(2000)
        # 验证已切换到已取消 tab
        assert "/order/list?filter_status=4" in p.url, "未切换到已取消tab"
        # 2. 检查是否存在订单
        empty_state = p.get_by_test_id(mweb_order_list_ele.empty_image_ele)
        if empty_state.is_visible():
            # 已取消tab下没有订单
            # 2. 如果没有订单，点击start shopping按钮
            start_shopping_btn = p.get_by_test_id(mweb_order_list_ele.start_shopping_btn_ele)
            assert start_shopping_btn.is_visible(), "未找到start shopping按钮"
            assert p.get_by_test_id(mweb_order_list_ele.empty_title_ele)
            start_shopping_btn.click()
            p.wait_for_timeout(3000)
            # 判断进入首页
            assert p.get_by_test_id("wid-view-more").is_visible(), "未成功跳转到首页"
        else:
            # 获取已取消tab下的normal订单
            order_R_items = p.get_by_test_id(mweb_order_list_ele.order_list_R_card_ele).all()
            order_S_items = p.get_by_test_id(mweb_order_list_ele.order_list_S_card_ele).all()

            if len(order_R_items) == 0 and len(order_S_items) == 0:
                log.warning("已发货tab下没有normal+seller 订单，无法继续测试")
                pytest.skip("已发货tab下没有normal+seller 订单，跳过测试")
            elif len(order_R_items) > 0:
                pass

            elif len(order_S_items) > 0:

                for index, item in enumerate(order_S_items):
                    # 验证前10 个订单seller内容信息
                    assert item.get_by_test_id(mweb_order_list_ele.order_list_card_status_ele).is_visible(), "订单内容信息不存在"
                    assert item.get_by_test_id("wid-order-list-totals-label").is_visible(), "订单内容信息不存在"
                    totals_amount = item.get_by_test_id("wid-order-list-totals-amount").text_content()
                    # 提取数字部分
                    import re
                    assert float(re.findall(r'\d+\.\d+', totals_amount)[0])> 0, "订单内容金额不存在"


                    # seller 订单
                    assert item.get_by_test_id("wid-order-list-vendor-title-link").is_visible(), "seller订单icon信息不存在"
                    # 验证订单卡片上 Delivery date\Order number\Items\Total 信息
                    assert item.get_by_test_id("wid-order-list-seller-delivery-date-label").is_visible(), " 预计送达信息不存在"
                    assert item.get_by_test_id("wid-order-list-pick-date").is_visible(), " Delivery date\Order number\Items\Total 信息不存在"


                    # 断言订单状态是Cancelled
                    assert item.locator(mweb_order_list_ele.order_list_card_statu_ele).text_content() in ("Canceled",
                                                                                                          "Cancelled"), "订单状态不是Cancelled"
                    assert item.locator(mweb_order_list_ele.order_list_card_product_ele).is_visible()
                # 点击卡片进入pdp
                order_list_items[0].locator(mweb_order_list_ele.order_list_card_product_ele).click()
                p.wait_for_timeout(2000)
                # 断言页面进入订单详情页面
                assert "order/detail/" in p.url, "没有进入订单详情页面"
                p.go_back()

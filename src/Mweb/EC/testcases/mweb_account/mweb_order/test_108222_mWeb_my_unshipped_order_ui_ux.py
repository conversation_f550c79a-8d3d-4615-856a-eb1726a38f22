
import allure
import pytest

from playwright.sync_api import Page

from src.Mweb.EC.mweb_pages.mweb_order_page.mweb_order_page import MWebOrderPage
from src.config.weee.log_help import log


@allure.story("【108222】 订单列表待发货 tab-订单流程验证")
class TestMWebMyUnShippedOrderUIUX:
    pytestmark = [pytest.mark.h5order, pytest.mark.mweb_todo, pytest.mark.transaction, pytest.mark.suqin]
    @allure.title("【108222】 订单列表待发货 tab-订单流程验证")
    def test_108222_mWeb_my_unshipped_order_ui_ux(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):
        """
        【108222】 订单列表待发货 tab-订单流程验证
        测试步骤：
        1、切换到待发货tab 下，检查是否有待发货订单
        2、如果没有，点击start shopping，进入首页
        3、如果有查看待发货订单，验证订单信息正确，订单状态正确
        4、点击订单列表下各按钮
        5、点击订单进入订单详情，验证订单详情信息正确，各交互正确
        """
        p: Page = phone_page.get("page")
        c = phone_page.get("context")

        # 1. 进入订单列表页面
        order_page = MWebOrderPage(p, h5_autotest_header, browser_context=c, page_url="/order/list")
        p.wait_for_timeout(3000)
        log.info("成功进入订单列表页面")
        
        # 切换到待发货tab
        unshipped_tab = p.get_by_test_id("order-tab-unshipped")
        assert unshipped_tab.is_visible(), "未找到待发货tab"
        unshipped_tab.click()
        p.wait_for_timeout(2000)
        log.info("成功切换到待发货tab")
        
        # 验证已切换到待发货tab
        assert unshipped_tab.get_attribute("aria-selected") == "true", "未成功切换到待发货tab"
        
        # 检查是否有待发货订单
        empty_state = p.get_by_test_id("empty-state")
        if empty_state.is_visible():
            log.info("待发货tab下没有订单")
            
            # 2. 如果没有订单，点击start shopping按钮
            start_shopping_btn = p.get_by_test_id("btn-start-shopping")
            assert start_shopping_btn.is_visible(), "未找到start shopping按钮"
            start_shopping_btn.click()
            p.wait_for_timeout(3000)
            log.info("点击start shopping按钮")
            
            # 验证跳转到首页
            assert "/product" in p.url, "未成功跳转到首页"
            log.info("成功跳转到首页")
            
            # 由于没有待发货订单，无法继续测试
            pytest.skip("待发货tab下没有订单，跳过后续测试")
        
        # 3. 如果有待发货订单，验证订单信息正确
        order_items = p.get_by_test_id("order-item").all()
        assert len(order_items) > 0, "待发货tab下没有订单"
        log.info(f"待发货tab下有{len(order_items)}个订单")
        
        # 选择第一个订单进行验证
        first_order = order_items[0]
        
        # 验证订单号存在
        order_number = first_order.get_by_test_id("order-number")
        assert order_number.is_visible(), "未找到订单号"
        order_num_text = order_number.text_content()
        assert order_num_text and "Order #" in order_num_text, "订单号格式不正确"
        log.info(f"订单号: {order_num_text}")
        
        # 验证订单日期存在
        order_date = first_order.get_by_test_id("order-date")
        assert order_date.is_visible(), "未找到订单日期"
        log.info(f"订单日期: {order_date.text_content()}")
        
        # 验证订单金额存在
        order_amount = first_order.get_by_test_id("order-amount")
        assert order_amount.is_visible(), "未找到订单金额"
        log.info(f"订单金额: {order_amount.text_content()}")
        
        # 验证订单状态正确
        order_status = first_order.get_by_test_id("order-status")
        assert order_status.is_visible(), "未找到订单状态"
        status_text = order_status.text_content()
        assert "Processing" in status_text or "Pending" in status_text, f"订单状态不是待发货状态: {status_text}"
        log.info(f"订单状态: {status_text}")
        
        # 验证订单商品列表存在
        order_products = first_order.get_by_test_id("order-product").all()
        assert len(order_products) > 0, "未找到订单商品"
        log.info(f"订单包含{len(order_products)}个商品")
        
        # 4. 点击订单列表下各按钮
        # 检查是否有取消订单按钮
        cancel_order_btn = first_order.get_by_test_id("btn-cancel-order")
        if cancel_order_btn.is_visible():
            log.info("找到取消订单按钮，但不进行点击操作，避免实际取消订单")
        else:
            log.info("当前订单没有取消订单按钮")
        
        # 检查是否有修改订单按钮
        modify_order_btn = first_order.get_by_test_id("btn-modify-order")
        if modify_order_btn.is_visible():
            log.info("找到修改订单按钮，但不进行点击操作，避免实际修改订单")
        else:
            log.info("当前订单没有修改订单按钮")
        
        # 检查是否有联系客服按钮
        contact_support_btn = first_order.get_by_test_id("btn-contact-support")
        if contact_support_btn.is_visible():
            log.info("找到联系客服按钮")
            # 可以点击联系客服按钮，但这里不实际点击，避免打开客服聊天
        else:
            log.info("当前订单没有联系客服按钮")
        
        # 5. 点击订单进入订单详情
        # 保存订单号，用于后续验证
        order_number_text = order_num_text.replace("Order #", "").strip()
        
        # 点击订单进入详情页
        first_order.click()
        p.wait_for_timeout(3000)
        log.info("点击订单进入详情页")
        
        # 验证进入订单详情页
        assert "order/detail" in p.url, "未成功进入订单详情页"
        log.info("成功进入订单详情页")
        
        # 验证订单详情页的订单号与列表页一致
        detail_order_number = p.get_by_test_id("detail-order-number")
        assert detail_order_number.is_visible(), "未找到详情页订单号"
        detail_order_num_text = detail_order_number.text_content()
        assert order_number_text in detail_order_num_text, "详情页订单号与列表页不一致"
        log.info("验证详情页订单号与列表页一致")
        
        # 验证订单状态
        detail_order_status = p.get_by_test_id("detail-order-status")
        assert detail_order_status.is_visible(), "未找到详情页订单状态"
        detail_status_text = detail_order_status.text_content()
        assert "Processing" in detail_status_text or "Pending" in detail_status_text, f"详情页订单状态不是待发货状态: {detail_status_text}"
        log.info(f"详情页订单状态: {detail_status_text}")
        
        # 验证订单详情各部分
        # 验证配送信息部分
        delivery_info_section = p.get_by_test_id("delivery-info-section")
        assert delivery_info_section.is_visible(), "未找到配送信息部分"
        log.info("验证配送信息部分存在")
        
        # 验证配送地址
        delivery_address = p.get_by_test_id("delivery-address")
        assert delivery_address.is_visible(), "未找到配送地址"
        log.info(f"配送地址: {delivery_address.text_content()}")
        
        # 验证配送时间
        delivery_time = p.get_by_test_id("delivery-time")
        assert delivery_time.is_visible(), "未找到配送时间"
        log.info(f"配送时间: {delivery_time.text_content()}")
        
        # 验证订单商品部分
        order_items_section = p.get_by_test_id("order-items-section")
        assert order_items_section.is_visible(), "未找到订单商品部分"
        
        # 验证商品列表
        detail_products = p.get_by_test_id("order-product").all()
        assert len(detail_products) > 0, "未找到详情页订单商品"
        log.info(f"详情页订单包含{len(detail_products)}个商品")
        
        # 验证订单金额部分
        order_summary_section = p.get_by_test_id("order-summary-section")
        assert order_summary_section.is_visible(), "未找到订单金额部分"
        
        # 验证商品总价
        subtotal = p.get_by_test_id("subtotal")
        assert subtotal.is_visible(), "未找到商品总价"
        log.info(f"商品总价: {subtotal.text_content()}")
        
        # 验证配送费
        delivery_fee = p.get_by_test_id("delivery-fee")
        assert delivery_fee.is_visible(), "未找到配送费"
        log.info(f"配送费: {delivery_fee.text_content()}")
        
        # 验证订单总价
        total = p.get_by_test_id("total")
        assert total.is_visible(), "未找到订单总价"
        log.info(f"订单总价: {total.text_content()}")
        
        # 验证支付方式部分
        payment_section = p.get_by_test_id("payment-section")
        assert payment_section.is_visible(), "未找到支付方式部分"
        
        # 验证支付方式
        payment_method = p.get_by_test_id("payment-method")
        assert payment_method.is_visible(), "未找到支付方式"
        log.info(f"支付方式: {payment_method.text_content()}")
        
        # 验证详情页按钮
        # 检查是否有取消订单按钮
        detail_cancel_btn = p.get_by_test_id("btn-cancel-order")
        if detail_cancel_btn.is_visible():
            log.info("找到详情页取消订单按钮，但不进行点击操作，避免实际取消订单")
        else:
            log.info("详情页没有取消订单按钮")
        
        # 检查是否有修改订单按钮
        detail_modify_btn = p.get_by_test_id("btn-modify-order")
        if detail_modify_btn.is_visible():
            log.info("找到详情页修改订单按钮，但不进行点击操作，避免实际修改订单")
        else:
            log.info("详情页没有修改订单按钮")
        
        # 检查是否有联系客服按钮
        detail_contact_btn = p.get_by_test_id("btn-contact-support")
        if detail_contact_btn.is_visible():
            log.info("找到详情页联系客服按钮")
            # 可以点击联系客服按钮，但这里不实际点击，避免打开客服聊天
        else:
            log.info("详情页没有联系客服按钮")
        
        # 返回订单列表页
        back_btn = p.get_by_test_id("btn-back")
        assert back_btn.is_visible(), "未找到返回按钮"
        back_btn.click()
        p.wait_for_timeout(2000)
        log.info("点击返回按钮")
        
        # 验证返回订单列表页
        assert "/order/list" in p.url, "未成功返回订单列表页"
        assert p.get_by_test_id("order-list-page").is_visible(), "未成功返回订单列表页"
        log.info("成功返回订单列表页")
        
        log.info("订单列表待发货 tab-订单流程验证完成")

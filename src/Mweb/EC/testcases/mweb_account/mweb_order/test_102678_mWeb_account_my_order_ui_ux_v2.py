import allure
import pytest

from playwright.sync_api import Page

from src.Mweb.EC.mweb_ele.mweb_account import mweb_account_ele
from src.Mweb.EC.mweb_pages.mweb_order_page.mweb_order_page import MWebOrderPage
from src.config.weee.log_help import log


@allure.story("【102678】 Mobile-Account 页面-我的订单模块UI/UX验证")
class TestMWebAccountMyOrderUIUXV2:
    pytestmark = [pytest.mark.h5order, pytest.mark.mweb_regression, pytest.mark.transaction, pytest.mark.suqin]
    
    @allure.title("【102678】 Mobile-Account 页面-我的订单模块UI/UX验证 V2")
    def test_102678_mWeb_account_my_order_ui_ux_v2(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):
        """
        【102678】 Mobile-Account 页面-我的订单模块UI/UX验证 V2
        测试步骤：
        1、me页面的我的订单模块下面展示:待付款、待发货、已发货、待晒单、退换/售后
        2、点击我的订单>,进入订单列表:默认选中"全部订单"tab
        3、点击待付款:进入我的订单页面，待付款tab下
        4、点击待发货:进入我的订单页面，待发货tab下
        5、点击已发货：进入我的订单页面，已发货tab下
        6、点击待晒单：进入我的订单页面，待晒单tab下
        7、点击退换/售后：进入我的订单页面，退换/售后tab下
        """
        p: Page = phone_page.get("page")
        c = phone_page.get("context")

        # 1. 进入me页面，验证我的订单模块展示
        log.info("步骤1：进入me页面，验证我的订单模块展示")
        account_page = MWebOrderPage(p, h5_autotest_header, browser_context=c, page_url="/account")
        p.wait_for_timeout(3000)
        log.info("成功进入账户页面")
        
        # 滚动到我的订单模块
        account_page.scroll_to_pos(mweb_account_ele.my_orders_ele)
        log.info("滚动到我的订单模块")
        p.wait_for_timeout(2000)
        
        # 验证我的订单模块UI元素
        self._verify_my_orders_ui_elements(p)
        
        # 2. 点击我的订单>,进入订单列表:默认选中"全部订单"tab
        log.info("步骤2：点击我的订单>,进入订单列表")
        self._click_my_orders_link_and_verify_all_tab(p)
        
        # 3-7. 测试各个订单状态快捷入口
        order_status_tests = [
            ("待付款", mweb_account_ele.my_order_pending_tab_ele, "/order/list?filter_status=1"),
            ("待发货", mweb_account_ele.my_order_unshipped_tab_ele, "/order/list?filter_status=2"),
            ("已发货", mweb_account_ele.my_order_shipped_tab_ele, "/order/list?filter_status=3"),
            ("待晒单", mweb_account_ele.my_order_review_tab_ele, "/order/list?filter_status=6"),
            ("退换/售后", mweb_account_ele.my_order_returns_tab_ele, "/order/case/list?ws=me_page")
        ]
        
        for step_num, (status_name, test_id, expected_url) in enumerate(order_status_tests, 3):
            log.info(f"步骤{step_num}：点击{status_name}，进入我的订单页面")
            self._test_order_status_shortcut(p, account_page, status_name, test_id, expected_url)

        log.info("所有测试步骤执行完成")

    def _verify_my_orders_ui_elements(self, p: Page):
        """
        验证我的订单模块的UI元素
        保留原有的备注和验证逻辑
        """
        # 断言 > 存在
        assert p.get_by_test_id("wid-order-arrow-right").is_visible(), "我的订单箭头未找到"
        
        # 验证各个订单状态的UI元素
        order_statuses = ["pending", "unshipped", "shipped", "to_review", "returns"]
        
        for status in order_statuses:
            # 断言 我的订单里 icon、title 都存在
            assert p.get_by_test_id(f"wid-order-{status}-image").is_visible(), f"{status} 图标未找到"
            
            # 处理title元素的空格问题（保留原有逻辑）
            if status == "unshipped":
                assert p.get_by_test_id(f"wid-order-{status}-title  ").is_visible(), f"{status} 标题未找到"
            else:
                assert p.get_by_test_id(f"wid-order-{status}-title").is_visible(), f"{status} 标题未找到"
            
            # 断言如果存在数字球，大于99 显示99+
            value_element = p.get_by_test_id(f"wid-order-{status}-value")
            if value_element.is_visible():
                try:
                    value_text = value_element.text_content()
                    if value_text and value_text.isdigit() and int(value_text) > 99:
                        assert value_text == "99+", f"{status} 数字球显示错误，期望99+，实际{value_text}"
                except (ValueError, TypeError):
                    # 如果不是数字，跳过验证
                    pass
            
            log.info(f"{status} 状态UI元素验证成功")

    def _click_my_orders_link_and_verify_all_tab(self, p: Page):
        """
        点击我的订单链接并验证全部订单tab
        """
        # 点击我的订单> 入口
        my_orders = p.get_by_test_id(mweb_account_ele.my_orders_link_ele)
        assert my_orders.is_visible(), "未找到我的订单> 入口"
        
        # 切换到全部订单tab
        my_orders.click()
        p.wait_for_timeout(2000)
        
        # 验证已切换到全部订单tab
        current_url = p.url
        log.info(f"当前URL: {current_url}")
        assert "/order/list" in current_url, "未切换到全部订单tab"
        log.info("成功进入订单列表页面，默认选中全部订单tab")

    def _test_order_status_shortcut(self, p: Page, account_page: MWebOrderPage, status_name: str, test_id: str, expected_url: str):
        """
        测试订单状态快捷入口
        Args:
            p: Page对象
            account_page: 账户页面对象
            status_name: 状态名称
            test_id: 测试ID
            expected_url: 期望的URL
        """
        # 返回账户页面
        p.go_back()
        p.wait_for_timeout(2000)
        
        # 滚动到我的订单模块
        account_page.scroll_to_pos(mweb_account_ele.my_orders_ele)
        log.info("滚动到我的订单模块")
        p.wait_for_timeout(2000)
        
        # 点击对应的状态tab
        status_tab = p.get_by_test_id(test_id)
        assert status_tab.is_visible(), f"未找到{status_name}tab"
        
        # 切换到对应状态tab
        status_tab.click()
        p.wait_for_timeout(2000)
        
        # 验证已切换到对应状态tab
        current_url = p.url
        log.info(f"当前URL: {current_url}")
        assert expected_url in current_url, f"未切换到{status_name}tab"
        log.info(f"成功切换到{status_name}tab")

    def _scroll_to_my_orders_section(self, account_page: MWebOrderPage):
        """
        滚动到我的订单模块的公共方法
        """
        account_page.scroll_to_pos(mweb_account_ele.my_orders_ele)
        log.info("滚动到我的订单模块")
        account_page.page.wait_for_timeout(2000)

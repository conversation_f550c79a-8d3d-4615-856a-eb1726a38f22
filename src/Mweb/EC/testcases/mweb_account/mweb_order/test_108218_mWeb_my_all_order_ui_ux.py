
import allure
import pytest

from playwright.sync_api import Page

from src.Mweb.EC.mweb_pages.mweb_order_page.mweb_order_page import MWebOrderPage
from src.config.weee.log_help import log
from src.Mweb.EC.mweb_ele.mweb_account.mweb_order import mweb_order_list_ele


@allure.story("【108218】 订单列表All tab-列表流程验证")
class TestMWebMyCanceledOrderUIUX:
    pytestmark = [pytest.mark.h5order, pytest.mark.mweb_regression, pytest.mark.transaction, pytest.mark.suqin]
    @allure.title("【108218】 订单列表All tab-列表流程验证")
    def test_108218_mWeb_my_all_order_ui_ux(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):
        """
        【108218】 订单列表All tab-列表流程验证
        测试步骤：
        1、进入订单列表页面，点击切换到已取消tab下
        2、如果存在订单，订单上会存在各种按钮
        3、点击 退款详情 按钮，进入售后退款页面，再返回已送达页面
        5、点击reorder 按钮 ，拉起再来一单选择商品页面，再返回已送达页面
        """
        p: Page = phone_page.get("page")
        c = phone_page.get("context")

        # 1. 进入订单列表页面
        order_page = MWebOrderPage(p, h5_autotest_header, browser_context=c, page_url="/order/list")
        p.wait_for_timeout(3000)
        log.info("成功进入订单列表页面")
        
        # 切换到全部订单tab
        alltab = p.get_by_test_id(mweb_order_list_ele.order_all_tab_ele)
        assert alltab.is_visible(), "未找到全部订单tab"
        # 切换到全部订单 tab
        alltab.click()
        p.wait_for_timeout(2000)
        # 验证已切换到全部订单 tab
        assert "/order/list?filter_status=all" in p.url, "未切换到全部订单tab"
        # 2. 检查是否存在订单
        empty_state = p.get_by_test_id(mweb_order_list_ele.empty_image_ele)
        if empty_state.is_visible():
            # 全部订单tab下没有订单
            # 2. 如果没有订单，点击start shopping按钮
            start_shopping_btn = p.get_by_test_id(mweb_order_list_ele.start_shopping_btn_ele)
            assert start_shopping_btn.is_visible(), "未找到start shopping按钮"
            assert p.get_by_test_id(mweb_order_list_ele.empty_title_ele)
            start_shopping_btn.click()
            p.wait_for_timeout(3000)
            # 判断进入首页
            assert p.get_by_test_id("wid-view-more").is_visible(), "未成功跳转到首页"
        else:
            # 获取已取消tab下的normal订单
            order_R_items = p.get_by_test_id(mweb_order_list_ele.order_list_R_card_ele).all()
            order_S_items = p.get_by_test_id(mweb_order_list_ele.order_list_S_card_ele).all()
            order_P_items = p.get_by_test_id(mweb_order_list_ele.order_list_P_card_ele).all()
            order_G_items = p.get_by_test_id(mweb_order_list_ele.order_list_G_card_ele).all()

            if len(order_R_items) == 0 and len(order_S_items) == 0:
                log.warning("已发货tab下没有normal+seller 订单，无法继续测试")
                pytest.skip("已发货tab下没有normal+seller 订单，跳过测试")
            elif len(order_R_items) > 0:
                for index, item_R in enumerate(order_R_items):
                    # 使用公共断言方法验证seller订单卡片信息
                    assert order_page.assert_order_tab_info(item_R, "all")
                    assert order_page.assert_order_card_info(item_R,"R"), f"第{index + 1}个生鲜订单卡片信息验证失败"
                    assert order_page.order_card_btn(item_R), f"第{index + 1}个生鲜订单卡片按钮验证失败"
            elif len(order_S_items) > 0:
                for index, item_S in enumerate(order_S_items):
                    # 使用公共断言方法验证seller订单卡片信息
                    assert order_page.assert_order_card_info(item_S,"S"), f"第{index+1}个seller订单卡片信息验证失败"
                    assert order_page.order_card_btn(item_S), f"第{index + 1}个seller订单卡片按钮验证失败"
            elif len(order_P_items) > 0:
                for index, item_P in enumerate(order_P_items):
                    # 验证前10 个订单seller内容信息
                    # 使用公共断言方法验证seller订单卡片信息
                    assert order_page.assert_order_card_info(item_P,"P"), f"第{index+1}个积分订单卡片信息验证失败"
            elif len(order_G_items) > 0:
                for index, item_G in enumerate(order_G_items):
                    # 验证前10 个订单seller内容信息
                    # 使用公共断言方法验证seller订单卡片信息
                    assert order_page.assert_order_card_info(item_G, "G"), f"第{index + 1}个里礼品卡订单卡片信息验证失败"
            # 点击卡片进入pdp
            order_R_items[0].click()
            p.wait_for_timeout(2000)
            # 断言页面进入订单详情页面
            assert "order/detail/" in p.url, "没有进入订单详情页面"
            p.get_by_test_id("wid-page-nav-header-back-button")




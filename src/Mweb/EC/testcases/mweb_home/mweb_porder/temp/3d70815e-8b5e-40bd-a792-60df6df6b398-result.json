{"name": "【108558】 购物车页面-切换日期验证 V2", "status": "failed", "statusDetails": {"message": "AssertionError: 购物车页面切换日期按钮未显示\nassert False\n +  where False = <bound method Locator.is_visible of <Locator frame=<Frame name= url='https://www.sayweee.com/en/cart?joinEnki=true'> selector='internal:testid=[data-testid=\"cart-delivery-date-btn\"s]'>>()\n +    where <bound method Locator.is_visible of <Locator frame=<Frame name= url='https://www.sayweee.com/en/cart?joinEnki=true'> selector='internal:testid=[data-testid=\"cart-delivery-date-btn\"s]'>> = <Locator frame=<Frame name= url='https://www.sayweee.com/en/cart?joinEnki=true'> selector='internal:testid=[data-testid=\"cart-delivery-date-btn\"s]'>.is_visible", "trace": "self = <test_108558_mWeb_change_delivery_date_on_cart_page_ui_ux_v2.TestMWebChangeDeliveryDateOnCartPageUIUXV2 object at 0x000001440F7AAB10>\nphone_page = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppDat...091\\chrome-win\\chrome.exe> version=120.0.6099.28>>, 'page': <Page url='https://www.sayweee.com/en/cart?joinEnki=true'>}\nh5_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...FvWrsDo22p9ykS-lT0BiCzI06zAtxmvCb4zw1eC0NLOjIvGHSfHkk3f8c4OXz_iXbSEecCTJNBqg9bICES22HUaKEwX3P0d32xvaRiJUxlwb3LhI', ...}\nh5_open_and_close_trace = None\n\n    @allure.title(\"【108558】 购物车页面-切换日期验证 V2\")\n    def test_108558_mWeb_change_delivery_date_on_cart_page_ui_ux_v2(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):\n        \"\"\"\n        【108558】 购物车页面-切换日期验证\n        测试步骤：\n        1、进入购物车页面\n        2、检查并切换zipcode为98011\n        3、如果购物车为空，从推荐模块加购商品\n        4、点击生鲜购物车上的切换日期按钮\n        5、弹出切换日期pop\n        6、点击pop里的日期进行切换日期\n        7、验证购物车日期切换成功\n        \"\"\"\n        p: Page = phone_page.get(\"page\")\n        c = phone_page.get(\"context\")\n    \n        # 1. 直接进入指定页面页面\n        cart_page = MWebCartPage(p, h5_autotest_header, browser_context=c, page_url=\"/cart\")\n        p.wait_for_timeout(3000)\n        log.info(\"成功进入购物车页面\")\n    \n        # 2. 检查并切换zipcode为98011\n        update_zipcode_v1(h5_autotest_header, \"98011\")\n        p.reload()\n        p.wait_for_timeout(3000)\n    \n        # 3. 如果购物车为空，从推荐模块加购商品\n    \n    \n        self.add_items_if_cart_empty(p,cart_page)\n    \n        # 4. 点击生鲜购物车上的切换日期按钮\n        # 使用data-testid定位切换日期按钮\n        delivery_date_btn = p.get_by_test_id(\"cart-delivery-date-btn\")\n>       assert delivery_date_btn.is_visible(), \"购物车页面切换日期按钮未显示\"\nE       AssertionError: 购物车页面切换日期按钮未显示\nE       assert False\nE        +  where False = <bound method Locator.is_visible of <Locator frame=<Frame name= url='https://www.sayweee.com/en/cart?joinEnki=true'> selector='internal:testid=[data-testid=\"cart-delivery-date-btn\"s]'>>()\nE        +    where <bound method Locator.is_visible of <Locator frame=<Frame name= url='https://www.sayweee.com/en/cart?joinEnki=true'> selector='internal:testid=[data-testid=\"cart-delivery-date-btn\"s]'>> = <Locator frame=<Frame name= url='https://www.sayweee.com/en/cart?joinEnki=true'> selector='internal:testid=[data-testid=\"cart-delivery-date-btn\"s]'>.is_visible\n\ntest_108558_mWeb_change_delivery_date_on_cart_page_ui_ux_v2.py:52: AssertionError"}, "description": "\n        【108558】 购物车页面-切换日期验证\n        测试步骤：\n        1、进入购物车页面\n        2、检查并切换zipcode为98011\n        3、如果购物车为空，从推荐模块加购商品\n        4、点击生鲜购物车上的切换日期按钮\n        5、弹出切换日期pop\n        6、点击pop里的日期进行切换日期\n        7、验证购物车日期切换成功\n        ", "start": 1747021255164, "stop": 1747021275301, "uuid": "d15f7b39-bc88-4e8b-8fa2-50bb163b4967", "historyId": "cf7afd47f620a8379254a81626a76086", "testCaseId": "cf7afd47f620a8379254a81626a76086", "fullName": "src.Mweb.EC.testcases.mweb_home.mweb_porder.test_108558_mWeb_change_delivery_date_on_cart_page_ui_ux_v2.TestMWebChangeDeliveryDateOnCartPageUIUXV2#test_108558_mWeb_change_delivery_date_on_cart_page_ui_ux_v2", "labels": [{"name": "story", "value": "【108558】 购物车页面-切换日期验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "h5order"}, {"name": "tag", "value": "mweb_regression"}, {"name": "tag", "value": "transaction"}, {"name": "tag", "value": "suqin"}, {"name": "parentSuite", "value": "src.Mweb.EC.testcases.mweb_home.mweb_porder"}, {"name": "suite", "value": "test_108558_mWeb_change_delivery_date_on_cart_page_ui_ux_v2"}, {"name": "subSuite", "value": "TestMWebChangeDeliveryDateOnCartPageUIUXV2"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "27908-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Mweb.EC.testcases.mweb_home.mweb_porder.test_108558_mWeb_change_delivery_date_on_cart_page_ui_ux_v2"}]}
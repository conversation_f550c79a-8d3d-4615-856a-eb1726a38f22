{"name": "【108558】 购物车页面-切换日期验证 V2", "status": "passed", "description": "\n        【108558】 购物车页面-切换日期验证\n        测试步骤：\n        1、进入购物车页面\n        2、检查并切换zipcode为98011\n        3、如果购物车为空，从推荐模块加购商品\n        4、点击生鲜购物车上的切换日期按钮\n        5、弹出切换日期pop\n        6、点击pop里的日期进行切换日期\n        7、验证购物车日期切换成功\n        ", "start": 1747021570820, "stop": 1747021594960, "uuid": "a04fc9d4-c3d0-460d-91be-09b7825f33d1", "historyId": "cf7afd47f620a8379254a81626a76086", "testCaseId": "cf7afd47f620a8379254a81626a76086", "fullName": "src.Mweb.EC.testcases.mweb_home.mweb_porder.test_108558_mWeb_change_delivery_date_on_cart_page_ui_ux_v2.TestMWebChangeDeliveryDateOnCartPageUIUXV2#test_108558_mWeb_change_delivery_date_on_cart_page_ui_ux_v2", "labels": [{"name": "story", "value": "【108558】 购物车页面-切换日期验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "h5order"}, {"name": "tag", "value": "mweb_regression"}, {"name": "tag", "value": "transaction"}, {"name": "tag", "value": "suqin"}, {"name": "parentSuite", "value": "src.Mweb.EC.testcases.mweb_home.mweb_porder"}, {"name": "suite", "value": "test_108558_mWeb_change_delivery_date_on_cart_page_ui_ux_v2"}, {"name": "subSuite", "value": "TestMWebChangeDeliveryDateOnCartPageUIUXV2"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "4320-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Mweb.EC.testcases.mweb_home.mweb_porder.test_108558_mWeb_change_delivery_date_on_cart_page_ui_ux_v2"}]}
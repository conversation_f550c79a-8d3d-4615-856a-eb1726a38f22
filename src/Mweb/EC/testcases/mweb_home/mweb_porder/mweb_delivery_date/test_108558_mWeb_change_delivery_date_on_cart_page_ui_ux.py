import allure
import pytest

from playwright.sync_api import Page

from src.Mweb.EC.mweb_ele import mweb_common_ele
from src.Mweb.EC.mweb_ele.mweb_cart import mweb_cart_ele
from src.Mweb.EC.mweb_pages.mweb_page_cart.mweb_page_cart import MWebCartPage
from src.common.commonui import scroll_one_page_until


@allure.story("【108558】 购物车页面-切换日期验证")
class TestMWebChangeDeliveryDateOnCartPageUIUX:
    pytestmark = [pytest.mark.h5order, pytest.mark.mweb_regression, pytest.mark.transaction, pytest.mark.suqin]

    @allure.title("【108558】 购物车页面-切换日期验证")
    def test_108558_mWeb_change_delivery_date_on_cart_page_ui_ux(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):
        """
        【108558】 购物车页面-切换日期验证
        """
        p: Page = phone_page.get("page")
        c = phone_page.get("context")
        # 直接进入指定页面
        cart_page = MWebCartPage(p, h5_autotest_header, browser_context=c, page_url="/cart")
        p.wait_for_timeout(3000)
        # 关闭Continue pop
        # p.locator(u"//button[contains(text(), 'Continue')]").click()
        # 滚动到指定位置-分享按钮
        # scroll_one_page_until(p, mweb_pdp_ele.ele_share)
        # 点击切换日期按钮
        cart_page.FE.ele(mweb_cart_ele.ele_cart_normal_delivery_date).click()
        p.wait_for_timeout(3000)
        # 断言进入切换日期pop页面
        assert cart_page.FE.ele(mweb_common_ele.ele_delivery_date_popup).is_visible()
        # 点击切换日期
        delivery_data = cart_page.FE.eles(mweb_common_ele.ele_delivery_date)
        for index,item in enumerate(delivery_data) :
            item.click()
            if index==0:
                break

        p.wait_for_timeout(3000)
        # 断言回到购物车页面
        assert cart_page.FE.ele(mweb_cart_ele.ele_cart_normal_delivery_date).is_visible()


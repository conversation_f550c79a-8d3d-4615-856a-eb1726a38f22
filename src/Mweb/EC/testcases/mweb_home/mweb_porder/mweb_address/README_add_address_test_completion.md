# test_110841_mWeb_add_address_from_home_page_ui_ux 测试用例补充

## 补充概述

根据提供的测试步骤，完整补充了 `test_110841_mWeb_add_address_from_home_page_ui_ux` 测试用例，实现了从首页添加地址的完整流程验证。

## 测试步骤实现

### 1. 点击首页zipcode，弹出Deliver to pop
- **元素定位**: `data-testid="wid-modal-zip-code-and-eta"`
- **验证**: 确认Deliver to弹窗正确显示
- **日志记录**: 详细的步骤日志

### 2. 点击新增地址按钮，弹出delivery address pop
- **元素定位**: `mweb_address.deliver_to_pop_add_btn` ("btn-add-address")
- **验证**: 确认新增地址按钮可见并可点击
- **等待**: 3秒等待页面响应

### 3. 在delivery address pop里输入地址
- **输入地址**: "18607 Bothell Way NE, Bothell, WA 98011"
- **元素定位**: `get_by_placeholder("Street Address")`
- **等待**: 2秒等待地址联想

### 4. 点击第一个联想地址，进入Delivery Info新增地址pop
- **元素定位**: `mweb_address.address_first_matched` ("streetAddressList")
- **操作**: 点击第一个联想地址
- **等待**: 3秒等待页面跳转

### 5. 输入姓名和电话
- **名字**: `mweb_address.address_first_name` ("wid-input-first-name") - 填入"Test"
- **姓氏**: `mweb_address.address_last_name` ("wid-input-last-name") - 填入"Automation"
- **电话**: `mweb_address.address_phone` ("wid-input-phone") - 填入"5555555555"

### 6. 验证街道、城市、zipcode已默认填写
- **街道地址**: `mweb_address.address_street` ("wid-input-street")
- **城市**: `get_by_placeholder("City")`
- **邮编**: `mweb_address.address_zipcode` ("wid-input-zipcode")
- **验证**: 确认所有字段都有值

### 7. 点击保存，回到首页
- **保存按钮**: `mweb_address.address_save_button` ("btn-save-address")
- **验证**: 确认成功回到首页
- **首页验证**: 通过首页分类按钮验证

## 使用的页面元素

从 `mweb_address.py` 文件中使用的元素：

```python
# Deliver to pop
deliver_to_pop_add_btn = "btn-add-address"

# 地址联想
address_first_matched = "streetAddressList"

# Delivery Info 表单
address_first_name = "wid-input-first-name"
address_last_name = "wid-input-last-name"
address_phone = "wid-input-phone"
address_street = "wid-input-street"
address_zipcode = "wid-input-zipcode"
address_save_button = "btn-save-address"
```

## 新增的导入

```python
from src.Mweb.EC.mweb_ele.mweb_home.mweb_porder.mweb_address import mweb_address
from src.config.weee.log_help import log
```

## 测试流程特点

### 1. 完整的用户流程
- ✅ 从首页开始的完整用户体验
- ✅ 真实的地址输入和联想选择
- ✅ 表单自动填充验证
- ✅ 保存后的页面跳转验证

### 2. 详细的验证点
- ✅ 每个步骤都有元素可见性验证
- ✅ 弹窗显示验证
- ✅ 表单字段自动填充验证
- ✅ 最终结果验证

### 3. 错误处理
- ✅ 清晰的断言错误信息
- ✅ 每个步骤的详细日志记录
- ✅ 适当的等待时间

### 4. 使用get_by_test_id
- ✅ 所有元素都使用test-id定位
- ✅ 符合最佳实践
- ✅ 提高测试稳定性

## 测试数据

- **测试地址**: "18607 Bothell Way NE, Bothell, WA 98011"
- **测试姓名**: "Test Automation"
- **测试电话**: "5555555555"

## 验证点总结

1. **UI元素验证**: 所有关键元素的可见性
2. **交互验证**: 点击、输入等操作的响应
3. **数据验证**: 表单字段的自动填充
4. **流程验证**: 完整的添加地址流程
5. **结果验证**: 成功保存并返回首页

## 兼容性

- 完全使用test-id进行元素定位
- 兼容移动端页面布局
- 适当的等待时间确保页面加载
- 清晰的错误信息便于调试

这个测试用例现在提供了完整的从首页添加地址功能验证，覆盖了用户的真实使用场景，确保地址添加功能的正确性和用户体验。

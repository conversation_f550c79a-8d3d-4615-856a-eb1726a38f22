import allure
import pytest

from playwright.sync_api import Page

from src.Mweb.EC.mweb_ele import mweb_common_ele
from src.Mweb.EC.mweb_ele.mweb_cart import mweb_cart_ele
from src.Mweb.EC.mweb_ele.mweb_home.mweb_porder.mweb_address import mweb_address
from src.Mweb.EC.mweb_pages.mweb_page_cart.mweb_page_cart import MWebCartPage
from src.Mweb.EC.mweb_pages.mweb_page_home.mweb_page_home import MWebPageHome
from src.common.commonui import scroll_one_page_until
from src.config.weee.log_help import log


@allure.story("【110841】首页/checkout/acccount/order detail-新增&应用地址验证")
class TestMWebAddAddressUIUX:
    pytestmark = [pytest.mark.h5order, pytest.mark.mweb_regression, pytest.mark.transaction, pytest.mark.suqin]

    @allure.title("【110841】 从首页-添加地址验证")
    def test_110841_mWeb_add_address_from_home_page_ui_ux(self, phone_page: dict,
                                                          h5_autotest_header,
                                                          h5_open_and_close_trace):
        """
        【110841】 从首页-添加地址验证
        # 1. 点击首页zipcode，data-testid= "wid-modal-zip-code-and-eta" ，弹出Deliver to pop
        # 2.点击新增地址按钮 deliver_to_pop_add_btn,弹出delivery address pop
        # 4. 在delivery address pop 里输入 “18607 Bothell Way NE, Bothell, WA 98011”
        # 5. 地址会联想，点击第一个地址，进入 Delivery Info 新增地址pop
        # 3.按照页面输入框，输入 姓名、电话
        # 4.其中街道、城市、zipcode 会默认已经填上了
        # 4. 点击保存，回到首页
        """
        p: Page = phone_page.get("page")
        c = phone_page.get("context")

        # 直接进入指定页面
        home_page = MWebPageHome(p, h5_autotest_header, browser_context=c)
        p.wait_for_timeout(3000)

        # 1. 点击首页zipcode，data-testid= "wid-modal-zip-code-and-eta" ，弹出Deliver to pop
        log.info("步骤1：点击首页zipcode，弹出Deliver to pop")
        zipcode_btn = p.get_by_test_id("wid-modal-zip-code-and-eta")
        assert zipcode_btn.is_visible(), "首页zipcode按钮不可见"
        zipcode_btn.click()
        p.wait_for_timeout(3000)

        # 验证Deliver to pop是否弹出
        deliver_to_popup = p.locator("//div[text()='Deliver to']")
        assert deliver_to_popup.is_visible(), "Deliver to弹窗未正确显示"
        log.info("Deliver to弹窗已正确显示")

        # 2. 点击新增地址按钮 deliver_to_pop_add_btn,弹出delivery address pop
        log.info("步骤2：点击新增地址按钮")
        add_address_btn = p.get_by_test_id(mweb_address.deliver_to_pop_add_btn)
        assert add_address_btn.is_visible(), "新增地址按钮不可见"
        add_address_btn.click()
        p.wait_for_timeout(3000)

        # 3. 在delivery address pop 里输入 "18607 Bothell Way NE, Bothell, WA 98011"
        log.info("步骤3：在delivery address pop里输入地址")
        street_input = p.get_by_placeholder("Street Address")
        assert street_input.is_visible(), "街道地址输入框不可见"
        street_input.fill("18607 Bothell Way NE, Bothell, WA 98011")
        p.wait_for_timeout(2000)

        # 4. 地址会联想，点击第一个地址，进入 Delivery Info 新增地址pop
        log.info("步骤4：点击第一个联想地址")
        first_matched_address = p.get_by_test_id(mweb_address.address_first_matched).first
        assert first_matched_address.is_visible(), "第一个联想地址不可见"
        first_matched_address.click()
        p.wait_for_timeout(3000)

        # 5. 按照页面输入框，输入 姓名、电话
        log.info("步骤5：输入姓名和电话")

        # 输入名字
        first_name_input = p.get_by_test_id(mweb_address.address_first_name)
        assert first_name_input.is_visible(), "名字输入框不可见"
        first_name_input.fill("Test")

        # 输入姓氏
        last_name_input = p.get_by_test_id(mweb_address.address_last_name)
        assert last_name_input.is_visible(), "姓氏输入框不可见"
        last_name_input.fill("Automation")

        # 输入电话
        phone_input = p.get_by_test_id(mweb_address.address_phone)
        assert phone_input.is_visible(), "电话输入框不可见"
        phone_input.fill("5555555555")

        # 验证街道、城市、zipcode 已经默认填上了
        log.info("步骤6：验证街道、城市、zipcode已默认填写")
        street_field = p.get_by_test_id(mweb_address.address_street)
        city_field = p.get_by_placeholder("City")
        zipcode_field = p.get_by_test_id(mweb_address.address_zipcode)

        assert street_field.input_value(), "街道地址未自动填写"
        assert city_field.input_value(), "城市未自动填写"
        assert zipcode_field.input_value(), "邮编未自动填写"
        log.info("街道、城市、邮编已自动填写完成")

        # 6. 点击保存，回到首页
        log.info("步骤7：点击保存按钮")
        save_btn = p.get_by_test_id(mweb_address.address_save_button)
        assert save_btn.is_visible(), "保存按钮不可见"
        save_btn.click()
        p.wait_for_timeout(5000)

        # 验证是否回到首页
        log.info("验证是否成功回到首页")
        # 可以通过检查首页特有的元素来验证
        home_indicator = p.locator("//div[@data-testid='wid-categories-item-0']")  # 首页分类按钮
        assert home_indicator.is_visible(), "未成功回到首页"

        log.info("从首页添加地址测试完成")




import allure
import pytest

from playwright.sync_api import Page

from src.Mweb.EC.mweb_ele import mweb_common_ele
from src.Mweb.EC.mweb_ele.mweb_cart import mweb_cart_ele
from src.Mweb.EC.mweb_pages.mweb_page_cart.mweb_page_cart import MWebCartPage
from src.Mweb.EC.mweb_pages.mweb_page_home.mweb_page_home import MWebPageHome
from src.common.commonui import scroll_one_page_until


@allure.story("【110841】首页/checkout/acccount/order detail-新增&应用地址验证")
class TestMWebAddAddressUIUX:
    pytestmark = [pytest.mark.h5order, pytest.mark.mweb_regression, pytest.mark.transaction, pytest.mark.suqin]

    @allure.title("【110841】 从首页-添加地址验证")
    def test_110841_mWeb_add_address_from_home_page_ui_ux(self, phone_page: dict,
                                                          h5_autotest_header,
                                                          h5_open_and_close_trace):
        """
        【110841】 从首页-添加地址验证
        """
        p: Page = phone_page.get("page")
        c = phone_page.get("context")
        # 直接进入指定页面
        home_page = MWebPageHome(p, h5_autotest_header, browser_context=c)
        p.wait_for_timeout(3000)
        # 1. 点击首页zipcode，data-testid= "wid-modal-zip-code-and-eta" ，弹出zipcode pop
        # 2.点击新增地址按钮 zipcode_pop_add_btn,弹出delivery address pop
        # 4. 在delivery address pop 里输入 “18607 Bothell Way NE, Bothell, WA 98011”
        # 5. 地址会联想，点击第一个地址，进入 Delivery Info 新增地址pop
        # 3.按照页面输入框，输入 姓名、电话、街道、城市
        4. 点击保存



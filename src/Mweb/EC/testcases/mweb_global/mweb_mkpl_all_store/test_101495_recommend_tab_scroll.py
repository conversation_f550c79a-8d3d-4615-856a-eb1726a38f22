"""
H5-Marketplace All Store 推荐Tab滚动功能测试

测试场景：
1. 访问页面https://www.sayweee.com/zh/mkpl/global?mode=sub_page&hide_activity_pop=1
2. 等待页面加载完成
3. 检查是否有弹窗，有弹窗则关闭
4. 点击推荐tab
5. 在推荐tab下滚动一页
6. 验证滚动效果
"""

import allure
import pytest
from playwright.sync_api import Page
from src.Mweb.EC.mweb_pages.mweb_page_mkpl.mweb_page_mkpl_all_store.mweb_page_mkpl_all_store_tabs_click import MWebMkplAllStorePage
from src.config.weee.log_help import log


@allure.story("H5-Marketplace All Store 推荐Tab滚动功能")
class TestRecommendTabScroll:
    pytestmark = [pytest.mark.mweb_todo]

    @allure.title("【101495】 H5-Marketplace All Store 推荐Tab下滚动一页")
    @pytest.mark.h5home
    def test_101495_recommend_tab_scroll_one_page(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):
        """
        【101495】 H5-Marketplace All Store 推荐Tab下滚动一页
        测试步骤：
        1. 访问页面 https://www.sayweee.com/zh/mkpl/global?mode=sub_page&hide_activity_pop=1
        2. 等待页面加载完成
        3. 检查是否有弹窗，有弹窗则关闭
        4. 点击推荐tab
        5. 在推荐tab下滚动一页
        6. 验证滚动效果
        """
        _page: Page = phone_page["page"]
        _context = phone_page["context"]

        # 创建全球购所有商店页面操作类实例
        all_store_page = MWebMkplAllStorePage(_page, h5_autotest_header, _context)

        # 获取初始滚动位置
        with allure.step("获取初始页面滚动位置"):
            initial_scroll_y = _page.evaluate("window.pageYOffset")
            log.info(f"初始滚动位置: {initial_scroll_y}px")

        # 点击推荐tab并滚动一页
        with allure.step("点击推荐tab并在推荐tab下滚动一页"):
            all_store_page.click_recommend_tab_and_scroll()

        # 验证滚动效果
        with allure.step("验证滚动效果"):
            final_scroll_y = _page.evaluate("window.pageYOffset")
            scroll_distance = final_scroll_y - initial_scroll_y
            
            log.info(f"最终滚动位置: {final_scroll_y}px")
            log.info(f"实际滚动距离: {scroll_distance}px")
            
            # 验证确实发生了滚动
            assert scroll_distance > 0, f"应该发生滚动，实际滚动距离: {scroll_distance}px"
            
            log.info(f"✅ 推荐tab下成功滚动了 {scroll_distance}px")

        log.info("【101495】推荐Tab下滚动一页测试完成")

    @allure.title("【101495-1】 H5-Marketplace All Store 推荐Tab点击功能验证")
    @pytest.mark.h5home
    def test_101495_recommend_tab_click_only(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):
        """
        【101495-1】 H5-Marketplace All Store 推荐Tab点击功能验证
        测试步骤：
        1. 访问页面并完成初始化
        2. 点击推荐tab
        3. 验证tab点击是否成功
        """
        _page: Page = phone_page["page"]
        _context = phone_page["context"]

        # 创建页面操作实例
        all_store_page = MWebMkplAllStorePage(_page, h5_autotest_header, _context)

        # 点击推荐tab
        with allure.step("点击推荐tab"):
            all_store_page.click_recommend_tab()
            log.info("✅ 推荐tab点击完成")

        log.info("【101495-1】推荐Tab点击功能验证完成")

    @allure.title("【101495-2】 H5-Marketplace All Store 推荐Tab下单独滚动功能验证")
    @pytest.mark.h5home
    def test_101495_recommend_tab_scroll_only(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):
        """
        【101495-2】 H5-Marketplace All Store 推荐Tab下单独滚动功能验证
        测试步骤：
        1. 访问页面并完成初始化
        2. 先点击推荐tab
        3. 单独执行滚动一页功能
        4. 验证滚动效果
        """
        _page: Page = phone_page["page"]
        _context = phone_page["context"]

        # 创建页面操作实例
        all_store_page = MWebMkplAllStorePage(_page, h5_autotest_header, _context)

        # 先点击推荐tab
        with allure.step("先点击推荐tab"):
            all_store_page.click_recommend_tab()

        # 记录滚动前位置
        with allure.step("记录滚动前位置"):
            before_scroll_y = _page.evaluate("window.pageYOffset")
            viewport_height = _page.evaluate("window.innerHeight")
            log.info(f"滚动前位置: {before_scroll_y}px")
            log.info(f"视窗高度: {viewport_height}px")

        # 执行滚动
        with allure.step("在推荐tab下执行滚动一页"):
            scroll_result = all_store_page.scroll_down_one_page_in_recommend_tab()
            
            # 验证滚动结果
            assert scroll_result, "滚动操作应该成功"

        # 验证滚动效果
        with allure.step("验证滚动效果"):
            after_scroll_y = _page.evaluate("window.pageYOffset")
            actual_scroll_distance = after_scroll_y - before_scroll_y
            
            log.info(f"滚动后位置: {after_scroll_y}px")
            log.info(f"实际滚动距离: {actual_scroll_distance}px")
            
            # 验证滚动距离合理（应该接近一个视窗高度）
            expected_min_scroll = viewport_height * 0.5  # 至少滚动半个视窗高度
            assert actual_scroll_distance >= expected_min_scroll, f"滚动距离应该至少 {expected_min_scroll}px，实际 {actual_scroll_distance}px"
            
            log.info(f"✅ 滚动验证通过，滚动距离: {actual_scroll_distance}px")

        log.info("【101495-2】推荐Tab下单独滚动功能验证完成")

    @allure.title("【101495-3】 H5-Marketplace All Store 推荐Tab多次滚动测试")
    @pytest.mark.h5home
    def test_101495_recommend_tab_multiple_scrolls(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):
        """
        【101495-3】 H5-Marketplace All Store 推荐Tab多次滚动测试
        测试步骤：
        1. 访问页面并完成初始化
        2. 点击推荐tab
        3. 执行多次滚动操作
        4. 验证每次滚动的效果
        5. 检查累积滚动效果
        """
        _page: Page = phone_page["page"]
        _context = phone_page["context"]

        # 创建页面操作实例
        all_store_page = MWebMkplAllStorePage(_page, h5_autotest_header, _context)

        # 点击推荐tab
        with allure.step("点击推荐tab"):
            all_store_page.click_recommend_tab()

        # 记录初始状态
        with allure.step("记录初始状态"):
            initial_scroll_y = _page.evaluate("window.pageYOffset")
            viewport_height = _page.evaluate("window.innerHeight")
            log.info(f"初始滚动位置: {initial_scroll_y}px")
            log.info(f"视窗高度: {viewport_height}px")

        # 执行多次滚动
        scroll_results = []
        max_scrolls = 3  # 最多滚动3次

        with allure.step(f"执行{max_scrolls}次滚动操作"):
            for i in range(max_scrolls):
                log.info(f"🔄 执行第{i+1}次滚动")
                
                # 获取滚动前位置
                before_scroll = _page.evaluate("window.pageYOffset")
                
                # 执行滚动
                scroll_success = all_store_page.scroll_down_one_page_in_recommend_tab()
                
                # 获取滚动后位置
                after_scroll = _page.evaluate("window.pageYOffset")
                
                # 计算滚动距离
                scroll_distance = after_scroll - before_scroll
                
                # 记录结果
                scroll_results.append({
                    "scroll_number": i + 1,
                    "success": scroll_success,
                    "before_y": before_scroll,
                    "after_y": after_scroll,
                    "scroll_distance": scroll_distance
                })
                
                log.info(f"第{i+1}次滚动结果: 成功={scroll_success}, 滚动距离={scroll_distance}px")
                
                # 如果滚动失败，停止继续滚动
                if not scroll_success:
                    log.info(f"第{i+1}次滚动失败，停止继续滚动")
                    break
                
                # 等待一下再进行下次滚动
                _page.wait_for_timeout(1000)

        # 验证滚动效果
        with allure.step("验证多次滚动的累积效果"):
            final_scroll_y = _page.evaluate("window.pageYOffset")
            total_scroll_distance = final_scroll_y - initial_scroll_y
            
            log.info(f"📊 多次滚动测试总结:")
            log.info(f"  总滚动次数: {len(scroll_results)}")
            log.info(f"  成功滚动次数: {sum(1 for r in scroll_results if r['success'])}")
            log.info(f"  总滚动距离: {total_scroll_distance}px")
            log.info(f"  平均每次滚动距离: {total_scroll_distance / len(scroll_results) if scroll_results else 0:.1f}px")
            
            # 验证至少有一次滚动成功
            successful_scrolls = [r for r in scroll_results if r['success']]
            assert len(successful_scrolls) > 0, "至少应该有一次滚动成功"
            
            # 验证总滚动距离合理
            assert total_scroll_distance > 0, f"总滚动距离应该大于0，实际: {total_scroll_distance}px"
            
            log.info("✅ 多次滚动测试验证通过")

        log.info("【100836-3】推荐Tab多次滚动测试完成")

    @allure.title("【100836-4】 H5-Marketplace All Store 推荐Tab滚动边界测试")
    @pytest.mark.h5home
    def test_101495_recommend_tab_scroll_boundary(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):
        """
        【101495-4】 H5-Marketplace All Store 推荐Tab滚动边界测试
        测试步骤：
        1. 访问页面并完成初始化
        2. 点击推荐tab
        3. 连续滚动直到页面底部
        4. 验证边界情况的处理
        """
        _page: Page = phone_page["page"]
        _context = phone_page["context"]

        # 创建页面操作实例
        all_store_page = MWebMkplAllStorePage(_page, h5_autotest_header, _context)

        # 点击推荐tab
        with allure.step("点击推荐tab"):
            all_store_page.click_recommend_tab()

        # 获取页面总高度
        with allure.step("获取页面信息"):
            page_height = _page.evaluate("document.documentElement.scrollHeight")
            viewport_height = _page.evaluate("window.innerHeight")
            initial_scroll_y = _page.evaluate("window.pageYOffset")
            
            log.info(f"页面总高度: {page_height}px")
            log.info(f"视窗高度: {viewport_height}px")
            log.info(f"初始滚动位置: {initial_scroll_y}px")
            log.info(f"可滚动距离: {page_height - viewport_height}px")

        # 连续滚动直到无法滚动
        with allure.step("连续滚动直到页面底部"):
            scroll_count = 0
            max_attempts = 10  # 最多尝试10次，防止无限循环
            last_scroll_y = initial_scroll_y
            
            while scroll_count < max_attempts:
                scroll_count += 1
                log.info(f"🔄 尝试第{scroll_count}次滚动")
                
                # 执行滚动
                scroll_success = all_store_page.scroll_down_one_page_in_recommend_tab()
                
                # 获取当前位置
                current_scroll_y = _page.evaluate("window.pageYOffset")
                
                # 如果滚动失败或位置没有变化，说明到达底部
                if not scroll_success or current_scroll_y == last_scroll_y:
                    log.info(f"第{scroll_count}次滚动后到达页面底部或滚动失败")
                    break
                
                last_scroll_y = current_scroll_y
                log.info(f"第{scroll_count}次滚动后位置: {current_scroll_y}px")
                
                # 等待一下
                _page.wait_for_timeout(500)

        # 验证最终状态
        with allure.step("验证最终滚动状态"):
            final_scroll_y = _page.evaluate("window.pageYOffset")
            total_scrolled = final_scroll_y - initial_scroll_y
            
            log.info(f"📊 边界滚动测试总结:")
            log.info(f"  总滚动次数: {scroll_count}")
            log.info(f"  最终滚动位置: {final_scroll_y}px")
            log.info(f"  总滚动距离: {total_scrolled}px")
            
            # 验证滚动确实发生了
            assert total_scrolled > 0, f"应该发生滚动，实际滚动距离: {total_scrolled}px"
            
            log.info("✅ 边界滚动测试验证通过")

        log.info("【101495-4】推荐Tab滚动边界测试完成")

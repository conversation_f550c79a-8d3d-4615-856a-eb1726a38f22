{"name": "【106444】 topx-- 分享流程验证", "status": "passed", "description": "\n        【106444】 topx-- 分享流程验证\n        ", "start": 1742958639358, "stop": 1742958664923, "uuid": "d0292b6b-80d4-47e0-aa3f-666ce6f0b638", "historyId": "e16989cf4a523133edebc5ee49c6c0bd", "testCaseId": "e16989cf4a523133edebc5ee49c6c0bd", "fullName": "src.Mweb.EC.testcases.mweb_topx.test_106444_mWeb_topx_share_ui_ux.TestMWebPDPProductShareUIUX#test_108209_mWeb_pdp_product_share_ui_ux", "labels": [{"name": "story", "value": "【106444】 topx-- 分享流程验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "h5pdp"}, {"name": "tag", "value": "regression"}, {"name": "tag", "value": "transaction"}, {"name": "tag", "value": "suqin"}, {"name": "parentSuite", "value": "src.Mweb.EC.testcases.mweb_topx"}, {"name": "suite", "value": "test_106444_mWeb_topx_share_ui_ux"}, {"name": "subSuite", "value": "TestMWebPDPProductShareUIUX"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "11764-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Mweb.EC.testcases.mweb_topx.test_106444_mWeb_topx_share_ui_ux"}]}
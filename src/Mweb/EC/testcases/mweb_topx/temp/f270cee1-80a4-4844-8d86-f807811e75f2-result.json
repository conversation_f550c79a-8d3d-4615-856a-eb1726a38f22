{"name": "【106444】 topx-- 分享流程验证", "status": "failed", "statusDetails": {"message": "AssertionError: assert 'Ranking charts updated daily' == 'Top choice for Asian groceries.'\n  - Top choice for Asian groceries.\n  + Ranking charts updated daily", "trace": "self = <test_106444_mWeb_topx_share_ui_ux.TestMWebPDPProductShareUIUX object at 0x00000205A8C8CDD0>\nphone_page = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppDat...ome.exe> version=120.0.6099.28>>, 'page': <Page url='https://www.sayweee.com/en/promotion/top-x/chart?joinEnki=false'>}\nh5_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...XPF89iDpIgCCKO9rc6_Xsxvw6UpQ_nH_LMjZLmP_EdV16P23z2iQL4h9ysnYqsWp79BNfviNdAVPN_dBlSCj7Xva0oZGMi-b8gZI1CYjPCtMSY4I', ...}\nh5_open_and_close_trace = None\n\n    @allure.title(\"【106444】 topx-- 分享流程验证\")\n    def test_108209_mWeb_pdp_product_share_ui_ux(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):\n        \"\"\"\n        【106444】 topx-- 分享流程验证\n        \"\"\"\n        p: Page = phone_page.get(\"page\")\n        c = phone_page.get(\"context\")\n        # 直接进入指定topx页面\n        pdp_page = PDPPage(p, h5_autotest_header, browser_context=c,\n                           page_url=\"/promotion/top-x/chart\")\n        p.wait_for_timeout(3000)\n        # 关闭Continue pop\n        # p.locator(u\"//button[contains(text(), 'Continue')]\").click()\n        # 滚动到指定位置-分享按钮\n        scroll_one_page_until(p, mweb_topx_ele.ele_share_topx)\n    \n        # 点击分享按钮\n        p.get_by_test_id(\"btn-share\").click()\n        p.wait_for_timeout(3000)\n        # 断言分享pop 弹出成功\n        assert pdp_page.FE.ele(mweb_topx_ele.ele_share_topx_pop).is_visible(), 'pdp点击分享未弹出pop'\n        # 断言分享pop title\n        assert pdp_page.FE.ele(mweb_topx_ele.ele_share_pop_title).text_content() == \"Share\"\n        # 断言分享商品小图\n        assert pdp_page.FE.ele(mweb_topx_ele.ele_share_pop_topx_img + \"//img\").is_visible()\n        # 断言分享pop 商品title\n        pop_product_title = pdp_page.FE.ele(\n            mweb_topx_ele.ele_share_pop_topx_img + \"//following-sibling::div//div[1]\").text_content()\n        assert pop_product_title\n        # 断言分享pop 商品子title\n        pop_product_sub_title = pdp_page.FE.ele(\n            mweb_topx_ele.ele_share_pop_topx_img + \"//following-sibling::div//div[2]\").text_content()\n>       assert pop_product_sub_title == \"Top choice for Asian groceries.\"\nE       AssertionError: assert 'Ranking charts updated daily' == 'Top choice for Asian groceries.'\nE         - Top choice for Asian groceries.\nE         + Ranking charts updated daily\n\ntest_106444_mWeb_topx_share_ui_ux.py:47: AssertionError"}, "description": "\n        【106444】 topx-- 分享流程验证\n        ", "start": 1742958546100, "stop": 1742958565678, "uuid": "8d95b6bc-eb98-4304-8707-cd76af85ab65", "historyId": "e16989cf4a523133edebc5ee49c6c0bd", "testCaseId": "e16989cf4a523133edebc5ee49c6c0bd", "fullName": "src.Mweb.EC.testcases.mweb_topx.test_106444_mWeb_topx_share_ui_ux.TestMWebPDPProductShareUIUX#test_108209_mWeb_pdp_product_share_ui_ux", "labels": [{"name": "story", "value": "【106444】 topx-- 分享流程验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "h5pdp"}, {"name": "tag", "value": "regression"}, {"name": "tag", "value": "transaction"}, {"name": "tag", "value": "suqin"}, {"name": "parentSuite", "value": "src.Mweb.EC.testcases.mweb_topx"}, {"name": "suite", "value": "test_106444_mWeb_topx_share_ui_ux"}, {"name": "subSuite", "value": "TestMWebPDPProductShareUIUX"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "10904-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Mweb.EC.testcases.mweb_topx.test_106444_mWeb_topx_share_ui_ux"}]}
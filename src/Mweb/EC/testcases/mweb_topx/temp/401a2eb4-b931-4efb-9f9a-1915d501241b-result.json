{"name": "【106444】 topx分享流程验证", "status": "passed", "description": "\n        【106444】 topx分享流程验证\n        ", "start": 1744359270022, "stop": 1744359293935, "uuid": "5f3dce85-5497-4c4e-8e15-f124f940bd26", "historyId": "1d2952ff3a78fb7de2b170f69a664af4", "testCaseId": "1d2952ff3a78fb7de2b170f69a664af4", "fullName": "src.Mweb.EC.testcases.mweb_topx.test_106444_mWeb_topx_share_ui_ux.TestMWebTopxShareUIUX#test_106444_mWeb_topx_share_ui_ux", "labels": [{"name": "story", "value": "【106444】 topx分享流程验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "h5pdp"}, {"name": "tag", "value": "mweb_regression"}, {"name": "tag", "value": "transaction"}, {"name": "tag", "value": "suqin"}, {"name": "parentSuite", "value": "src.Mweb.EC.testcases.mweb_topx"}, {"name": "suite", "value": "test_106444_mWeb_topx_share_ui_ux"}, {"name": "subSuite", "value": "TestMWebTopxShareUIUX"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "19076-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Mweb.EC.testcases.mweb_topx.test_106444_mWeb_topx_share_ui_ux"}]}
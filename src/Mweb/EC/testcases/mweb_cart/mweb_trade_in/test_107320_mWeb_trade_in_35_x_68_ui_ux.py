import allure
import pytest
from playwright.sync_api import Page

from src.Mweb.EC.mweb_ele.mweb_cart import mweb_cart_ele
from src.Mweb.EC.mweb_ele.mweb_cart import mweb_trade_in_ele
from src.Mweb.EC.mweb_ele.mweb_cart import mweb_recommendations_ele
from src.Mweb.EC.mweb_pages.mweb_page_cart.mweb_page_cart import MWebCartPage
from src.Mweb.EC.mweb_pages.mweb_page_cart.mweb_trade_in_page import MWebTradeInPage
from src.common.commonui import scroll_one_page_until
from src.config.weee.log_help import log
from src.common.commfunc import empty_cart, CommonCheck


@allure.story("【107320】 $35<X<$68-换购页面UI/UX验证")
class TestMWebTradeInUIUX:
    pytestmark = [pytest.mark.h5cart, pytest.mark.mweb_regression, pytest.mark.transaction]

    @allure.title("【107320】 $35<X<$68-换购页面UI/UX验证")
    def test_107320_mWeb_trade_in_35_x_68_ui_ux(self, phone_page: dict, h5_autotest_header):
        """
        【107320】 $35<X<$68-换购页面UI/UX验证
        测试步骤：
        1. 清空购物车后从推荐模块加购商品
        2. 验证购物车换购模块状态
        3. 进入换购页面，验证按钮状态和提示信息
        4. 进入凑单页面，验证各项功能
        5. 完成凑单后返回购物车，验证状态
        """
        p: Page = phone_page.get("page")
        c = phone_page.get("context")

        # 初始化页面和清空购物车
        try:
            CommonCheck().set_porder(h5_autotest_header, 98011)
            empty_cart(h5_autotest_header)
        except Exception as e:
            log.info(f"初始化失败: {str(e)}")

        # 初始化页面对象
        cart_page = MWebCartPage(p, h5_autotest_header, browser_context=c, page_url="/cart")
        trade_in_page = MWebTradeInPage(p)

        # 1. 从推荐模块加购商品
        scroll_one_page_until(p, mweb_cart_ele.ele_recommend_tab)
        recommend_card = cart_page.FE.eles(mweb_cart_ele.ele_recommend_module_card)

        for item in recommend_card:
            item.query_selector(u"//div[@data-testid='btn-atc-plus']").click()
            p.wait_for_timeout(3000)
            # 回到购物车第一个商品位置
            p.query_selector(f"{mweb_cart_ele.ele_cart_normal_card}" + "[1]").scroll_into_view_if_needed()
            p.wait_for_timeout(2000)
            total_fee = cart_page.FE.ele(mweb_cart_ele.ele_cart_normal_total + u"//span[2]").text_content().split("$")[
                1]
            print(total_fee)
            # 如果商品金额小于35，不显示购物车换购模块
            if float(total_fee) < 35:
                # ele_cart_trade_in = cart_page.FE.ele(mweb_cart_ele.ele_cart_trade_in)
                assert not cart_page.FE.ele(mweb_cart_ele.ele_cart_trade_in)
            # 如果商品金额大于35，小于68，显示购物车换购模块
            elif 35 <= float(total_fee) < 68:
                # 断言换购模块存在
                assert cart_page.FE.ele(mweb_cart_ele.ele_cart_trade_in).is_visible()
                # 断言 换购卡片上不可加购
                assert cart_page.FE.ele(
                    mweb_cart_ele.ele_cart_trade_in_card + u"//div[contains(@class,'product-card-btn-shadow')]").is_visible()
                # 点击查看更多，跳转换购页，然后再返回购物车
                cart_page.FE.ele(mweb_cart_ele.ele_cart_trade_in_button).click()
                # 断言进入换购页面
                assert cart_page.FE.ele(mweb_cart_ele.ele_trade_in).is_visible()
                # 断言换购页面上方的文案
                discount_wrap = cart_page.FE.ele(mweb_cart_ele.ele_trade_in_banner)
                # 断言 "Up to $25+ off!" 文本存在
                up_to_25_off_text = discount_wrap.query_selector('text="Up to $25+ off!"')
                assert up_to_25_off_text
                # 断言 "Enjoy discounts only if you add items from this list to your cart." 文本存在
                enjoy_discounts_text = discount_wrap.query_selector(
                    'text="Enjoy discounts only if you add items from this list to your cart."')
                assert enjoy_discounts_text

                # 断言换购页面不可加购
                trade_in_card = cart_page.FE.eles(mweb_cart_ele.ele_trade_in_card)
                # 点击不可加购按钮，弹出pop 提示
                for index2, item2 in enumerate(trade_in_card):
                    # 断言卡片基本信息
                    assert item2.query_selector(u"//div[@data-role='image-container']//img")
                    # 定位折扣标签
                    discount_label = item2.query_selector('span:has-text("% OFF")')
                    assert discount_label
                    title = item2.query_selector(mweb_cart_ele.ele_trade_in_card_title)
                    # 定位产品标题
                    product_title = title.query_selector(u'[@data-role="product-title"]')
                    assert product_title
                    price = item2.query_selector(mweb_cart_ele.ele_trade_in_card_price)
                    # 定位当前价格
                    current_price = price.query_selector(u'[@data-role="text"]')
                    assert current_price
                    # 定位划线价格
                    base_price = price.query_selector('.enki-body-sm.text-surface-100-fg-minor.line-through')
                    assert base_price
                    # 定位搜藏按钮并点击
                    item2.query_selector(u"//div[@data-testid='btn-favorite']").click()
                    # 订单select按钮并点击
                    item2.query_selector(u"//button").click()
                    # assert p.query_selector()
                    # 点击商品卡片

                    if index2 == 0:
                        break

            # 如果商品金额大于68，换购可加购
            elif float(total_fee) >= 68:
                assert cart_page.FE.ele(mweb_cart_ele.ele_cart_trade_in).is_visible()
                # 断言 换购卡片上可加购
                assert cart_page.FE.ele(mweb_cart_ele.ele_cart_trade_in_card_add).is_visible()
                # 点击加购换购商品
                cart_page.FE.ele(mweb_cart_ele.ele_cart_trade_in_card_add).click()
                # 断言换购商品加入购物车成功
            if index1 == 20:
                break

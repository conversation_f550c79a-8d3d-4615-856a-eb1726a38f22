import allure
import pytest

from playwright.sync_api import Page

from src.Mweb.EC.mweb_pages.mweb_order_page.mweb_order_page import MWebOrderPage
from src.common.commonui import scroll_one_page_until


@allure.story("【111625】 订单列表-再来一单功能验证")
class TestMWebPDPProductShareUIUX:
    pytestmark = [pytest.mark.h5order, pytest.mark.mweb_regression, pytest.mark.transaction, pytest.mark.suqin]

    @allure.title("【111625】 订单列表-再来一单功能验证")
    def test_111625_mWeb_my_order_buy_again_ui_ux(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):
        """
        【111625】 订单列表-再来一单功能验证
        """
        p: Page = phone_page.get("page")
        c = phone_page.get("context")
        # 直接进入指定页面
        order_page = MWebOrderPage(p, h5_autotest_header, browser_context=c, page_url="/order/list")
        p.wait_for_timeout(3000)
        # 关闭Continue pop
        # p.locator(u"//button[contains(text(), 'Continue')]").click()
        # 滚动到指定位置-分享按钮
        # scroll_one_page_until(p, mweb_pdp_ele.ele_share)

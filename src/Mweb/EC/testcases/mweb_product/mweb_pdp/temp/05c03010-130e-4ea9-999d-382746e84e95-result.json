{"name": "【112063】 H5-PDP-商品组-只有一个属性模块UI/UX验证", "status": "broken", "statusDetails": {"message": "playwright._impl._errors.TimeoutError: Timeout 10000ms exceeded.", "trace": "self = <test_112063_mWeb_pdp_product_group_ui_ux.TestMWebPDPProductGroupUIUX object at 0x00000157255671D0>\nphone_page = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppDat...>>, 'page': <Page url='https://www.sayweee.com/en/product/Vita-Honey-Chrysanthemum-Tea-250ml-6/106422?joinEnki=false'>}\nh5_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...A1PcHR5XxZ9_ZQGUE6xtAjEefJcinT3xRIkPA7uEtXTJfTLPtJ5Rh9KeB8dsIUetdh9sgxhlxMIN0ozAoicdvm3cAjAcmn7QXYB3VIAk3ue_mvqE', ...}\nh5_open_and_close_trace = None\n\n    @allure.title(\"【112063】 H5-PDP-商品组-只有一个属性模块UI/UX验证\")\n    def test_112063_mWeb_pdp_product_group_ui_ux(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):\n        \"\"\"【112063】 H5-PDP-商品组-只有一个属性模块UI/UX验证\n    \n        测试步骤：\n        1. 进入指定PDP页面\n        2. 验证商品组组件的基础UI元素\n        3. 验证商品组弹窗功能\n        4. 验证商品组切换功能\n    \n        预期结果：\n        1. 商品组组件正确显示\n        2. 商品组弹窗正常显示和关闭\n        3. 商品切换功能正常\n        \"\"\"\n        # 初始化测试环境\n>       page, pdp_page = self._setup_test_environment(phone_page, h5_autotest_header)\n\ntest_112063_mWeb_pdp_product_group_ui_ux.py:31: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\ntest_112063_mWeb_pdp_product_group_ui_ux.py:67: in _setup_test_environment\n    page.wait_for_selector(mweb_pdp_ele.ele_product_group, state=\"visible\", timeout=10000)\n..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py:8334: in wait_for_selector\n    self._sync(\n..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_page.py:348: in wait_for_selector\n    return await self._main_frame.wait_for_selector(**locals_to_params(locals()))\n..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_frame.py:317: in wait_for_selector\n    await self._channel.send(\"waitForSelector\", locals_to_params(locals()))\n..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:62: in send\n    return await self._connection.wrap_api_call(\n..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:492: in wrap_api_call\n    return await cb()\n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n\nself = <playwright._impl._connection.Channel object at 0x0000015725716F90>\nmethod = 'waitForSelector'\nparams = {'selector': \"//div[@class='relative flex']\", 'state': 'visible', 'timeout': 10000}\nreturn_as_dict = False\n\n    async def inner_send(\n        self, method: str, params: Optional[Dict], return_as_dict: bool\n    ) -> Any:\n        if params is None:\n            params = {}\n        callback = self._connection._send_message_to_server(\n            self._object, method, params\n        )\n        if self._connection._error:\n            error = self._connection._error\n            self._connection._error = None\n            raise error\n        done, _ = await asyncio.wait(\n            {\n                self._connection._transport.on_error_future,\n                callback.future,\n            },\n            return_when=asyncio.FIRST_COMPLETED,\n        )\n        if not callback.future.done():\n            callback.future.cancel()\n>       result = next(iter(done)).result()\nE       playwright._impl._errors.TimeoutError: Timeout 10000ms exceeded.\n\n..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:100: TimeoutError"}, "description": "【112063】 H5-PDP-商品组-只有一个属性模块UI/UX验证\n        \n        测试步骤：\n        1. 进入指定PDP页面\n        2. 验证商品组组件的基础UI元素\n        3. 验证商品组弹窗功能\n        4. 验证商品组切换功能\n        \n        预期结果：\n        1. 商品组组件正确显示\n        2. 商品组弹窗正常显示和关闭\n        3. 商品切换功能正常\n        ", "start": 1742533868464, "stop": 1742533895257, "uuid": "3170cee4-a53a-459e-bf9d-3f6fdec67abe", "historyId": "a04e0017fe119811658bd84390ed0585", "testCaseId": "a04e0017fe119811658bd84390ed0585", "fullName": "src.Mweb.EC.testcases.mweb_pdp.test_112063_mWeb_pdp_product_group_ui_ux.TestMWebPDPProductGroupUIUX#test_112063_mWeb_pdp_product_group_ui_ux", "labels": [{"name": "story", "value": "【112063】 H5-PDP-商品组-只有一个属性模块UI/UX验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "h5pdp"}, {"name": "tag", "value": "transaction"}, {"name": "tag", "value": "suqin"}, {"name": "parentSuite", "value": "src.Mweb.EC.testcases.mweb_pdp"}, {"name": "suite", "value": "test_112063_mWeb_pdp_product_group_ui_ux"}, {"name": "subSuite", "value": "TestMWebPDPProductGroupUIUX"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "32760-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Mweb.EC.testcases.mweb_pdp.test_112063_mWeb_pdp_product_group_ui_ux"}]}
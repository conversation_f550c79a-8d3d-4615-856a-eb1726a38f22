{"name": "【112063】 H5-PDP-商品组-只有一个属性模块UI/UX验证", "status": "failed", "statusDetails": {"message": "AssertionError: 验证商品组基础UI失败: 'ElementHandle' object has no attribute 'wait_for'", "trace": "self = <test_112063_mWeb_pdp_product_group_ui_ux.TestMWebPDPProductGroupUIUX object at 0x0000025EC47EC410>\npdp_page = <src.Mweb.EC.mweb_pages.mweb_page_pdp.mweb_page_pdp.PDPPage object at 0x0000025EC4D0D250>\n\n    def _verify_product_group_basic_ui(self, pdp_page: PDPPage):\n        \"\"\"验证商品组基础UI元素\"\"\"\n        try:\n            # 增加等待时间\n            pdp_page.page.wait_for_selector(mweb_pdp_ele.ele_product_group_select, state=\"visible\", timeout=10000)\n            assert pdp_page.FE.ele(mweb_pdp_ele.ele_product_group_select).is_visible(), \"商品组选择器未显示\"\n    \n            title_element = pdp_page.FE.ele(mweb_pdp_ele.ele_product_group_title)\n>           title_element.wait_for(state=\"visible\", timeout=5000)\nE           AttributeError: 'ElementHandle' object has no attribute 'wait_for'\n\ntest_112063_mWeb_pdp_product_group_ui_ux.py:80: AttributeError\n\nDuring handling of the above exception, another exception occurred:\n\nself = <test_112063_mWeb_pdp_product_group_ui_ux.TestMWebPDPProductGroupUIUX object at 0x0000025EC47EC410>\nphone_page = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppDat...n=120.0.6099.28>>, 'page': <Page url='https://www.sayweee.com/en/product/Vita-Lemon-Tea-250ml-6/97175?joinEnki=false'>}\nh5_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...sMbpIFnjKclq7pb5uBQMSywjFkvLoAAMmHZ1gVxOPpezsezi2Wm9qA0ePKbv6yqxcQaD7cwn65Eu7pD1i05bl1C7XRGj7PznFyJnBviLyQ4wgeZE', ...}\nh5_open_and_close_trace = None\n\n    @allure.title(\"【112063】 H5-PDP-商品组-只有一个属性模块UI/UX验证\")\n    def test_112063_mWeb_pdp_product_group_ui_ux(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):\n        \"\"\"【112063】 H5-PDP-商品组-只有一个属性模块UI/UX验证\n    \n        测试步骤：\n        1. 进入指定PDP页面\n        2. 验证商品组组件的基础UI元素\n        3. 验证商品组弹窗功能\n        4. 验证商品组切换功能\n    \n        预期结果：\n        1. 商品组组件正确显示\n        2. 商品组弹窗正常显示和关闭\n        3. 商品切换功能正常\n        \"\"\"\n        # 初始化测试环境\n        page, pdp_page = self._setup_test_environment(phone_page, h5_autotest_header)\n    \n        # 验证商品组基础UI\n>       self._verify_product_group_basic_ui(pdp_page)\n\ntest_112063_mWeb_pdp_product_group_ui_ux.py:34: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n\nself = <test_112063_mWeb_pdp_product_group_ui_ux.TestMWebPDPProductGroupUIUX object at 0x0000025EC47EC410>\npdp_page = <src.Mweb.EC.mweb_pages.mweb_page_pdp.mweb_page_pdp.PDPPage object at 0x0000025EC4D0D250>\n\n    def _verify_product_group_basic_ui(self, pdp_page: PDPPage):\n        \"\"\"验证商品组基础UI元素\"\"\"\n        try:\n            # 增加等待时间\n            pdp_page.page.wait_for_selector(mweb_pdp_ele.ele_product_group_select, state=\"visible\", timeout=10000)\n            assert pdp_page.FE.ele(mweb_pdp_ele.ele_product_group_select).is_visible(), \"商品组选择器未显示\"\n    \n            title_element = pdp_page.FE.ele(mweb_pdp_ele.ele_product_group_title)\n            title_element.wait_for(state=\"visible\", timeout=5000)\n            title = title_element.text_content()\n            assert title, \"商品组标题为空\"\n        except Exception as e:\n>           raise AssertionError(f\"验证商品组基础UI失败: {str(e)}\")\nE           AssertionError: 验证商品组基础UI失败: 'ElementHandle' object has no attribute 'wait_for'\n\ntest_112063_mWeb_pdp_product_group_ui_ux.py:84: AssertionError"}, "description": "【112063】 H5-PDP-商品组-只有一个属性模块UI/UX验证\n        \n        测试步骤：\n        1. 进入指定PDP页面\n        2. 验证商品组组件的基础UI元素\n        3. 验证商品组弹窗功能\n        4. 验证商品组切换功能\n        \n        预期结果：\n        1. 商品组组件正确显示\n        2. 商品组弹窗正常显示和关闭\n        3. 商品切换功能正常\n        ", "start": 1742534007540, "stop": 1742534025039, "uuid": "77541690-f9d4-451a-b07e-7e641b02e948", "historyId": "a04e0017fe119811658bd84390ed0585", "testCaseId": "a04e0017fe119811658bd84390ed0585", "fullName": "src.Mweb.EC.testcases.mweb_pdp.test_112063_mWeb_pdp_product_group_ui_ux.TestMWebPDPProductGroupUIUX#test_112063_mWeb_pdp_product_group_ui_ux", "labels": [{"name": "story", "value": "【112063】 H5-PDP-商品组-只有一个属性模块UI/UX验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "h5pdp"}, {"name": "tag", "value": "transaction"}, {"name": "tag", "value": "suqin"}, {"name": "parentSuite", "value": "src.Mweb.EC.testcases.mweb_pdp"}, {"name": "suite", "value": "test_112063_mWeb_pdp_product_group_ui_ux"}, {"name": "subSuite", "value": "TestMWebPDPProductGroupUIUX"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "18784-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Mweb.EC.testcases.mweb_pdp.test_112063_mWeb_pdp_product_group_ui_ux"}]}
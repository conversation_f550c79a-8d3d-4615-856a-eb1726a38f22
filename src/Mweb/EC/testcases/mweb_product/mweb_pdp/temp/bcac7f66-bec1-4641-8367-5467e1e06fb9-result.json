{"name": "【108701】 PDP-分享-商品图片分享UI/UX验证", "status": "failed", "statusDetails": {"message": "AssertionError: assert <JSHandle preview=JSHandle@<p>Asian Groceries</p>> == 'Asian Groceries'", "trace": "self = <test_108701_mWeb_pdp_product_image_share_ui_ux.TestMWebPDPProductImgShareUIUX object at 0x00000248F0698B50>\nphone_page = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppDat...>>, 'page': <Page url='https://www.sayweee.com/en/product/Vita-Honey-Chrysanthemum-Tea-250ml-6/106422?joinEnki=false'>}\nh5_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...bYrOJuGfyL502imDmCWyGsAQOuEScb50Yj65A4qNFaYHNLipMPJUNh1YnIWTiHo7GW0ag1Kti8d27rjUf5OiaGfojdYKl3XwG4zyEpRduC4v30zM', ...}\nh5_open_and_close_trace = None\n\n    @allure.title(\"【108701】 PDP-分享-商品图片分享UI/UX验证\")\n    def test_112063_mWeb_pdp_product_group_ui_ux(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):\n        \"\"\"\n        【108701】 PDP-分享-商品图片分享UI/UX验证\n        \"\"\"\n        p: Page = phone_page.get(\"page\")\n        c = phone_page.get(\"context\")\n        # 直接进入指定pdp页面\n        pdp_page = PDPPage(p, h5_autotest_header, browser_context=c,\n                           page_url=\"/product/Vita-Honey-Chrysanthemum-Tea-250ml-6/106422\")\n        p.wait_for_timeout(3000)\n        # 关闭pop\n        p.locator(u\"//button[contains(text(), 'Continue')]\").click()\n    \n        # 滚动到指定位置-分享按钮\n        scroll_one_page_until(p, mweb_pdp_ele.ele_share)\n        # 点击分享按钮\n        pdp_page.FE.ele(mweb_pdp_ele.ele_share).click()\n        p.wait_for_timeout(3000)\n        # 断言分享pop 弹出成功\n        assert pdp_page.FE.ele(mweb_pdp_ele.ele_share_pop), 'pdp点击分享未弹出pop'\n        # 点击分享图片按钮\n        pdp_page.FE.ele(mweb_pdp_ele.ele_share_pop_img).click()\n        p.wait_for_timeout(6000)\n        # 断言分享图片pop 弹出成功\n        assert pdp_page.FE.ele(mweb_pdp_ele.ele_share_img_pop), 'pdp点击分享图片未弹出pop'\n        # 断言分享图片中内容\n        # 断言img pop title = Press and hold screen to save image\n        assert pdp_page.FE.ele(\n            mweb_pdp_ele.ele_share_img_pop + \"//span\").text_content() == \"Press and hold screen to save image\"\n        # 断言头像信息\n        image_avatar = pdp_page.FE.ele(mweb_pdp_ele.ele_share_img_pop + \"//div[contains(@class,'shareImage_avatar_')]\")\n        assert image_avatar\n        # 断言name 信息\n        img_name = pdp_page.FE.ele(\n            mweb_pdp_ele.ele_share_img_pop + \"//div[contains(@class,'shareImage_avatarMessage')]/p[1]\")\n        assert img_name\n        img_title = pdp_page.FE.ele(\n            mweb_pdp_ele.ele_share_img_pop + \"//div[contains(@class,'shareImage_avatarMessage')]/p[2]\").text_content()\n        assert img_title == '\"I found this great item on Weee!\"'\n        # 断言商品图片\n        share_img = pdp_page.FE.ele(\n            mweb_pdp_ele.ele_share_img_pop + \"//img[contains(@class,'shareImage_productImage')]\")\n        assert share_img\n        # 断言销量\n        weekly_sold = pdp_page.FE.ele(\n            mweb_pdp_ele.ele_share_img_pop + \"//div[contains(@class,'shareImage_productWeeklySold')]//span\")\n        assert weekly_sold\n        # 断言img product title\n        product_title = pdp_page.FE.ele(\n            mweb_pdp_ele.ele_share_img_pop + \"//h2[contains(@class,'shareImage_productTitle')]\")\n        assert product_title\n        # 断言img product price\n        price = pdp_page.FE.ele(mweb_pdp_ele.ele_share_img_pop + \"//span[contains(@class,'shareImage_price')]\")\n        assert price\n        # 断言img product base price\n        base_price = pdp_page.FE.ele(mweb_pdp_ele.ele_share_img_pop + \"//span[contains(@class,'shareImage_basePrice')]\")\n        if base_price:\n            # 断言off 标签存在\n            img_off = pdp_page.FE.ele(\n                mweb_pdp_ele.ele_share_img_pop + \"//div[contains(@class,'shareImage_productDiscount')]\")\n    \n            assert img_off\n        # 断言二维码\n        product_qrcode = pdp_page.FE.ele(\n                mweb_pdp_ele.ele_share_img_pop + \"//div[contains(@class,'shareImage_productQRCode_')]//img\")\n        assert product_qrcode\n        # 断言底部footer logo 信息\n        weee_logo = pdp_page.FE.ele(\n            mweb_pdp_ele.ele_share_img_pop + \"//div[contains(@class,'shareImage_footerLogo')]//img\")\n        assert weee_logo\n        # 断言底部footer 信息\n        weee_title = pdp_page.FE.ele(\n            mweb_pdp_ele.ele_share_img_pop + \"//div[contains(@class,'shareImage_bottomFooter')]//p\")\n>       assert weee_title == \"Asian Groceries\"\nE       AssertionError: assert <JSHandle preview=JSHandle@<p>Asian Groceries</p>> == 'Asian Groceries'\n\ntest_108701_mWeb_pdp_product_image_share_ui_ux.py:89: AssertionError"}, "description": "\n        【108701】 PDP-分享-商品图片分享UI/UX验证\n        ", "start": 1742885298630, "stop": 1742885328020, "uuid": "6e8a789e-9b12-48bf-b7b9-6621ad334bf7", "historyId": "94db29360838c0af903fcc091a6c4b63", "testCaseId": "94db29360838c0af903fcc091a6c4b63", "fullName": "src.Mweb.EC.testcases.mweb_pdp.test_108701_mWeb_pdp_product_image_share_ui_ux.TestMWebPDPProductImgShareUIUX#test_112063_mWeb_pdp_product_group_ui_ux", "labels": [{"name": "story", "value": "【108701】 PDP-分享-商品图片分享UI/UX验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "h5pdp"}, {"name": "tag", "value": "transaction"}, {"name": "tag", "value": "suqin"}, {"name": "parentSuite", "value": "src.Mweb.EC.testcases.mweb_pdp"}, {"name": "suite", "value": "test_108701_mWeb_pdp_product_image_share_ui_ux"}, {"name": "subSuite", "value": "TestMWebPDPProductImgShareUIUX"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "30336-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Mweb.EC.testcases.mweb_pdp.test_108701_mWeb_pdp_product_image_share_ui_ux"}]}
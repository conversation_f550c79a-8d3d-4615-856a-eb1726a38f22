{"name": "【112063】 H5-PDP-商品组-只有一个属性模块UI/UX验证", "status": "broken", "statusDetails": {"message": "_pytest.fixtures.FixtureLookupError: ('open_and_close_trace', <FixtureRequest for <Function test_112063_mWeb_pdp_product_group_ui_ux>>)", "trace": "file D:\\MyWork\\Python\\EC-demo\\qa-ui-dmweb\\src\\Mweb\\EC\\testcases\\mweb_pdp\\test_112063_mWeb_pdp_product_group_ui_ux.py, line 15\n      @allure.title(\"【112063】 H5-PDP-商品组-只有一个属性模块UI/UX验证\")\n      def test_112063_mWeb_pdp_product_group_ui_ux(self, phone_page: dict, h5_autotest_header, open_and_close_trace):\nE       fixture 'open_and_close_trace' not found\n>       available fixtures: __pytest_repeat_step_number, _session_faker, cache, capfd, capfdbinary, caplog, capsys, capsysbinary, doctest_namespace, faker, h5_autotest_header, h5_open_and_close_trace, monkeypatch, not_login_phone_page, phone_page, playwright, porder, pytestconfig, record_property, record_testsuite_property, record_xml_attribute, recwarn, tmp_path, tmp_path_factory, tmpdir, tmpdir_factory, user_setup\n>       use 'pytest --fixtures [testpath]' for help on them.\n\nD:\\MyWork\\Python\\EC-demo\\qa-ui-dmweb\\src\\Mweb\\EC\\testcases\\mweb_pdp\\test_112063_mWeb_pdp_product_group_ui_ux.py:15"}, "description": "【112063】 H5-PDP-商品组-只有一个属性模块UI/UX验证\n        \n        测试步骤：\n        1. 进入指定PDP页面\n        2. 验证商品组组件的基础UI元素\n        3. 验证商品组弹窗功能\n        4. 验证商品组切换功能\n        \n        预期结果：\n        1. 商品组组件正确显示\n        2. 商品组弹窗正常显示和关闭\n        3. 商品切换功能正常\n        ", "start": 1742533662373, "stop": 1742533662373, "uuid": "15e4afb2-81dd-46d7-b0e1-47eca77e12ba", "historyId": "a04e0017fe119811658bd84390ed0585", "testCaseId": "a04e0017fe119811658bd84390ed0585", "fullName": "src.Mweb.EC.testcases.mweb_pdp.test_112063_mWeb_pdp_product_group_ui_ux.TestMWebPDPProductGroupUIUX#test_112063_mWeb_pdp_product_group_ui_ux", "labels": [{"name": "story", "value": "【112063】 H5-PDP-商品组-只有一个属性模块UI/UX验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "h5pdp"}, {"name": "tag", "value": "transaction"}, {"name": "tag", "value": "suqin"}, {"name": "parentSuite", "value": "src.Mweb.EC.testcases.mweb_pdp"}, {"name": "suite", "value": "test_112063_mWeb_pdp_product_group_ui_ux"}, {"name": "subSuite", "value": "TestMWebPDPProductGroupUIUX"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "21236-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Mweb.EC.testcases.mweb_pdp.test_112063_mWeb_pdp_product_group_ui_ux"}]}
{"name": "【112063】 H5-PDP-商品组-只有一个属性模块UI/UX验证", "status": "broken", "statusDetails": {"message": "_pytest.fixtures.FixtureLookupError: ('open_and_close_trace', <FixtureRequest for <Function test_112063_mWeb_pdp_product_group_ui_ux>>)", "trace": "file D:\\MyWork\\Python\\EC-demo\\qa-ui-dmweb\\src\\Mweb\\EC\\testcases\\mweb_pdp\\test_112063_mWeb_pdp_product_group_ui_ux.py, line 15\n      @allure.title(\"【112063】 H5-PDP-商品组-只有一个属性模块UI/UX验证\")\n      def test_112063_mWeb_pdp_product_group_ui_ux(self, phone_page: dict, h5_autotest_header, open_and_close_trace):\nE       fixture 'open_and_close_trace' not found\n>       available fixtures: __pytest_repeat_step_number, _session_faker, cache, capfd, capfdbinary, caplog, capsys, capsysbinary, doctest_namespace, faker, h5_autotest_header, h5_open_and_close_trace, monkeypatch, not_login_phone_page, phone_page, playwright, porder, pytestconfig, record_property, record_testsuite_property, record_xml_attribute, recwarn, tmp_path, tmp_path_factory, tmpdir, tmpdir_factory, user_setup\n>       use 'pytest --fixtures [testpath]' for help on them.\n\nD:\\MyWork\\Python\\EC-demo\\qa-ui-dmweb\\src\\Mweb\\EC\\testcases\\mweb_pdp\\test_112063_mWeb_pdp_product_group_ui_ux.py:15"}, "description": "\n        【112063】 H5-PDP-商品组-只有一个属性模块UI/UX验证\n        ", "start": 1742538791041, "stop": 1742538791041, "uuid": "a1437c11-eadb-4371-989b-dcefbd34d9e1", "historyId": "a04e0017fe119811658bd84390ed0585", "testCaseId": "a04e0017fe119811658bd84390ed0585", "fullName": "src.Mweb.EC.testcases.mweb_pdp.test_112063_mWeb_pdp_product_group_ui_ux.TestMWebPDPProductGroupUIUX#test_112063_mWeb_pdp_product_group_ui_ux", "labels": [{"name": "story", "value": "【112063】 H5-PDP-商品组-只有一个属性模块UI/UX验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "h5pdp"}, {"name": "tag", "value": "transaction"}, {"name": "tag", "value": "suqin"}, {"name": "parentSuite", "value": "src.Mweb.EC.testcases.mweb_pdp"}, {"name": "suite", "value": "test_112063_mWeb_pdp_product_group_ui_ux"}, {"name": "subSuite", "value": "TestMWebPDPProductGroupUIUX"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "3228-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Mweb.EC.testcases.mweb_pdp.test_112063_mWeb_pdp_product_group_ui_ux"}]}
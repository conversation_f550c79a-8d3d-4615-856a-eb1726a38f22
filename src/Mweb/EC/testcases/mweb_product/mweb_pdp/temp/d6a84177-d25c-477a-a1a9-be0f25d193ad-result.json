{"name": "【112065】 PC-PDP-商品组-只有一个属性模块UI/UX验证", "status": "broken", "statusDetails": {"message": "_pytest.fixtures.FixtureLookupError: ('page', <FixtureRequest for <Function test_112065_mWeb_pdp_product_group_ui_ux>>)", "trace": "file D:\\MyWork\\Python\\EC-demo\\qa-ui-dmweb\\src\\Mweb\\EC\\testcases\\mweb_pdp\\test_112063_mWeb_pdp_product_group_ui_ux.py, line 15\n      @allure.title(\"【112065】 PC-PDP-商品组-只有一个属性模块UI/UX验证\")\n      def test_112065_mWeb_pdp_product_group_ui_ux(self, page: dict, h5_autotest_header, login_trace):\nE       fixture 'page' not found\n>       available fixtures: __pytest_repeat_step_number, _session_faker, cache, capfd, capfdbinary, caplog, capsys, capsysbinary, doctest_namespace, faker, h5_autotest_header, monkeypatch, not_login_phone_page, open_and_close_trace, phone_page, playwright, porder, pytestconfig, record_property, record_testsuite_property, record_xml_attribute, recwarn, tmp_path, tmp_path_factory, tmpdir, tmpdir_factory, user_setup\n>       use 'pytest --fixtures [testpath]' for help on them.\n\nD:\\MyWork\\Python\\EC-demo\\qa-ui-dmweb\\src\\Mweb\\EC\\testcases\\mweb_pdp\\test_112063_mWeb_pdp_product_group_ui_ux.py:15"}, "description": "\n        【112065】 PC-PDP-商品组-只有一个属性模块UI/UX验证\n        ", "start": 1741775635073, "stop": 1741775635073, "uuid": "5dae5dd3-6ecd-4477-9f0e-2968c81aa960", "historyId": "f9ae2b83fe2d1c35d4e399926ac943d8", "testCaseId": "f9ae2b83fe2d1c35d4e399926ac943d8", "fullName": "src.Mweb.EC.testcases.mweb_pdp.test_112063_mWeb_pdp_product_group_ui_ux.TestMWebPDPProductGroupUIUX#test_112065_mWeb_pdp_product_group_ui_ux", "labels": [{"name": "story", "value": "【112065】 PC-PDP-商品组-只有一个属性模块UI/UX验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "h5pdp"}, {"name": "tag", "value": "transaction"}, {"name": "tag", "value": "suqin"}, {"name": "parentSuite", "value": "src.Mweb.EC.testcases.mweb_pdp"}, {"name": "suite", "value": "test_112063_mWeb_pdp_product_group_ui_ux"}, {"name": "subSuite", "value": "TestMWebPDPProductGroupUIUX"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "24912-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Mweb.EC.testcases.mweb_pdp.test_112063_mWeb_pdp_product_group_ui_ux"}]}
{"name": "【112065】 PC-PDP-商品组-只有一个属性模块UI/UX验证", "status": "broken", "statusDetails": {"message": "_pytest.fixtures.FixtureLookupError: ('page', <FixtureRequest for <Function test_112065_mWeb_pdp_product_group_ui_ux>>)", "trace": "file D:\\MyWork\\Python\\EC-demo\\qa-ui-dmweb\\src\\Mweb\\EC\\testcases\\pdp\\test_112063_mWeb_pdp_product_group_ui_ux.py, line 15\n      @allure.title(\"【112065】 PC-PDP-商品组-只有一个属性模块UI/UX验证\")\n      def test_112065_mWeb_pdp_product_group_ui_ux(self, page: dict, pc_autotest_header, login_trace):\nE       fixture 'page' not found\n>       available fixtures: __pytest_repeat_step_number, _session_faker, cache, capfd, capfdbinary, caplog, capsys, capsysbinary, doctest_namespace, faker, h5_autotest_header, monkeypatch, not_login_phone_page, open_and_close_trace, phone_page, playwright, porder, pytestconfig, record_property, record_testsuite_property, record_xml_attribute, recwarn, tmp_path, tmp_path_factory, tmpdir, tmpdir_factory, user_setup\n>       use 'pytest --fixtures [testpath]' for help on them.\n\nD:\\MyWork\\Python\\EC-demo\\qa-ui-dmweb\\src\\Mweb\\EC\\testcases\\pdp\\test_112063_mWeb_pdp_product_group_ui_ux.py:15"}, "description": "\n        【112065】 PC-PDP-商品组-只有一个属性模块UI/UX验证\n        ", "start": 1741769958344, "stop": 1741769958344, "uuid": "d04f130d-40a0-49d2-a9b9-ace39901f067", "historyId": "c9518422bc49e9129ea3333bf04757d5", "testCaseId": "c9518422bc49e9129ea3333bf04757d5", "fullName": "src.Mweb.EC.testcases.pdp.test_112063_mWeb_pdp_product_group_ui_ux.TestMWebPDPProductGroupUIUX#test_112065_mWeb_pdp_product_group_ui_ux", "labels": [{"name": "story", "value": "【112065】 PC-PDP-商品组-只有一个属性模块UI/UX验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "h5pdp"}, {"name": "tag", "value": "transaction"}, {"name": "tag", "value": "suqin"}, {"name": "parentSuite", "value": "src.Mweb.EC.testcases.pdp"}, {"name": "suite", "value": "test_112063_mWeb_pdp_product_group_ui_ux"}, {"name": "subSuite", "value": "TestMWebPDPProductGroupUIUX"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "15708-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Mweb.EC.testcases.pdp.test_112063_mWeb_pdp_product_group_ui_ux"}]}
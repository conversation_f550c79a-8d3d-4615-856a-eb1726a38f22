{"name": "【108701】 PDP-分享-商品图片分享UI/UX验证", "status": "failed", "statusDetails": {"message": "AssertionError: assert False", "trace": "self = <test_108701_mWeb_pdp_product_image_share_ui_ux.TestMWebPDPProductImgShareUIUX object at 0x0000029D5E0FC150>\nphone_page = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppDat...>>, 'page': <Page url='https://www.sayweee.com/en/product/Vita-Honey-Chrysanthemum-Tea-250ml-6/106422?joinEnki=false'>}\nh5_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...lk_nPtFOznS3CSXyguBE8NFIS7VuzFUD_T-6d4RX8ofNknVsrx0XdVfC3BxordZ8vhHuWlFU_28ka8ebC-0P9dphpsf65cKjFLhOzbdEtqGNKwAc', ...}\nh5_open_and_close_trace = None\n\n    @allure.title(\"【108701】 PDP-分享-商品图片分享UI/UX验证\")\n    def test_112063_mWeb_pdp_product_group_ui_ux(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):\n        \"\"\"\n        【108701】 PDP-分享-商品图片分享UI/UX验证\n        \"\"\"\n        p: Page = phone_page.get(\"page\")\n        c = phone_page.get(\"context\")\n        # 直接进入指定pdp页面\n        pdp_page = PDPPage(p, h5_autotest_header, browser_context=c,\n                           page_url=\"/product/Vita-Honey-Chrysanthemum-Tea-250ml-6/106422\")\n        p.wait_for_timeout(3000)\n        # 关闭pop\n        p.locator(u\"//button[contains(text(), 'Continue')]\").click()\n    \n        # 滚动到指定位置-分享按钮\n        scroll_one_page_until(p, mweb_pdp_ele.ele_share)\n        # 点击分享按钮\n        pdp_page.FE.ele(mweb_pdp_ele.ele_share).click()\n        p.wait_for_timeout(3000)\n        # 断言分享pop 弹出成功\n        assert pdp_page.FE.ele(mweb_pdp_ele.ele_share_pop), 'pdp点击分享未弹出pop'\n        # 点击分享图片按钮\n        pdp_page.FE.ele(mweb_pdp_ele.ele_share_pop_img).click()\n        p.wait_for_timeout(6000)\n        # 断言分享图片pop 弹出成功\n        assert pdp_page.FE.ele(mweb_pdp_ele.ele_share_img_pop), 'pdp点击分享图片未弹出pop'\n        # 断言分享图片中内容\n        # 断言img pop title = Press and hold screen to save image\n        assert pdp_page.FE.ele(\n            mweb_pdp_ele.ele_share_img_pop + \"//span\").text_content() == \"Press and hold screen to save image\"\n        # 断言头像信息\n        image_avatar = pdp_page.FE.ele(mweb_pdp_ele.ele_share_img_pop + \"//div[contains(@class,'shareImage_avatar_')]\")\n        assert image_avatar\n        # 断言name 信息\n        img_name = pdp_page.FE.ele(\n            mweb_pdp_ele.ele_share_img_pop + \"//div[contains(@class,'shareImage_avatarMessage')]/p[1]\")\n        assert img_name\n        img_title = pdp_page.FE.ele(\n            mweb_pdp_ele.ele_share_img_pop + \"//div[contains(@class,'shareImage_avatarMessage')]/p[2]\").text_content() == \"I found this great item on Weee!\"\n>       assert img_title\nE       assert False\n\ntest_108701_mWeb_pdp_product_image_share_ui_ux.py:54: AssertionError"}, "description": "\n        【108701】 PDP-分享-商品图片分享UI/UX验证\n        ", "start": 1742884749505, "stop": 1742884774583, "uuid": "d6a744fc-05cb-4c0d-bf83-ef5a28aa1878", "historyId": "94db29360838c0af903fcc091a6c4b63", "testCaseId": "94db29360838c0af903fcc091a6c4b63", "fullName": "src.Mweb.EC.testcases.mweb_pdp.test_108701_mWeb_pdp_product_image_share_ui_ux.TestMWebPDPProductImgShareUIUX#test_112063_mWeb_pdp_product_group_ui_ux", "labels": [{"name": "story", "value": "【108701】 PDP-分享-商品图片分享UI/UX验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "h5pdp"}, {"name": "tag", "value": "transaction"}, {"name": "tag", "value": "suqin"}, {"name": "parentSuite", "value": "src.Mweb.EC.testcases.mweb_pdp"}, {"name": "suite", "value": "test_108701_mWeb_pdp_product_image_share_ui_ux"}, {"name": "subSuite", "value": "TestMWebPDPProductImgShareUIUX"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "9472-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Mweb.EC.testcases.mweb_pdp.test_108701_mWeb_pdp_product_image_share_ui_ux"}]}
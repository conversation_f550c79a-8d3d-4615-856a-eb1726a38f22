{"name": "【108701】 PDP-分享-商品图片分享UI/UX验证", "status": "failed", "statusDetails": {"message": "AssertionError: pdp点击分享图片未弹出pop\nassert None\n +  where None = <bound method FindElement.ele of <src.common.commonui.FindElement object at 0x0000015E46E30A90>>(\"//div[@data-type='popup' and @data-popup-visible='true' and @id='share-image']\")\n +    where <bound method FindElement.ele of <src.common.commonui.FindElement object at 0x0000015E46E30A90>> = <src.common.commonui.FindElement object at 0x0000015E46E30A90>.ele\n +      where <src.common.commonui.FindElement object at 0x0000015E46E30A90> = <src.Mweb.EC.mweb_pages.mweb_page_pdp.mweb_page_pdp.PDPPage object at 0x0000015E46AD8190>.FE\n +    and   \"//div[@data-type='popup' and @data-popup-visible='true' and @id='share-image']\" = mweb_pdp_ele.ele_share_img_pop", "trace": "self = <test_108701_mWeb_pdp_product_image_share_ui_ux.TestMWebPDPProductImgShareUIUX object at 0x0000015E46977190>\nphone_page = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppDat...>>, 'page': <Page url='https://www.sayweee.com/en/product/Vita-Honey-Chrysanthemum-Tea-250ml-6/106422?joinEnki=false'>}\nh5_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...p5_fQwPNSCVqCZ4kunU8ipbARtWgLBvLBgZ_6S-ITvxsLgYiZ5rWPPb9bjiAd9c7ShMsK_kz5xN_G_-FW4ealADZoT_h0DVEP5IFJVBAqs2z1Ix0', ...}\nh5_open_and_close_trace = None\n\n    @allure.title(\"【108701】 PDP-分享-商品图片分享UI/UX验证\")\n    def test_112063_mWeb_pdp_product_group_ui_ux(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):\n        \"\"\"\n        【108701】 PDP-分享-商品图片分享UI/UX验证\n        \"\"\"\n        p: Page = phone_page.get(\"page\")\n        c = phone_page.get(\"context\")\n        # 直接进入指定pdp页面\n        pdp_page = PDPPage(p, h5_autotest_header, browser_context=c,\n                           page_url=\"/product/Vita-Honey-Chrysanthemum-Tea-250ml-6/106422\")\n        p.wait_for_timeout(3000)\n        # 关闭pop\n        p.locator(u\"//button[contains(text(), 'Continue')]\").click()\n    \n        # 滚动到指定位置-分享按钮\n        scroll_one_page_until(p, mweb_pdp_ele.ele_share)\n        # 点击分享按钮\n        pdp_page.FE.ele(mweb_pdp_ele.ele_share).click()\n        p.wait_for_timeout(3000)\n        # 断言分享pop 弹出成功\n        assert pdp_page.FE.ele(mweb_pdp_ele.ele_share_pop), 'pdp点击分享未弹出pop'\n        # 点击分享图片按钮\n        pdp_page.FE.ele(mweb_pdp_ele.ele_share_pop_img)\n        p.wait_for_timeout(6000)\n        # 断言分享图片pop 弹出成功\n>       assert pdp_page.FE.ele(mweb_pdp_ele.ele_share_img_pop), 'pdp点击分享图片未弹出pop'\nE       AssertionError: pdp点击分享图片未弹出pop\nE       assert None\nE        +  where None = <bound method FindElement.ele of <src.common.commonui.FindElement object at 0x0000015E46E30A90>>(\"//div[@data-type='popup' and @data-popup-visible='true' and @id='share-image']\")\nE        +    where <bound method FindElement.ele of <src.common.commonui.FindElement object at 0x0000015E46E30A90>> = <src.common.commonui.FindElement object at 0x0000015E46E30A90>.ele\nE        +      where <src.common.commonui.FindElement object at 0x0000015E46E30A90> = <src.Mweb.EC.mweb_pages.mweb_page_pdp.mweb_page_pdp.PDPPage object at 0x0000015E46AD8190>.FE\nE        +    and   \"//div[@data-type='popup' and @data-popup-visible='true' and @id='share-image']\" = mweb_pdp_ele.ele_share_img_pop\n\ntest_108701_mWeb_pdp_product_image_share_ui_ux.py:40: AssertionError"}, "description": "\n        【108701】 PDP-分享-商品图片分享UI/UX验证\n        ", "start": 1742882802930, "stop": 1742882832545, "uuid": "8e600465-aa7b-401f-b078-226e2b88e87f", "historyId": "94db29360838c0af903fcc091a6c4b63", "testCaseId": "94db29360838c0af903fcc091a6c4b63", "fullName": "src.Mweb.EC.testcases.mweb_pdp.test_108701_mWeb_pdp_product_image_share_ui_ux.TestMWebPDPProductImgShareUIUX#test_112063_mWeb_pdp_product_group_ui_ux", "labels": [{"name": "story", "value": "【108701】 PDP-分享-商品图片分享UI/UX验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "h5pdp"}, {"name": "tag", "value": "transaction"}, {"name": "tag", "value": "suqin"}, {"name": "parentSuite", "value": "src.Mweb.EC.testcases.mweb_pdp"}, {"name": "suite", "value": "test_108701_mWeb_pdp_product_image_share_ui_ux"}, {"name": "subSuite", "value": "TestMWebPDPProductImgShareUIUX"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "9880-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Mweb.EC.testcases.mweb_pdp.test_108701_mWeb_pdp_product_image_share_ui_ux"}]}
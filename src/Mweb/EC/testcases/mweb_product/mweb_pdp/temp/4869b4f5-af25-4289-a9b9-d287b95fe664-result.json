{"name": "【112063】 H5-PDP-商品组-只有一个属性模块UI/UX验证", "status": "broken", "statusDetails": {"message": "AttributeError: 'NoneType' object has no attribute 'is_visible'", "trace": "self = <test_112063_mWeb_pdp_product_group_ui_ux.TestMWebPDPProductGroupUIUX object at 0x00000170117BDE90>\nphone_page = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppDat...>>, 'page': <Page url='https://www.sayweee.com/en/product/Vita-Honey-Chrysanthemum-Tea-250ml-6/106422?joinEnki=false'>}\nh5_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...TqhYtwiafAh-WyPwFIqTZeB8XdQlCg9_sAc3a_93106LDhk9C5nlLdiRxkrGbDgp0XK-9iCy18QRBCt5hs7e0qa5adzola5MjsQJHR5cguonEk2w', ...}\nh5_open_and_close_trace = None\n\n    @allure.title(\"【112063】 H5-PDP-商品组-只有一个属性模块UI/UX验证\")\n    def test_112063_mWeb_pdp_product_group_ui_ux(self, phone_page: dict, h5_autotest_header, open_and_close_trace):\n        \"\"\"\n        【112063】 H5-PDP-商品组-只有一个属性模块UI/UX验证\n        \"\"\"\n        p: Page = phone_page.get(\"page\")\n        c = phone_page.get(\"context\")\n        # 直接进入指定pdp页面\n        pdp_page = PDPPage(p, h5_autotest_header, browser_context=c,\n                           page_url=\"/product/Vita-Honey-Chrysanthemum-Tea-250ml-6/106422\")\n        p.wait_for_timeout(3000)\n        # 关闭pop\n        p.locator(u\"//button[contains(text(), 'Continue')]\").click()\n    \n        # 滚动到指定位置\n        scroll_one_page_until(p, mweb_pdp_ele.ele_product_group)\n        # 断言product group 文案存在\n>       assert pdp_page.FE.ele(mweb_pdp_ele.ele_product_group_select).is_visible()\nE       AttributeError: 'NoneType' object has no attribute 'is_visible'\n\ntest_112063_mWeb_pdp_product_group_ui_ux.py:32: AttributeError"}, "description": "\n        【112063】 H5-PDP-商品组-只有一个属性模块UI/UX验证\n        ", "start": 1742538972086, "stop": 1742539045921, "uuid": "89f23ed1-5bbf-48f3-a31a-ed2b103f6bfc", "historyId": "a04e0017fe119811658bd84390ed0585", "testCaseId": "a04e0017fe119811658bd84390ed0585", "fullName": "src.Mweb.EC.testcases.mweb_pdp.test_112063_mWeb_pdp_product_group_ui_ux.TestMWebPDPProductGroupUIUX#test_112063_mWeb_pdp_product_group_ui_ux", "labels": [{"name": "story", "value": "【112063】 H5-PDP-商品组-只有一个属性模块UI/UX验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "h5pdp"}, {"name": "tag", "value": "transaction"}, {"name": "tag", "value": "suqin"}, {"name": "parentSuite", "value": "src.Mweb.EC.testcases.mweb_pdp"}, {"name": "suite", "value": "test_112063_mWeb_pdp_product_group_ui_ux"}, {"name": "subSuite", "value": "TestMWebPDPProductGroupUIUX"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "31116-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Mweb.EC.testcases.mweb_pdp.test_112063_mWeb_pdp_product_group_ui_ux"}]}
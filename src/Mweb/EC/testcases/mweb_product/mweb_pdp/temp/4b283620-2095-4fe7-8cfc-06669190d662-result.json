{"name": "【112065】 PC-PDP-商品组-只有一个属性模块UI/UX验证", "status": "broken", "statusDetails": {"message": "playwright._impl._errors.Error: Unexpected token \"/\" while parsing selector \"/img\"", "trace": "self = <test_112063_mWeb_pdp_product_group_ui_ux.TestMWebPDPProductGroupUIUX object at 0x000001FB3535F0D0>\nphone_page = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppDat...>>, 'page': <Page url='https://www.sayweee.com/en/product/Vita-Honey-Chrysanthemum-Tea-250ml-6/106422?joinEnki=false'>}\nh5_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...OZVeOgLitYzBCw0Lo_kxb83VTMZsPreB26Bg6AnJpFg7cfyLO36_iUiNdwmiO5IGCyZTfRaDh8SDWEPF6HmwCe1foRQ79CFnDKPdP92x-mM_1bSw', ...}\nopen_and_close_trace = None\n\n    @allure.title(\"【112065】 PC-PDP-商品组-只有一个属性模块UI/UX验证\")\n    def test_112065_mWeb_pdp_product_group_ui_ux(self, phone_page: dict, h5_autotest_header,open_and_close_trace):\n        \"\"\"\n        【112065】 PC-PDP-商品组-只有一个属性模块UI/UX验证\n        \"\"\"\n        p: Page = phone_page.get(\"page\")\n        c = phone_page.get(\"context\")\n        # 直接进入指定pdp页面\n        pdp_page = PDPPage(p, h5_autotest_header, browser_context=c,\n                           page_url=\"/product/Vita-Honey-Chrysanthemum-Tea-250ml-6/106422\")\n        # 滚动到指定位置\n        scroll_one_page_until(p, mweb_pdp_ele.ele_product_group)\n        # 断言product group 文案存在\n        assert pdp_page.FE.ele(mweb_pdp_ele.ele_product_group_select).is_visible()\n        assert pdp_page.FE.ele(mweb_pdp_ele.ele_product_group_title).text_content()\n        # 获取product group 组件商品list\n        product_group_list = pdp_page.FE.eles(mweb_pdp_ele.ele_product_group_img)\n        len1 = len(product_group_list)\n        print(len1)\n        for index, item in enumerate(product_group_list):\n            # 验证product_group 商品图片\n>           assert item.query_selector(u\"/img\")\n\ntest_112063_mWeb_pdp_product_group_ui_ux.py:36: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py:2897: in query_selector\n    self._sync(self._impl_obj.query_selector(selector=selector))\n..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_element_handle.py:317: in query_selector\n    await self._channel.send(\"querySelector\", dict(selector=selector))\n..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:62: in send\n    return await self._connection.wrap_api_call(\n..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:492: in wrap_api_call\n    return await cb()\n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n\nself = <playwright._impl._connection.Channel object at 0x000001FB38EE4390>\nmethod = 'querySelector', params = {'selector': '/img'}, return_as_dict = False\n\n    async def inner_send(\n        self, method: str, params: Optional[Dict], return_as_dict: bool\n    ) -> Any:\n        if params is None:\n            params = {}\n        callback = self._connection._send_message_to_server(\n            self._object, method, params\n        )\n        if self._connection._error:\n            error = self._connection._error\n            self._connection._error = None\n            raise error\n        done, _ = await asyncio.wait(\n            {\n                self._connection._transport.on_error_future,\n                callback.future,\n            },\n            return_when=asyncio.FIRST_COMPLETED,\n        )\n        if not callback.future.done():\n            callback.future.cancel()\n>       result = next(iter(done)).result()\nE       playwright._impl._errors.Error: Unexpected token \"/\" while parsing selector \"/img\"\n\n..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:100: Error"}, "description": "\n        【112065】 PC-PDP-商品组-只有一个属性模块UI/UX验证\n        ", "start": 1741776031996, "stop": 1741776043901, "uuid": "a490a120-3eb0-4bde-832e-bf74c8b2aa59", "historyId": "f9ae2b83fe2d1c35d4e399926ac943d8", "testCaseId": "f9ae2b83fe2d1c35d4e399926ac943d8", "fullName": "src.Mweb.EC.testcases.mweb_pdp.test_112063_mWeb_pdp_product_group_ui_ux.TestMWebPDPProductGroupUIUX#test_112065_mWeb_pdp_product_group_ui_ux", "labels": [{"name": "story", "value": "【112065】 PC-PDP-商品组-只有一个属性模块UI/UX验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "h5pdp"}, {"name": "tag", "value": "transaction"}, {"name": "tag", "value": "suqin"}, {"name": "parentSuite", "value": "src.Mweb.EC.testcases.mweb_pdp"}, {"name": "suite", "value": "test_112063_mWeb_pdp_product_group_ui_ux"}, {"name": "subSuite", "value": "TestMWebPDPProductGroupUIUX"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "22420-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Mweb.EC.testcases.mweb_pdp.test_112063_mWeb_pdp_product_group_ui_ux"}]}
{"name": "【108209】 PDP-分享-商品分享流程验证", "status": "passed", "description": "\n        【108209】 PDP-分享-商品分享流程验证\n        ", "start": 1742892127090, "stop": 1742892151645, "uuid": "05614276-da39-4de0-8dff-7b2aebf8eabc", "historyId": "e041fa463445e74f903fa7bec7299477", "testCaseId": "e041fa463445e74f903fa7bec7299477", "fullName": "src.Mweb.EC.testcases.mweb_pdp.test_108209_mWeb_pdp_product_share_ui_ux.TestMWebPDPProductShareUIUX#test_112063_mWeb_pdp_product_share_ui_ux", "labels": [{"name": "story", "value": "【108209】 PDP-分享-商品分享流程验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "h5pdp"}, {"name": "tag", "value": "transaction"}, {"name": "tag", "value": "suqin"}, {"name": "parentSuite", "value": "src.Mweb.EC.testcases.mweb_pdp"}, {"name": "suite", "value": "test_108209_mWeb_pdp_product_share_ui_ux"}, {"name": "subSuite", "value": "TestMWebPDPProductShareUIUX"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "21404-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Mweb.EC.testcases.mweb_pdp.test_108209_mWeb_pdp_product_share_ui_ux"}]}
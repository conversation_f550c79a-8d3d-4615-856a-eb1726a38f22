# 地址卡片
address_card = "wid-address-card"
address_card_location_icon = "wid-address-card-location-icon"
address_card_name = "wid-address-card-name"
address_card_address = "wid-address-card-address"
address_card_city = "wid-address-card-city"
address_card_edit = "wid-address-card-edit"

# Deliver to pop
# 添加地址按钮
# h5 暂时用这个，等开发修改了替换下面这个
deliver_to_pop_add_btn = "btn-add-address"
# deliver_to_pop_add_btn = "wid-btn-add-address"
# zipcode 弹窗x按钮
deliver_to_pop_close_btn  = "btn-close-delivery-info-dialog"
# 新增地址按钮
ele_add_new_address_button = u"//button[text()='Add new address']"
# ########################################################################
# delivery address pop
delivery_address_input = ""
# 第一个联想的地址
address_first_matched = "streetAddressList"
# 查看更多按钮
zipcode_pop_more_btn = "wid-btn-more-address"

# ########################################################################
# # Delivery Info 添加地址页面pop
# 输入姓名
address_first_name = "wid-input-first-name"
address_last_name = "wid-input-last-name"
# 输入电话
address_phone = "wid-input-phone"
# 输入街道地址
address_street = "wid-input-street"
# 输入公寓号
address_apt = "wid-input-flats"
# 输入城市
address_city = u"input[placeholder='City']"
# 输入州
address_state = "text-filed-input"
# 输入邮编
address_zipcode = "wid-input-zipcode"
# 输入备注
address_note = "wid-input-notes"
# 保存地址
address_save_button = "btn-save-address"
# 取消保存
address_cancel_button = u"button[type='button']"
# 删除按钮
address_delete_button = "btn-delete-address"


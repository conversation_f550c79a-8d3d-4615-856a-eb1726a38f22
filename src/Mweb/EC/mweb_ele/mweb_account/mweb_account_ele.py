"""
<AUTHOR>  Assistant
@Version        :  V1.0.0
------------------------------------
@File           :   mweb_account_ele.py
@Description    :  Mobile Account页面元素定位
@CreateTime     :  2025/1/24
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2025/1/24
"""

# Me页面主要元素
# 我的订单模块
ele_my_orders_section = u"//div[@data-testid='my-orders-section']"
ele_my_orders_title = u"//div[contains(text(), 'My Orders') or contains(text(), '我的订单')]"
ele_my_orders_arrow = u"//div[@data-testid='my-orders-section']//i[contains(@class, 'arrow') or contains(@class, 'chevron')]"

# 我的订单状态快捷入口
ele_pending_payment_shortcut = u"//div[@data-testid='order-status-pending-payment']"
ele_pending_shipment_shortcut = u"//div[@data-testid='order-status-pending-shipment']"
ele_shipped_shortcut = u"//div[@data-testid='order-status-shipped']"
ele_to_review_shortcut = u"//div[@data-testid='order-status-to-review']"
ele_returns_after_sales_shortcut = u"//div[@data-testid='order-status-returns-after-sales']"

# 订单状态文本
ele_pending_payment_text = u"//span[contains(text(), '待付款') or contains(text(), 'Pending Payment')]"
ele_pending_shipment_text = u"//span[contains(text(), '待发货') or contains(text(), 'Pending Shipment')]"
ele_shipped_text = u"//span[contains(text(), '已发货') or contains(text(), 'Shipped')]"
ele_to_review_text = u"//span[contains(text(), '待晒单') or contains(text(), 'To Review')]"
ele_returns_after_sales_text = u"//span[contains(text(), '退换/售后') or contains(text(), 'Returns/After-sales')]"

# 账户页面其他元素
ele_account_avatar = u"//div[@data-testid='account-avatar']"
ele_account_name = u"//div[@data-testid='account-name']"
ele_account_settings = u"//div[@data-testid='account-settings']"

# 底部导航
ele_bottom_nav_me = u"//div[@data-testid='bottom-nav-me']"
ele_bottom_nav_home = u"//div[@data-testid='bottom-nav-home']"
ele_bottom_nav_cart = u"//div[@data-testid='bottom-nav-cart']"
ele_bottom_nav_category = u"//div[@data-testid='bottom-nav-category']"

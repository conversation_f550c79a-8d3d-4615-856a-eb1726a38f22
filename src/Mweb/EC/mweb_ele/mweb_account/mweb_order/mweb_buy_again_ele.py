
"""
<AUTHOR>  suqin
@Version        :  V1.0.0
------------------------------------
@File           :   mweb_buy_again_ele.py
@Description    :  再来一单页面元素定义
@CreateTime     :  2025/6/8 11:31
@Software       :  PyCharm
------------------------------------

"""
# 再来一单页面
buy_again_page_close = "btn-drawer-close"
buy_again_page_info = "wid-account-buy-again-date-info"
buy_again_page = "wid-order-buy-again-content"
buy_again_available = "wid-order-buy-again-content-available"
# 再来一单页面-商品列表
buy_again_available_product = "wid-order-buy-again-product-item"
buy_again_unavailable = "wid-order-buy-again-content-inavailable"
buy_again_item_checkbox = "wid-order-buy-again-product-item-checkbox"
# 再来一单页面-加入购物车按钮
# H5
buy_again_add_cart_button = "wid-order-buy-again-submit"
# pc
buy_again_add_cart_btn= "btn-account-buy-again-add-cart"
# 再来一单页面-全选按钮
# H5
buy_again_chose_all = "wid-order-buy-again-chose-all"
# pc
buy_again_select_all = "wid-account-buy-again-select-all"

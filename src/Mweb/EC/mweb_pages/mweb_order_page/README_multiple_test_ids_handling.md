# 多个可能元素ID的处理优化

## 问题描述

在订单验证过程中，某些元素可能有多个不同的test-id，例如：
- 普通订单的预计送达信息：`wid-order-list-delivery-date-label`
- Pantry订单的预计送达信息：`wid-order-list-pantry-delivery-date-label`

原始代码只检查一种test-id，当遇到另一种类型时会导致断言失败。

## 解决方案

### 1. 创建通用辅助方法

在 `MWebOrderPage` 类中添加了 `_check_element_visibility_by_multiple_test_ids()` 方法：

```python
def _check_element_visibility_by_multiple_test_ids(self, order_item, test_ids: list, element_description: str = "元素"):
    """
    检查多个可能的test-id中是否有任何一个元素可见
    Args:
        order_item: 订单卡片元素
        test_ids: 可能的test-id列表
        element_description: 元素描述，用于错误信息
    Returns:
        bool: 是否找到可见的元素
    """
    for test_id in test_ids:
        try:
            element = order_item.get_by_test_id(test_id)
            if element.is_visible():
                log.info(f"找到{element_description}: {test_id}")
                return True
        except:
            continue
    
    log.error(f"{element_description}不存在（尝试了: {', '.join(test_ids)}）")
    return False
```

### 2. 优化原始断言代码

**原始代码（第206行）**：
```python
# 如果是pantry wid-order-list-pantry-delivery-date-label ，这里会报错
assert order_item.get_by_test_id("wid-order-list-delivery-date-label").is_visible(), " 预计送达信息不存在"
```

**优化后代码**：
```python
# 处理两种可能的delivery date label：普通订单和pantry订单
delivery_date_test_ids = [
    "wid-order-list-delivery-date-label",
    "wid-order-list-pantry-delivery-date-label"
]
assert self._check_element_visibility_by_multiple_test_ids(
    order_item, delivery_date_test_ids, "预计送达信息"
), f"预计送达信息不存在（尝试了: {', '.join(delivery_date_test_ids)}）"
```

## 优化效果

### 1. 兼容性提升
- ✅ 支持普通订单的 `wid-order-list-delivery-date-label`
- ✅ 支持Pantry订单的 `wid-order-list-pantry-delivery-date-label`
- ✅ 可以轻松扩展支持更多类型的test-id

### 2. 错误信息优化
- 提供详细的错误信息，显示尝试过的所有test-id
- 成功时记录找到的具体test-id，便于调试

### 3. 代码复用性
- 通用的辅助方法可以用于处理其他类似的多元素ID情况
- 减少重复代码，提高维护性

### 4. 稳定性提升
- 避免因单一test-id不存在而导致的测试失败
- 更好的异常处理机制

## 使用示例

### 基本用法
```python
# 检查多个可能的按钮test-id
button_test_ids = [
    "wid-btn-primary",
    "wid-btn-secondary", 
    "wid-btn-alternative"
]

if self._check_element_visibility_by_multiple_test_ids(
    order_item, button_test_ids, "操作按钮"
):
    # 找到了可见的按钮
    pass
else:
    # 所有按钮都不可见
    pass
```

### 在断言中使用
```python
# 断言至少有一个元素可见
assert self._check_element_visibility_by_multiple_test_ids(
    order_item, 
    ["test-id-1", "test-id-2", "test-id-3"], 
    "重要元素"
), "重要元素不存在"
```

## 扩展性

这个方法可以轻松扩展到其他类似场景：

1. **不同订单类型的状态标签**
2. **不同地区的配送信息**
3. **不同版本的UI元素**
4. **A/B测试中的不同元素**

## 最佳实践

1. **优先级排序**: 将最常见的test-id放在列表前面
2. **描述性命名**: 使用有意义的 `element_description` 参数
3. **日志记录**: 利用方法内置的日志功能进行调试
4. **错误处理**: 结合断言使用，提供清晰的错误信息

## 兼容性

- 完全向后兼容现有代码
- 不影响其他测试用例的运行
- 可以逐步应用到其他需要类似处理的地方

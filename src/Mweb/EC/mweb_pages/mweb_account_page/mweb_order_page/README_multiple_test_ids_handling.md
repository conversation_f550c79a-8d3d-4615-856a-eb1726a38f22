# 多个可能元素ID的处理优化

## 问题描述

在订单验证过程中，某些元素可能有多个不同的test-id，例如：
- 普通订单的预计送达信息：`wid-order-list-delivery-date-label`
- Pantry订单的预计送达信息：`wid-order-list-pantry-delivery-date-label`

原始代码只检查一种test-id，当遇到另一种类型时会导致断言失败。

## 解决方案

使用简单的try-except结构来处理多个可能的元素ID：

**原始代码（第206行）**：
```python
# 如果是pantry wid-order-list-pantry-delivery-date-label ，这里会报错
assert order_item.get_by_test_id("wid-order-list-delivery-date-label").is_visible(), " 预计送达信息不存在"
```

**优化后代码**：
```python
# 处理两种可能的delivery date label：普通订单和pantry订单
try:
    # 先尝试普通订单的delivery date label
    assert order_item.get_by_test_id("wid-order-list-delivery-date-label").is_visible(), "预计送达信息不存在"
except:
    # 如果普通的不存在，尝试pantry订单的delivery date label
    assert order_item.get_by_test_id("wid-order-list-pantry-delivery-date-label").is_visible(), "预计送达信息不存在（尝试了wid-order-list-delivery-date-label和wid-order-list-pantry-delivery-date-label）"
```

## 优化效果

### 1. 兼容性提升
- ✅ 支持普通订单的 `wid-order-list-delivery-date-label`
- ✅ 支持Pantry订单的 `wid-order-list-pantry-delivery-date-label`
- ✅ 简单的try-except结构，易于理解和维护

### 2. 错误信息优化
- 提供详细的错误信息，显示尝试过的所有test-id
- 当第一个元素不存在时，自动尝试第二个元素

### 3. 代码简洁性
- 使用简单的try-except结构，不需要额外的方法
- 代码逻辑清晰，易于阅读和维护

### 4. 稳定性提升
- 避免因单一test-id不存在而导致的测试失败
- 优雅的异常处理机制

## 使用示例

这种模式可以应用到其他类似场景：

```python
# 处理多个可能的按钮test-id
try:
    # 先尝试主要按钮
    assert order_item.get_by_test_id("wid-btn-primary").is_visible(), "按钮不存在"
except:
    # 如果主要按钮不存在，尝试备用按钮
    assert order_item.get_by_test_id("wid-btn-secondary").is_visible(), "按钮不存在（尝试了primary和secondary）"
```

## 扩展性

这种模式可以轻松扩展到其他类似场景：

1. **不同订单类型的状态标签**
2. **不同地区的配送信息**
3. **不同版本的UI元素**
4. **A/B测试中的不同元素**

## 最佳实践

1. **优先级排序**: 将最常见的test-id放在try块中
2. **清晰的错误信息**: 在except块的断言中说明尝试过的所有test-id
3. **简洁性**: 保持代码简单，避免过度复杂化

## 兼容性

- 完全向后兼容现有代码
- 不影响其他测试用例的运行
- 简单的结构，易于理解和维护

"""
<AUTHOR>  Assistant
@Version        :  V1.0.0
------------------------------------
@File           :   mweb_account_page.py
@Description    :  Mobile Account页面封装
@CreateTime     :  2025/1/24
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2025/1/24
"""

from playwright.sync_api import Page
from src.config.base_config import TEST_URL
from src.config.weee.log_help import log
from src.Mweb.EC.mweb_pages.mweb_page_common.mweb_common_page import MWebCommonPage
from src.Mweb.EC.mweb_ele.mweb_account import mweb_account_ele
from src.Mweb.EC.mweb_ele.mweb_order import mweb_order_list_ele


class MWebAccountPage(MWebCommonPage):
    """
    Mobile Account页面类，封装账户页面相关操作
    """
    
    def __init__(self, page: Page, header, browser_context, page_url: str = None):
        super().__init__(page, header)
        self.bc = browser_context
        # 直接进入账户页面
        if page_url:
            self.page.goto(TEST_URL + page_url + "?joinEnki=true")
        else:
            self.page.goto(TEST_URL + "/account" + "?joinEnki=true")
        self.page.wait_for_timeout(3000)

    def verify_my_orders_section(self):
        """
        验证我的订单模块是否显示正确
        Returns:
            bool: 验证是否成功
        """
        try:
            # 验证我的订单标题
            my_orders_title = self.FE.ele("//div[contains(text(), 'My orders') or contains(text(), '我的订单')]")
            if not my_orders_title or not my_orders_title.is_visible():
                log.error("我的订单标题未找到或不可见")
                return False
            
            # 验证我的订单箭头按钮
            my_orders_arrow = self.FE.ele("//div[contains(text(), 'My orders')]/following-sibling::*[contains(@class, 'icon') or name()='svg']")
            if not my_orders_arrow:
                # 尝试其他可能的箭头元素定位
                my_orders_arrow = self.FE.ele("//div[contains(text(), 'My orders')]/..//*[contains(@class, 'arrow') or contains(@class, 'chevron') or contains(@class, 'right')]")
            
            if not my_orders_arrow or not my_orders_arrow.is_visible():
                log.warning("我的订单箭头按钮未找到，但继续验证其他元素")
            
            log.info("我的订单模块验证成功")
            return True
            
        except Exception as e:
            log.error(f"验证我的订单模块时发生异常: {str(e)}")
            return False

    def verify_order_status_shortcuts(self):
        """
        验证订单状态快捷入口是否显示正确
        Returns:
            bool: 验证是否成功
        """
        try:
            # 根据图片，验证5个订单状态：Pending, Unshipped, Shipped, To review, Returns
            status_texts = ["Pending", "Unshipped", "Shipped", "To review", "Returns"]
            
            for status in status_texts:
                status_element = self.FE.ele(f"//span[contains(text(), '{status}')]")
                if not status_element or not status_element.is_visible():
                    log.error(f"订单状态 '{status}' 未找到或不可见")
                    return False
                log.info(f"订单状态 '{status}' 验证成功")
            
            log.info("所有订单状态快捷入口验证成功")
            return True
            
        except Exception as e:
            log.error(f"验证订单状态快捷入口时发生异常: {str(e)}")
            return False

    def click_my_orders_arrow(self):
        """
        点击我的订单箭头，进入订单列表页面
        Returns:
            bool: 操作是否成功
        """
        try:
            # 点击我的订单区域或箭头
            my_orders_section = self.FE.ele("//div[contains(text(), 'My orders')]/..")
            if my_orders_section and my_orders_section.is_visible():
                my_orders_section.click()
                self.page.wait_for_timeout(3000)
                log.info("成功点击我的订单区域")
                return True
            else:
                log.error("我的订单区域未找到")
                return False
                
        except Exception as e:
            log.error(f"点击我的订单箭头时发生异常: {str(e)}")
            return False

    def click_order_status_shortcut(self, status_name: str):
        """
        点击指定的订单状态快捷入口
        Args:
            status_name: 状态名称 (Pending, Unshipped, Shipped, To review, Returns)
        Returns:
            bool: 操作是否成功
        """
        try:
            status_element = self.FE.ele(f"//span[contains(text(), '{status_name}')]/..")
            if status_element and status_element.is_visible():
                status_element.click()
                self.page.wait_for_timeout(3000)
                log.info(f"成功点击订单状态 '{status_name}'")
                return True
            else:
                log.error(f"订单状态 '{status_name}' 未找到")
                return False
                
        except Exception as e:
            log.error(f"点击订单状态 '{status_name}' 时发生异常: {str(e)}")
            return False

    def verify_order_list_page_with_tab(self, expected_tab: str):
        """
        验证是否进入订单列表页面并选中了正确的tab
        Args:
            expected_tab: 期望选中的tab名称
        Returns:
            bool: 验证是否成功
        """
        try:
            # 验证URL包含订单列表路径
            if "/order/list" not in self.page.url:
                log.error("未成功跳转到订单列表页面")
                return False
            
            # 验证对应的tab是否被选中
            tab_mapping = {
                "all": "order-tab-all",
                "pending": "order-tab-pending-payment", 
                "unshipped": "order-tab-unshipped",
                "shipped": "order-tab-shipped",
                "to_review": "order-tab-to-review",
                "returns": "order-tab-returns-after-sales"
            }
            
            if expected_tab in tab_mapping:
                tab_element = self.page.get_by_test_id(tab_mapping[expected_tab])
                if tab_element.is_visible() and tab_element.get_attribute("aria-selected") == "true":
                    log.info(f"成功验证 '{expected_tab}' tab被选中")
                    return True
                else:
                    log.error(f"'{expected_tab}' tab未被正确选中")
                    return False
            else:
                log.error(f"未知的tab类型: {expected_tab}")
                return False
                
        except Exception as e:
            log.error(f"验证订单列表页面时发生异常: {str(e)}")
            return False

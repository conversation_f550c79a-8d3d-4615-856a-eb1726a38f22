from playwright.sync_api import Page
from src.Mweb.EC.mweb_ele.mweb_mkpl_vender.mweb_mkpl_vender_ele import *
from src.config.weee.log_help import log


class VendorHomepage:
    def __init__(self, page: Page):
        self.page = page

    def navigate_to_vendor(self):
        """访问商家页面"""
        self.page.goto("https://tb1.sayweee.net/zh/mkpl/vendor/9755")
        self.page.wait_for_timeout(2000)

    def check_required_elements_exist(self):
        """检查页面必须存在的元素"""
        required_elements = [
            ele_seller_title,
            ele_search_btn,
            ele_seller_estimate_range,
            ele_seller_reminder_shipping,
            ele_send_message_to_seller,
            ele_share_seller_info
        ]
        
        for element in required_elements:
            locator = self.page.get_by_test_id(element)
            locator.wait_for(state="visible", timeout=5000)
            print(f"元素检查成功: {element}")

    def click_all_tab_if_exists(self):
        """等待5秒后点击全部标签页，如果不存在则跳过"""
        self.page.wait_for_timeout(5000)
        
        locator = self.page.locator(ele_seller_tab_all_backup)
        if locator.count() > 0:
            locator.first.click(force=True)
            return True
        return False
    
    def scroll_and_check_back_to_top(self):
        """等待5秒，滚动一次列表，等待8秒，检查一键置顶按钮"""
        self.page.wait_for_timeout(5000)
        self.page.mouse.wheel(0, 1000)
        self.page.wait_for_timeout(8000)
        
        locator = self.page.get_by_test_id(ele_seller_back_to_top)
        if locator.count() > 0:
            locator.first.click(force=True)
            return True
        return False
    
    def check_and_click_seller_detail(self):
        """检查并点击商家详情按钮，然后点击相关标签页"""
        # 检查访问商家详情按钮是否存在
        visit_detail_btn = self.page.get_by_test_id(ele_visit_seller_detail)
        if visit_detail_btn.count() == 0:
            log.info("访问商家详情按钮不存在，结束操作")
            return False
        
        # 点击访问商家详情按钮
        visit_detail_btn.click()
        log.info("点击访问商家详情按钮成功")
        
        # 等待页面加载
        self.page.wait_for_timeout(3000)
        
        # 依次检查并点击标签页
        tabs_to_check = [
            (ele_seller_detail_feedback, "评价标签页"),
            (ele_seller_shipping_return, "运输标签页"),
            (ele_seller_about_tab, "关于标签页")
        ]
        
        clicked_count = 0
        for tab_id, tab_name in tabs_to_check:
            self.page.wait_for_timeout(5000)  # 每次点击间隔5秒
            tab = self.page.get_by_test_id(tab_id)
            if tab.count() > 0:
                tab.click()
                log.info(f"点击{tab_name}成功")
                clicked_count += 1
            else:
                log.info(f"{tab_name}不存在，跳过")
        
        log.info(f"商家详情标签页操作完成，共点击{clicked_count}个标签页")
        return True

    def click_reviews_tab_if_exists(self):
        """等待5秒后点击晒单标签页，如果不存在则跳过"""
        self.page.wait_for_timeout(5000)
        
        locator = self.page.locator(ele_seller_tab_reviews)
        if locator.count() > 0:
            locator.first.click(force=True)
            return True
        return False

    def click_explore_tab_if_exists(self):
        """等待5秒后点击探索标签页，如果不存在则跳过"""
        self.page.wait_for_timeout(5000)
        
        locator = self.page.locator(ele_seller_tab_explore)
        if locator.count() > 0:
            locator.first.click(force=True)
            return True
        return False

from typing import Literal
from urllib.parse import urlparse, parse_qs

from playwright.sync_api import Page

from src.Mweb.EC.mweb_ele.mweb_mkpl_all_store.mweb_mkpl_all_store_ele import (
    ele_recommend_tab,
    ele_japan_tab,
    ele_korea_tab,
    ele_usa_tab,
    ele_other_tab
)
from src.Mweb.EC.mweb_pages.mweb_page_common.mweb_page_common_operations import PageH5CommonOperations
from src.common.commfunc import empty_cart
from src.common.commonui import home_init_h5, scroll_one_page_until
from src.config.base_config import TEST_URL
from src.config.weee.log_help import log


class MWebMkplAllStorePage(PageH5CommonOperations):
    """
    全球购所有商店页面操作类
    """
    def __init__(self, page: Page, header, browser_context):
        super().__init__(page, header)
        self.bc = browser_context

        # 清空购物车
        try:
            empty_cart(self.header)
        except Exception as e:
            log.info("清空购物车发生异常" + str(e))

        # 访问指定地址
        self.page.goto("https://tb1.sayweee.net/zh/mkpl/global?mode=sub_page&hide_activity_pop=1")
        self.page.wait_for_timeout(5000)
        # 关闭首页广告
        self.close_advertisement_in_homepage()

    def click_recommend_tab(self):
        """
        点击推荐tab
        """
        # 使用get_by_test_id定位元素
        tab_element = self.page.get_by_test_id(ele_recommend_tab)
        if tab_element.count() == 0:
            raise AssertionError("未找到推荐tab元素")

        # 如果元素不在可视区域内，需要滚动到元素位置
        if not tab_element.is_visible():
            tab_element.scroll_into_view_if_needed()
            self.page.wait_for_timeout(1000)

        # 点击元素
        tab_element.click()
        log.info("已点击推荐tab")

        # 等待页面加载
        self.page.wait_for_timeout(5000)

    def click_recommend_tab_and_scroll(self):
        """
        点击推荐tab并在推荐tab下滚动一页
        """
        # 先点击推荐tab
        self.click_recommend_tab()

        # 在推荐tab下滚动一页
        self.scroll_down_one_page_in_recommend_tab()

    def scroll_down_one_page_in_recommend_tab(self):
        """
        在推荐tab下滚动一页
        """
        try:
            log.info("🔄 开始在推荐tab下滚动一页")

            # 获取当前滚动位置
            current_scroll_y = self.page.evaluate("window.pageYOffset")
            log.info(f"📍 当前滚动位置: {current_scroll_y}px")

            # 获取视窗高度
            viewport_height = self.page.evaluate("window.innerHeight")
            log.info(f"📏 视窗高度: {viewport_height}px")

            # 计算滚动距离（一页的高度）
            scroll_distance = viewport_height
            target_scroll_y = current_scroll_y + scroll_distance

            log.info(f"🎯 目标滚动位置: {target_scroll_y}px (滚动距离: {scroll_distance}px)")

            # 执行平滑滚动
            self.page.evaluate(f"""
                window.scrollTo({{
                    top: {target_scroll_y},
                    behavior: 'smooth'
                }});
            """)

            # 等待滚动动画完成
            self.page.wait_for_timeout(2000)

            # 验证滚动是否成功
            new_scroll_y = self.page.evaluate("window.pageYOffset")
            log.info(f"✅ 滚动完成，新位置: {new_scroll_y}px")

            # 检查滚动效果
            actual_scroll_distance = new_scroll_y - current_scroll_y
            if actual_scroll_distance > 0:
                log.info(f"✅ 在推荐tab下成功滚动了 {actual_scroll_distance}px")
                return True
            else:
                log.warning("⚠️ 滚动位置未发生变化，可能已到达页面底部")
                return False

        except Exception as e:
            log.error(f"❌ 在推荐tab下滚动时发生异常: {str(e)}")
            return False

    def click_japan_tab(self):
        """
        点击日本tab
        """
        # 使用get_by_test_id定位元素
        tab_element = self.page.get_by_test_id(ele_japan_tab)
        if tab_element.count() == 0:
            raise AssertionError("未找到日本tab元素")

        # 如果元素不在可视区域内，需要滚动到元素位置
        if not tab_element.is_visible():
            tab_element.scroll_into_view_if_needed()
            self.page.wait_for_timeout(1000)

        # 点击元素
        tab_element.click()
        log.info("已点击日本tab")

        # 等待页面加载
        self.page.wait_for_timeout(5000)

    def click_korea_tab(self):
        """
        点击韩国tab
        """
        # 使用get_by_test_id定位元素
        tab_element = self.page.get_by_test_id(ele_korea_tab)
        if tab_element.count() == 0:
            raise AssertionError("未找到韩国tab元素")

        # 如果元素不在可视区域内，需要滚动到元素位置
        if not tab_element.is_visible():
            tab_element.scroll_into_view_if_needed()
            self.page.wait_for_timeout(1000)

        # 点击元素
        tab_element.click()
        log.info("已点击韩国tab")

        # 等待页面加载
        self.page.wait_for_timeout(5000)

    def click_usa_tab(self):
        """
        点击美国tab
        """
        # 使用get_by_test_id定位元素
        tab_element = self.page.get_by_test_id(ele_usa_tab)
        if tab_element.count() == 0:
            raise AssertionError("未找到美国tab元素")

        # 如果元素不在可视区域内，需要滚动到元素位置
        if not tab_element.is_visible():
            tab_element.scroll_into_view_if_needed()
            self.page.wait_for_timeout(1000)

        # 点击元素
        tab_element.click()
        log.info("已点击美国tab")

        # 等待页面加载
        self.page.wait_for_timeout(5000)

    def click_other_tab(self):
        """
        点击其他tab
        """
        # 使用get_by_test_id定位元素
        tab_element = self.page.get_by_test_id(ele_other_tab)
        if tab_element.count() == 0:
            raise AssertionError("未找到其他tab元素")

        # 如果元素不在可视区域内，需要滚动到元素位置
        if not tab_element.is_visible():
            tab_element.scroll_into_view_if_needed()
            self.page.wait_for_timeout(1000)

        # 点击元素
        tab_element.click()
        log.info("已点击其他tab")

        # 等待页面加载
        self.page.wait_for_timeout(5000)

    def click_all_tabs_in_sequence(self):
        """
        按顺序点击所有tab，每个tab点击间隔5秒
        按照 "推荐" -> "日本" -> "韩国" -> "美国" -> "其他" -> "推荐" 的顺序依次点击校验
        最后回到推荐tab结束，并校验URL中的key参数
        """
        log.info("开始按顺序点击所有tab")

        # 定义tab点击顺序、对应的元素选择器和URL key参数
        # 注意：推荐tab在初始化时已经是默认激活的，所以从日本tab开始
        tabs_sequence = [
            ("日本", ele_japan_tab, "japan"),
            ("韩国", ele_korea_tab, "korea"),
            ("美国", ele_usa_tab, "usa"),
            ("其他", ele_other_tab, "others")
        ]

        # 按顺序点击每个tab
        for i, (tab_name, tab_element_selector, expected_key) in enumerate(tabs_sequence):
            log.info(f"第{i+1}步：准备点击 {tab_name} tab")

            # 点击tab
            self._click_single_tab(tab_element_selector, tab_name)

            # 验证tab是否被激活
            self._verify_tab_active(tab_name)

            # 只在expected_key不为None时才验证URL中的key参数
            if expected_key is not None:
                self._verify_url_key_parameter(tab_name, expected_key)
            else:
                log.info(f"{tab_name}tab 跳过URL验证")

            log.info(f"第{i+1}步：已成功点击 {tab_name} tab 并验证完成")

            # 等待页面内容加载完成
            self.page.wait_for_timeout(5000)

        # 所有tab点击完成后，回到推荐tab
        log.info("第5步：所有tab点击完成，现在回到推荐tab")
        self._click_single_tab(ele_recommend_tab, "推荐")
        self._verify_tab_active("推荐")
        # 推荐tab不验证URL
        log.info("推荐tab 跳过URL验证")
        log.info("第5步：已成功回到推荐tab")

        # 等待页面内容加载完成
        self.page.wait_for_timeout(5000)

        log.info("已完成所有tab的顺序点击操作，最后回到推荐tab")

    def _click_single_tab(self, tab_element_selector, tab_name):
        """
        点击单个tab

        Args:
            tab_element_selector: tab元素选择器
            tab_name: tab名称，用于日志记录
        """
        # 使用get_by_test_id定位元素
        tab_element = self.page.get_by_test_id(tab_element_selector)
        element_count = tab_element.count()
        log.info(f"查找{tab_name}tab元素: {tab_element_selector}, 找到{element_count}个元素")
        
        if element_count == 0:
            # 调试信息：打印页面上所有包含'btn-all-store'的元素
            log.info("调试信息：查找页面上所有包含'btn-all-store'的元素")
            all_buttons = self.page.locator("[data-testid*='btn-all-store']")
            button_count = all_buttons.count()
            log.info(f"找到{button_count}个包含'btn-all-store'的元素")
            
            for i in range(button_count):
                button = all_buttons.nth(i)
                test_id = button.get_attribute("data-testid")
                text = button.inner_text() if button.is_visible() else "[不可见]"
                log.info(f"元素{i+1}: data-testid='{test_id}', 文本='{text}'")
            
            raise AssertionError(f"未找到{tab_name}tab元素: {tab_element_selector}")

        # 如果元素不在可视区域内，需要滚动到元素位置
        if not tab_element.is_visible():
            tab_element.scroll_into_view_if_needed()
            self.page.wait_for_timeout(1000)

        # 点击元素
        tab_element.click()
        log.info(f"已点击{tab_name}tab")

        # 等待tab切换动画完成
        self.page.wait_for_timeout(2000)

    def _verify_tab_active(self, tab_name):
        """
        验证tab是否被激活

        Args:
            tab_name: tab名称，用于日志记录
        """
        # 等待页面内容更新
        self.page.wait_for_timeout(1000)

        # 这里可以添加更多的验证逻辑，比如检查tab的激活状态、页面内容等
        # 目前只做基本的日志记录
        log.info(f"{tab_name}tab 已激活，页面内容正在加载")

    def _verify_url_key_parameter(self, tab_name, expected_key):
        """
        验证URL中的key参数是否正确

        Args:
            tab_name: tab名称，用于日志记录
            expected_key: 期望的key参数值
        """
        # 等待URL更新
        self.page.wait_for_timeout(1000)

        # 获取当前页面URL
        current_url = self.page.url
        log.info(f"当前页面URL: {current_url}")

        # 解析URL参数
        parsed_url = urlparse(current_url)
        query_params = parse_qs(parsed_url.query)

        # 获取key参数
        actual_key = query_params.get('key', [None])[0]

        # 断言key参数是否正确
        assert actual_key == expected_key, f"{tab_name}tab的URL key参数验证失败: 期望 '{expected_key}', 实际 '{actual_key}'"

        log.info(f"{tab_name}tab的URL key参数验证成功: key={actual_key}")

    def _click_tab_with_validation(self, tab_element_selector, tab_name):
        """
        点击指定tab并进行验证（保留给单独的tab测试方法使用）

        Args:
            tab_element_selector: tab元素选择器
            tab_name: tab名称，用于日志记录
        """
        # 使用get_by_test_id定位元素
        tab_element = self.page.get_by_test_id(tab_element_selector)
        if tab_element.count() == 0:
            raise AssertionError(f"未找到{tab_name}tab元素")

        # 如果元素不在可视区域内，需要滚动到元素位置
        if not tab_element.is_visible():
            tab_element.scroll_into_view_if_needed()
            self.page.wait_for_timeout(1000)

        # 点击元素
        tab_element.click()
        log.info(f"已点击{tab_name}tab")

        # 验证tab是否被激活（可以根据实际页面情况添加更多验证逻辑）
        self.page.wait_for_timeout(2000)  # 等待tab切换动画完成

from playwright.sync_api import Page
from src.config.base_config import TEST_URL
from src.Dweb.EC.dweb_ele.dweb_help import dweb_help_ele
from src.config.weee.log_help import log
from src.Dweb.EC.dweb_pages.dweb_common_page import DWebCommonPage
from src.common.commonui import scroll_one_page_until


class HelpPage(DWebCommonPage):
    def __init__(self, page: Page, header, browser_context, page_url: str = None):
        super().__init__(page, header)
        self.bc = browser_context
        # 直接进入帮助中心页面
        self.page.goto(TEST_URL + "/account/help")
        # 等待页面加载完成
        self.page.wait_for_load_state("load")

    def click_hot_issues(self):
        """
        点击热点问题分类
        """
        # 滚动到热点问题分类位置
        scroll_one_page_until(self.page, dweb_help_ele.ele_hot_issues_category)
        # 点击热点问题分类
        self.FE.ele(dweb_help_ele.ele_hot_issues_category).click()

    def click_change_delivery_address(self):
        """
        点击如何修改订单地址文章
        """
        # 滚动到文章位置
        scroll_one_page_until(self.page, dweb_help_ele.ele_change_delivery_address_link)
        # 点击文章链接
        self.FE.ele(dweb_help_ele.ele_change_delivery_address_link).click()

    def verify_article_title(self):
        """
        验证文章页面标题
        """
        # 等待页面加载完成
        self.page.wait_for_load_state("load")
        # 滚动到标题位置
        scroll_one_page_until(self.page, dweb_help_ele.ele_change_delivery_address_title)
        # 获取标题元素
        article_title = self.FE.ele(dweb_help_ele.ele_change_delivery_address_title)
        # 验证标题是否可见
        assert article_title.is_visible(), "文章标题未显示"
        # 验证标题文本
        assert article_title.text_content() == "How to change the delivery address?", "文章标题不匹配"

    def click_confirm_button(self):
        """
        点击确认按钮
        """
        # 滚动到确认按钮位置
        scroll_one_page_until(self.page, dweb_help_ele.ele_confirm_button)
        # 点击确认按钮
        self.FE.ele(dweb_help_ele.ele_confirm_button).click()

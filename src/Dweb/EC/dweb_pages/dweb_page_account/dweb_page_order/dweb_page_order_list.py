import re

from playwright.sync_api import Page

from src.Dweb.EC.dweb_ele.dweb_account.dweb_order import dweb_order_list_ele
from src.Dweb.EC.dweb_pages.dweb_common_page import DWebCommonPage
from src.common.commonui import close_advertise_on_home
from src.config.base_config import TEST_URL
from src.config.weee.log_help import log


class DWebOrderListPage(DWebCommonPage):
    """
    这个类主要封装了PC web端对订单列表页面的操作
    """
    def __init__(self, page: Page, header, browser_context, zipcode: str = "98011", page_url: str = None):

        """
        构造方法
        """
        super().__init__(page, header)
        self.bc = browser_context
        self.page.goto(TEST_URL + page_url)
        self.page.wait_for_timeout(10000)
        close_advertise_on_home(self.page)
        # # 获取顶部语言
        # self.home_page_switch_lang(lang="English")
        # # 获取顶部zipocde
        # self.home_page_switch_zipcode(zipcode)


    def assert_order_tab_info(self, order_item, order_tab):
        """
        验证seller订单卡片信息的公共断言方法
        Args:
            order_item: 订单卡片元素
        Returns:
            bool: 验证是否成功
        """
        status_text = order_item.get_by_test_id(dweb_order_list_ele.order_list_card_status_ele).text_content()
        if order_tab == "all":
            # 全部订单
            # 断言全部订单里不存在状态是Cancelled的订单
            assert status_text not in ("Canceled", "Cancelled"), f"全部订单里有Cancelled订单"
        if order_tab == "1":
            # 待支付订单
            # 断言全部订单里不存在状态是Cancelled的订单
            assert status_text in ("Pending", "待支付"), f"待支付订单里状态不对"
            assert order_item.get_by_test_id("wid-order-btn-cancel").is_visible(), "待支付订单里没有取消按钮"
            assert order_item.get_by_test_id("wid-order-btn-pay").is_visible(), "待支付订单里没有pay按钮"


        elif order_tab == "2":
            # 待发货订单
            assert status_text in ("Ready to Ship"), f"待发货订单里状态不对"

        elif order_tab == "3":
            # 已发货订单
            assert status_text in ("Delivered"), f"已发货订单里状态不对"

        elif order_tab == "4":
            # 已取消订单
            assert status_text in ("Canceled", "Cancelled"), f"已取消订单状态不对"
        elif order_tab == "6":
            # 待评价订单
            assert order_item.get_by_test_id("wid-order-btn-post").is_visible(), "待评价订单里没有去晒单按钮"


        return True

    def assert_order_card_info(self, order_item,order_type):
        """
        验证seller订单卡片信息的公共断言方法
        Args:
            order_item: 订单卡片元素
        Returns:
            bool: 验证是否成功
        """
        # 验证订单内容信息
        assert order_item.get_by_test_id(dweb_order_list_ele.order_list_card_status_ele).is_visible(), "订单内容信息不存在"
        assert order_item.get_by_test_id("wid-order-list-product-list").is_visible(), "订单内容信息不存在"
        product_image = order_item.get_by_test_id("wid-order-list-product-image").all()
        # for item in product_image:
        #     img = item.locator("img")
        #     # assert img.count() > 0, "图片元素不存在"
        #     assert img.get_attribute("src") is not None, "图片src属性不存在"
        # seller 订单
        if order_type == "S":
            assert order_item.get_by_test_id("wid-order-list-seller-delivery-date-label").is_visible(), " 预计送达信息不存在"
            assert order_item.get_by_test_id("wid-order-list-vendor-title-link").is_visible(), "seller订单icon信息不存在"
            assert order_item.get_by_test_id("wid-order-btn-buy_again").is_visible(), "积分订单的存在再来一单按钮"
        if order_type == "R":
            # 处理两种可能的delivery date label：普通订单和pantry订单
            try:
                # 先尝试普通订单的delivery date label
                assert order_item.get_by_test_id("wid-order-list-delivery-date-label").is_visible(), "预计送达信息不存在"
            except:
                # 如果普通的不存在，尝试pantry订单的delivery date label
                assert order_item.get_by_test_id("wid-order-list-pantry-delivery-date-label").is_visible(), "预计送达信息不存在（尝试了wid-order-list-delivery-date-label和wid-order-list-pantry-delivery-date-label）"

            # assert order_item.get_by_test_id("wid-order-btn-buy_again").is_visible(), "积分订单的存在再来一单按钮"
        # 礼品卡订单
        elif order_type == "G":
            assert order_item.get_by_test_id("wid-order-list-giftcard-delivery-date-label").is_visible(), " 预计送达信息不存在"

            assert order_item.get_by_test_id("wid-order-list-giftcard-send-to").is_visible(), "giftcard订单发送给 XX信息不存在"
            assert not order_item.get_by_test_id("wid-order-btn-buy_again").is_visible(),"礼品卡存在再来一单按钮"
        # 积分订单
        elif order_type == "P":
            assert order_item.get_by_test_id("wid-order-list-effective-date-label").is_visible(), " 预计送达信息不存在"
            assert not order_item.get_by_test_id("wid-order-btn-buy_again").is_visible(),"积分订单的存在再来一单按钮"

        # 验证订单卡片上 Delivery date\Order number\Items\Total 信息
        assert order_item.get_by_test_id("wid-order-list-pick-date").is_visible(), " Delivery date信息不存在"
        assert order_item.get_by_test_id("wid-order-list-order-number-label").is_visible(), " Order number 信息不存在"
        assert order_item.get_by_test_id("wid-order-list-order-number").is_visible(), " Order number信息不存在"
        assert order_item.get_by_test_id("wid-order-list-totals-label").is_visible(), "Total 信息不存在"
        assert order_item.get_by_test_id(dweb_order_list_ele.order_list_card_status_ele).is_visible(), "订单状态不存在"
        # 验证Total金额
        totals_amount = order_item.get_by_test_id("wid-order-list-totals-amount").text_content()
        # 提取数字部分
        amount_matches = re.findall(r'\d+\.\d+', totals_amount)
        assert len(amount_matches) > 0 and float(amount_matches[0]) > 0, "Total 信息不存在或金额无效"
        return True

    def order_card_btn(self,order_item):
        # 再来一单按钮
        if order_item.get_by_test_id("wid-order-btn-buy_again").is_visible():
            # 点击按钮
            order_item.get_by_test_id("wid-order-btn-buy_again").click()
            self.page.wait_for_timeout(2000)
            # 验证是否进入再来一单页面
            assert self.page.locator(u"//div[@id = 'order-buy-again']").is_visible(), "未成功进入再来一单页面"
            self.page.go_back()
            return True
        # 售后按钮
        elif order_item.get_by_test_id("wid-order-btn-view_refund").is_visible():
            # 点击按钮
            order_item.get_by_test_id("wid-order-btn-view_refund").click()
            self.page.wait_for_timeout(2000)
            # 验证进入订单详情
            assert "/order/detail/" in self.page.url, "未成功进入订单详情页面"
            self.page.go_back()
            return True
        # 邀请好友按钮
        elif order_item.get_by_test_id("wid-order-btn-order_share").is_visible():
            # 点击按钮
            order_item.get_by_test_id("wid-order-btn-order_share").click()
            self.page.wait_for_timeout(2000)
            # 验证是否进入分享页面
            assert "/order/share/grocery/create" in self.page.url, "未成功进入分享页面"
            self.page.go_back()
            return True

        # 去晒单按钮
        elif order_item.get_by_test_id("wid-order-btn-post").is_visible():
            # 点击按钮
            order_item.get_by_test_id("wid-order-btn-post").click()
            self.page.wait_for_timeout(2000)
            # 验证是否进入晒单页面
            assert "/social/post-review/review-order" in self.page.url, "未成功进入晒单页面"
            self.page.go_back()
            return True
        # 查看物流按钮
        elif order_item.get_by_test_id("wid-order-btn-logistics_tracking").is_visible():
            # 点击按钮
            order_item.get_by_test_id("wid-order-btn-logistics_tracking").click()
            self.page.wait_for_timeout(2000)
            # 验证是否进入物流页面
            assert "/order/track/" in self.page.url, "未成功进入物流页面"
            self.page.go_back()
            return True
        elif order_item.get_by_test_id("wid-order-btn-cancel").is_visible():
            pass
            return True
            # # 点击取消按钮
            # order_item.get_by_test_id("wid-order-btn-cancel").click()
            # self.page.wait_for_timeout(2000)
            # # 验证是否进入物流页面
            # assert "/order/track/" in self.page.url, "未成功进入物流页面"
            # self.page.go_back()
        elif order_item.get_by_test_id("wid-order-btn-pay").is_visible():
            pass
            # # 点击取消按钮
            # order_item.get_by_test_id("wid-order-btn-pay").click()
            # self.page.wait_for_timeout(2000)
            # # 验证是否进入物流页面
            # assert "/order/track/" in self.page.url, "未成功进入物流页面"
            # self.page.go_back()

            return True
        else:
            return None


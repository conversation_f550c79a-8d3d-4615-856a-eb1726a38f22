import allure
import pytest
from playwright.sync_api import Page
from src.Dweb.EC.dweb_ele.dweb_help import dweb_help_ele
from src.Dweb.EC.dweb_pages.dweb_page_help.dweb_page_help import HelpPage
from src.config.weee.log_help import log


@allure.story("帮助中心-文章查看流程")
class TestHelpCenter:
    pytestmark = [pytest.mark.pchelp, pytest.mark.transaction]

    @allure.title("帮助中心-热点问题文章查看流程验证")
    def test_help_center_hot_issues(self, page: dict, pc_autotest_header, login_trace):
        """
        测试帮助中心热点问题文章查看流程
        步骤：
        1. 访问帮助中心页面
        2. 点击热点问题分类
        3. 点击如何修改订单地址文章
        4. 验证文章页面标题
        5. 点击有用按钮
        6. 验证按钮变为灰色
        """
        p: Page = page.get("page")
        c = page.get("context")

        # 初始化帮助中心页面
        help_page = HelpPage(p, pc_autotest_header, browser_context=c)
        log.info("成功初始化帮助中心页面")

        try:
            # 1. 点击热点问题分类
            help_page.click_hot_issues()
            log.info("成功点击热点问题分类")

            # 2. 点击如何修改订单地址文章
            help_page.click_change_delivery_address()
            log.info("成功点击如何修改订单地址文章")

            # 3. 验证文章页面标题
            help_page.verify_article_title()
            log.info("成功验证文章标题")

            # 4. 点击有用按钮
            help_page.click_confirm_button()
            log.info("成功点击有用按钮")

            # 5. 等待3秒
            p.wait_for_timeout(3000)

            # 6. 验证按钮变为灰色
            gray_button = p.locator(dweb_help_ele.ele_confirm_button_gray)
            assert gray_button.is_visible(), "按钮未变为灰色"
            log.info("成功验证按钮变为灰色")

        except Exception as e:
            log.error(f"测试过程中发生异常: {str(e)}")
            raise

import allure
import pytest

from playwright.sync_api import Page

from src.Dweb.EC.dweb_ele.dweb_account.dweb_order import dweb_order_list_ele, dweb_buy_again_ele
from src.Dweb.EC.dweb_pages.dweb_page_account.dweb_page_order.dweb_page_order_list import DWebOrderListPage
from src.config.weee.log_help import log


@allure.story("【108222】 订单列表已发货 tab订单流程验证")
class TestDWebMyUnShippedOrderUIUX:
    pytestmark = [pytest.mark.dweb_porder, pytest.mark.dweb_regression, pytest.mark.transaction, pytest.mark.suqin]

    @allure.title("【108222】 订单列表已发货 tab订单流程验证")
    def test_108222_dWeb_my_unshipped_order_ui_ux(self, page: dict, pc_autotest_header, login_trace):
        """
        【108222】 订单列表待发货 tab-订单流程验证
        测试步骤：
        1、切换到待发货tab 下，检查是否有待发货订单
        2、如果没有，点击start shopping，进入首页
        3、如果有查看待发货订单，验证订单信息正确，订单状态正确
        4、点击订单列表下各按钮
        5、点击订单进入订单详情，验证订单详情信息正确，各交互正确
        3、4、5、 依赖测试数据
        """
        p: Page = page.get("page")
        c = page.get("context")

        # 1. 进入订单列表页面
        order_page = DWebOrderListPage(p, pc_autotest_header, browser_context=c, page_url="/account/my_orders")
        p.wait_for_timeout(3000)
        log.info("成功进入订单列表页面")
        # 切换到待发货tab
        unshipped_tab = p.locator(dweb_order_list_ele.order_unshipped_tab_ele)
        assert unshipped_tab.is_visible(), "未找到待发货tab"
        # 切换到待发货tab
        unshipped_tab.click()
        p.wait_for_timeout(2000)
        # 验证已切换到待发货tab
        print(p.url)
        assert "filter_status=2" in p.url, "未切换到待发货tab"
        if p.locator(dweb_order_list_ele.order_list_card_ele).is_visible():
            log.info("待发货tab下有订单")
        else:
            log.info("待发货tab下没有订单")
            pytest.skip("待发货tab下没有订单，跳过测试")
        # 2. 检查存在待发货订单
        order_list_items = p.locator(dweb_order_list_ele.order_list_card_ele).all()
        # 2. 检查是否存在订单
        if len(order_list_items) == 0:
            log.warning("待发货tab下没有订单，无法继续测试")
            pytest.skip("待发货tab下没有订单，跳过测试")

        log.info(f"待发货tab下有{len(order_list_items)}个订单")
        for index, item in enumerate(order_list_items):
            # 验证前10 个订单内容信息
            assert item.locator(dweb_order_list_ele.order_list_card_status_ele).is_visible(), "订单内容信息不存在"
            assert item.locator(dweb_order_list_ele.order_list_card_detail_ele).is_visible(), "订单内容信息不存在"
            # seller 订单
            if item.locator(dweb_order_list_ele.order_list_card_title_ele).is_visible():
                assert item.locator(dweb_order_list_ele.order_list_card_title_icon_ele), "seller订单icon信息不存在"

            assert item.locator(
                dweb_order_list_ele.order_list_card_items_ele).is_visible(), "Delivery date\Order number\Items\Total 信息不存在"
            # 验证订单卡片上 Delivery date\Order number\Items\Total 信息
            for item2 in item.locator(dweb_order_list_ele.order_list_card_items_ele).all():
                for item3 in item2.locator(dweb_order_list_ele.order_list_card_item_ele).all():
                    assert item3.locator(dweb_order_list_ele.order_list_card_info_ele).is_visible()
                    assert item3.locator(dweb_order_list_ele.order_list_card_label_ele).is_visible()
            # 断言订单状态是Ready to Ship
            assert item.locator(dweb_order_list_ele.order_list_card_statu_ele).text_content() in (
                "Ready to Ship"), "订单状态不是Ready to Ship"
            assert item.locator(dweb_order_list_ele.order_list_card_product_ele).is_visible()
            # 查找订单列表中的按钮
            button_lists = item.locator(dweb_order_list_ele.order_list_cart_btn_ele).all()
            if len(button_lists) > 0:
                for index, item2 in enumerate(button_lists):
                    if item2.locator(u"//button").text_content() == "Cancel order":
                        log.info("找到 Cancel 按钮")
                        # 点击 Cancel 按钮
                        item2.locator(u"//button").click()
                        p.wait_for_timeout(2000)
                        assert p.locator(dweb_order_list_ele.cancel_order_pop_box).is_visible(), "未成功弹出取消订单pop"
                        # 关闭取消订单pop
                        p.locator(dweb_order_list_ele.cancel_order_pop_close_btn)
                    elif item2.locator(u"//button").text_content() == "Modify order":
                        log.info("找到 Modify order 按钮")
                        # 点击 Modify order 按钮
                        item2.locator(u"//button").click()
                        p.wait_for_timeout(2000)
                        assert "order/modify/" in p.url, "未成功跳转到修改订单页面"

                        p.go_back()
                    elif item2.locator(u"//button").text_content() == "Share my order":
                        log.info("找到 Share my order 按钮")
                        # 点击 Share my order 按钮
                        item2.locator(u"//button").click()
                        p.wait_for_timeout(2000)
                        assert "/order/share/grocery/" in p.url, "未成功跳转到订单分享页面"
                        p.go_back()
                    elif item2.locator(u"//button").text_content() == "Reorder":
                        log.info("找到 Reorder 按钮")
                        # 点击 Reorder 按钮
                        item2.locator(u"//button").click()
                        p.wait_for_timeout(2000)
                        assert p.locator(dweb_buy_again_ele.buy_again_reorder_header_ele).is_visible(), "未成功拉出再来一单选择商品页面"
                        p.locator(dweb_buy_again_ele.buy_again_x_btn).click()
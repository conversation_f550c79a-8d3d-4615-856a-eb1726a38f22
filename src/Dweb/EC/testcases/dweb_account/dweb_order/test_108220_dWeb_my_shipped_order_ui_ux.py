import allure
import pytest

from playwright.sync_api import Page

from src.Dweb.EC.dweb_ele.dweb_account.dweb_order import dweb_order_list_ele
from src.Dweb.EC.dweb_pages.dweb_page_account.dweb_page_order.dweb_page_order_list import DWebOrderListPage
from src.config.weee.log_help import log


@allure.story("【108220】 订单列表已发货 tab订单流程验证")
class TestDWebMyShippedOrderUIUX:
    pytestmark = [pytest.mark.dweb_porder, pytest.mark.dweb_todo, pytest.mark.transaction, pytest.mark.suqin]

    @allure.title("【108220】 订单列表已发货 tab订单流程验证")
    def test_108220_dWeb_my_shipped_order_ui_ux(self, page: dict, pc_autotest_header, login_trace):
        """
        【108220】 订单列表已发货 tab订单流程验证
        测试步骤：
        1、进入订单列表页面，点击切换到已发货tab下
        2、如果存在订单，订单上会存在各种按钮
        3、点击 Return/ Refund 按钮，进入售后退款页面，再返回已送达页面
        4、点击Refund details 按钮，进入售后退款详情页面，再返回已送达页面
        5、点击Track items 按钮，弹出物流pop，点击conform 按钮，回到已送达页面
        6、点击reorder 按钮 ，右侧拉出再来一单选择商品页面，关闭返回已送达页面
        """
        p: Page = page.get("page")
        c = page.get("context")

        # 1. 进入订单列表页面
        order_page = DWebOrderListPage(p, pc_autotest_header, browser_context=c, page_url="/account/my_orders")
        p.wait_for_timeout(3000)
        log.info("成功进入订单列表页面")
        # 切换到已发货tab
        shipped_tab = p.locator(dweb_order_list_ele.order_shipped_tab_ele)
        assert shipped_tab.is_visible(), "未找到已发货tab"
        # 切换到已发货tab
        shipped_tab.click()
        p.wait_for_timeout(2000)
        log.info("成功切换到已发货tab")
        # 2. 检查存在已发货订单
        order_list_items = p.locator(dweb_order_list_ele.order_list_card_ele).all()
        # 2. 检查是否存在订单
        if len(order_list_items) == 0:
            log.warning("已发货tab下没有订单，无法继续测试")
            pytest.skip("已发货tab下没有订单，跳过测试")
        
        log.info(f"已发货tab下有{len(order_list_items)}个订单")
        for index, item in enumerate(order_list_items):
            # 验证前10 个订单内容信息
            assert item.locator(dweb_order_list_ele.order_list_card_status_ele).is_visible(), "订单内容信息不存在"
            assert item.locator(dweb_order_list_ele.order_list_card_detail_ele).is_visible(), "订单内容信息不存在"
            # seller 订单
            if item.locator(dweb_order_list_ele.order_list_card_title_ele).is_visible():
                assert item.locator(dweb_order_list_ele.order_list_card_title_icon_ele), "seller订单icon信息不存在"

            assert item.locator(dweb_order_list_ele.order_list_card_items_ele).is_visible(), "Delivery date\Order number\Items\Total 信息不存在"
            # 验证订单卡片上 Delivery date\Order number\Items\Total 信息
            for item2 in item.locator(dweb_order_list_ele.order_list_card_items_ele).all():
                for item3 in item2.locator(dweb_order_list_ele.order_list_card_item_ele).all():
                    assert item3.locator(dweb_order_list_ele.order_list_card_info_ele).is_visible()
                    assert item3.locator(dweb_order_list_ele.order_list_card_label_ele).is_visible()
            # 断言订单状态是Delivered
            assert item.locator(dweb_order_list_ele.order_list_card_statu_ele).text_content() in ("Delivered"), "订单状态不是Delivered"
            assert item.locator(dweb_order_list_ele.order_list_card_product_ele).is_visible()
            # 查找订单列表中的按钮
            button_lists = item.locator(dweb_order_list_ele.order_list_cart_btn_ele).all()
            if len(button_lists) > 0:
                for index, item2 in enumerate(button_lists):
                    if item2.locator(u"//button").text_content() == "Return/Refund":
                        log.info("找到 Return/Refund 按钮")
                        # 点击 Return/Refund 按钮
                        item2.locator(u"//button").click()
                    elif item2.locator(u"//button").text_content() == "Review":
                        log.info("找到 Review 按钮")
                        
                    elif item2.locator(u"//button").text_content() == "Track items":
                        log.info("找到 Track items 按钮")
                    elif item2.locator(u"//button").text_content() == "Reorder":
                        log.info("找到 Reorder 按钮")
            else:
                log.info("当前订单没有按钮")

        # 点击订单detail 按钮进入pdp
        order_list_items[0].locator(dweb_order_list_ele.order_list_card_product_ele).click()
        p.wait_for_timeout(2000)
        # 断言页面进入订单详情页面
        assert "order/detail/" in p.url , "没有进入订单详情页面"


        # 选择第一个订单进行操作
        first_order = order_list_items[0]
        
        # 3. 点击 Return/Refund 按钮
        return_refund_btn = first_order.get_by_test_id("btn-return-refund")
        if return_refund_btn.is_visible():
            log.info("找到 Return/Refund 按钮")
            return_refund_btn.click()
            p.wait_for_timeout(3000)
            
            # 验证进入售后退款页面
            refund_page = p.get_by_test_id("refund-page")
            assert refund_page.is_visible(), "未成功进入售后退款页面"
            log.info("成功进入售后退款页面")
            
            # 返回已送达页面
            back_btn = p.get_by_test_id("btn-back")
            assert back_btn.is_visible(), "未找到返回按钮"
            back_btn.click()
            p.wait_for_timeout(2000)
            log.info("返回已送达页面")
            
            # 验证已返回订单列表页面
            assert p.get_by_test_id("order-list-page").is_visible(), "未成功返回订单列表页面"
        else:
            log.info("当前订单没有 Return/Refund 按钮")
        
        # 4. 点击 Refund details 按钮
        refund_details_btn = first_order.get_by_test_id("btn-refund-details")
        if refund_details_btn.is_visible():
            log.info("找到 Refund details 按钮")
            refund_details_btn.click()
            p.wait_for_timeout(3000)
            
            # 验证进入售后退款详情页面
            refund_details_page = p.get_by_test_id("refund-details-page")
            assert refund_details_page.is_visible(), "未成功进入售后退款详情页面"
            log.info("成功进入售后退款详情页面")
            
            # 返回已送达页面
            back_btn = p.get_by_test_id("btn-back")
            assert back_btn.is_visible(), "未找到返回按钮"
            back_btn.click()
            p.wait_for_timeout(2000)
            log.info("返回已送达页面")
            
            # 验证已返回订单列表页面
            assert p.get_by_test_id("order-list-page").is_visible(), "未成功返回订单列表页面"
        else:
            log.info("当前订单没有 Refund details 按钮")
        
        # 5. 点击 Track items 按钮
        track_items_btn = first_order.get_by_test_id("btn-track-items")
        if track_items_btn.is_visible():
            log.info("找到 Track items 按钮")
            track_items_btn.click()
            p.wait_for_timeout(3000)
            
            # 验证弹出物流pop
            tracking_popup = p.get_by_test_id("tracking-popup")
            assert tracking_popup.is_visible(), "未成功弹出物流pop"
            log.info("成功弹出物流pop")
            
            # 点击 Confirm 按钮
            confirm_btn = p.get_by_test_id("btn-confirm")
            assert confirm_btn.is_visible(), "未找到 Confirm 按钮"
            confirm_btn.click()
            p.wait_for_timeout(2000)
            log.info("点击 Confirm 按钮")
            
            # 验证已关闭物流pop
            assert not tracking_popup.is_visible(), "物流pop未成功关闭"
            log.info("物流pop已关闭")
        else:
            log.info("当前订单没有 Track items 按钮")
        
        # 6. 点击 Reorder 按钮
        reorder_btn = first_order.get_by_test_id("btn-reorder")
        if reorder_btn.is_visible():
            log.info("找到 Reorder 按钮")
            reorder_btn.click()
            p.wait_for_timeout(3000)
            
            # 验证右侧拉出再来一单选择商品页面
            buy_again_drawer = p.get_by_test_id("buy-again-product-drawer")
            assert buy_again_drawer.is_visible(), "未成功拉出再来一单选择商品页面"
            log.info("成功拉出再来一单选择商品页面")
            
            # 关闭再来一单选择商品页面
            close_btn = p.get_by_test_id("btn-close-drawer")
            assert close_btn.is_visible(), "未找到关闭按钮"
            close_btn.click()
            p.wait_for_timeout(2000)
            log.info("关闭再来一单选择商品页面")
            
            # 验证已关闭再来一单选择商品页面
            assert not buy_again_drawer.is_visible(), "再来一单选择商品页面未成功关闭"
            log.info("再来一单选择商品页面已关闭")
            
            # 验证已返回订单列表页面
            assert p.get_by_test_id("order-list-page").is_visible(), "未成功返回订单列表页面"
        else:
            log.info("当前订单没有 Reorder 按钮")
        
        log.info("订单列表已发货 tab订单流程验证完成")


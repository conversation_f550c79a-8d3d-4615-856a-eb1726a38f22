import allure
import pytest

from playwright.sync_api import Page

from src.Dweb.EC.dweb_ele.dweb_account.dweb_order import dweb_order_list_ele, dweb_buy_again_ele
from src.Dweb.EC.dweb_pages.dweb_page_account.dweb_page_order.dweb_page_order_list import DWebOrderListPage
from src.config.weee.log_help import log


@allure.story("【108221】 订单列表待付款 tab-订单流程验证")
class TestDWebMyPendingOrderUIUX:
    pytestmark = [pytest.mark.dweb_porder, pytest.mark.dweb_regression, pytest.mark.transaction, pytest.mark.suqin]

    @allure.title("【108221】 订单列表待付款 tab-订单流程验证")
    def test_108221_dWeb_my_pending_order_ui_ux(self, page: dict, pc_autotest_header, login_trace):
        """
        【108221】 订单列表待付款 tab-订单流程验证
        测试步骤：
        1、切换到待付款tab 下，检查是否有待付款订单
        2、如果没有，点击start shopping，进入首页
        3、如果有查看待付款订单，验证订单信息正确，订单状态正确
        4、点击订单列表下各按钮
        5、点击订单进入订单详情，验证订单详情信息正确，各交互正确
        3、4、5、 依赖测试数据
        """
        p: Page = page.get("page")
        c = page.get("context")

        # 1. 进入订单列表页面
        order_page = DWebOrderListPage(p, pc_autotest_header, browser_context=c, page_url="/account/my_orders")
        p.wait_for_timeout(3000)
        log.info("成功进入订单列表页面")
        # 切换到待付款tab
        pending_tab = p.get_by_test_id(dweb_order_list_ele.order_pending_tab_ele)
        assert pending_tab.is_visible(), "未找到待支付tab"
        # 切换到待支付 tab
        pending_tab.click()
        p.wait_for_timeout(2000)
        # 验证已切换到待支付 tab
        assert "filter_status=1" in p.url, "未切换到待支付tab"
        # 2. 检查是否存在订单
        empty_state = p.get_by_test_id(dweb_order_list_ele.empty_image_ele)
        if empty_state.is_visible():
            # 待支付tab下没有订单
            # 2. 如果没有订单，点击start shopping按钮
            start_shopping_btn = p.get_by_test_id(dweb_order_list_ele.start_shopping_btn_ele)
            assert start_shopping_btn.is_visible(), "未找到start shopping按钮"
            assert p.get_by_test_id(dweb_order_list_ele.empty_title_ele)
            start_shopping_btn.click()
            p.wait_for_timeout(3000)
            # 判断进入首页
            assert p.get_by_test_id("btn-main-banner-img-0").is_visible(), "未成功跳转到首页"
        else:
            # 获取订单列表
            order_R_items = p.get_by_test_id(dweb_order_list_ele.order_list_R_card_ele).all()
            order_S_items = p.get_by_test_id(dweb_order_list_ele.order_list_S_card_ele).all()
            order_P_items = p.get_by_test_id(dweb_order_list_ele.order_list_P_card_ele).all()
            order_G_items = p.get_by_test_id(dweb_order_list_ele.order_list_G_card_ele).all()

            if len(order_R_items) == 0 and len(order_S_items) == 0:
                log.warning("已发货tab下没有normal+seller 订单，无法继续测试")
                pytest.skip("已发货tab下没有normal+seller 订单，跳过测试")
            elif len(order_R_items) > 0:
                for index, item_R in enumerate(order_R_items):
                    # 使用公共断言方法验证seller订单卡片信息
                    assert order_page.assert_order_tab_info(item_R, "4")
                    assert order_page.assert_order_card_info(item_R, "R"), f"第{index + 1}个生鲜订单卡片信息验证失败"
            elif len(order_S_items) > 0:
                for index, item_S in enumerate(order_S_items):
                    # 使用公共断言方法验证seller订单卡片信息
                    assert order_page.assert_order_card_info(item_S, "S"), f"第{index + 1}个seller订单卡片信息验证失败"
            elif len(order_P_items) > 0:
                for index, item_P in enumerate(order_P_items):
                    # 验证前10 个订单seller内容信息
                    # 使用公共断言方法验证seller订单卡片信息
                    assert order_page.assert_order_card_info(item_P, "P"), f"第{index + 1}个积分订单卡片信息验证失败"
            elif len(order_G_items) > 0:
                for index, item_G in enumerate(order_G_items):
                    # 验证前10 个订单seller内容信息
                    # 使用公共断言方法验证seller订单卡片信息
                    assert order_page.assert_order_card_info(item_G,
                                                             "G"), f"第{index + 1}个里礼品卡订单卡片信息验证失败"
            # 点击卡片进入pdp
            order_R_items[0].click()
            p.wait_for_timeout(2000)
            # 断言页面进入订单详情页面
            assert "/order/dealpay/v2" in p.url, "没有进入订单详情页面"
            p.go_back()


        # # 切换到待付款tab
        # pending_tab.click()
        # p.wait_for_timeout(2000)
        # # 验证已切换到待付款tab
        # print(p.url)
        # assert "filter_status=1" in p.url, "未切换到待付款tab"
        # if p.locator(dweb_order_list_ele.order_list_card_ele).is_visible():
        #     log.info("待付款tab下有订单")
        # else:
        #     log.info("待付款tab下没有订单")
        #     pytest.skip("待付款tab下没有订单，跳过测试")
        # # 2. 检查存在待付款订单
        # order_list_items = p.locator(dweb_order_list_ele.order_list_card_ele).all()
        # # 2. 检查是否存在订单
        # if len(order_list_items) == 0:
        #     log.warning("待付款tab下没有订单，无法继续测试")
        #     pytest.skip("待付款tab下没有订单，跳过测试")
        #
        # log.info(f"待付款tab下有{len(order_list_items)}个订单")
        # for index, item in enumerate(order_list_items):
        #     # 验证前10 个订单内容信息
        #     assert item.locator(dweb_order_list_ele.order_list_card_status_ele).is_visible(), "订单内容信息不存在"
        #     assert item.locator(dweb_order_list_ele.order_list_card_detail_ele).is_visible(), "订单内容信息不存在"
        #     # seller 订单
        #     if item.locator(dweb_order_list_ele.order_list_card_title_ele).is_visible():
        #         assert item.locator(dweb_order_list_ele.order_list_card_title_icon_ele), "seller订单icon信息不存在"
        #
        #     assert item.locator(
        #         dweb_order_list_ele.order_list_card_items_ele).is_visible(), "Delivery date\Order number\Items\Total 信息不存在"
        #     # 验证订单卡片上 Delivery date\Order number\Items\Total 信息
        #     for item2 in item.locator(dweb_order_list_ele.order_list_card_items_ele).all():
        #         for item3 in item2.locator(dweb_order_list_ele.order_list_card_item_ele).all():
        #             assert item3.locator(dweb_order_list_ele.order_list_card_info_ele).is_visible()
        #             assert item3.locator(dweb_order_list_ele.order_list_card_label_ele).is_visible()
        #     # 断言订单状态是Ready to Ship
        #     assert item.locator(dweb_order_list_ele.order_list_card_statu_ele).text_content() in (
        #         "Ready to Ship"), "订单状态不是Ready to Ship"
        #     assert item.locator(dweb_order_list_ele.order_list_card_product_ele).is_visible()
        #     # 查找订单列表中的按钮
        #     button_lists = item.locator(dweb_order_list_ele.order_list_cart_btn_ele)
        #     # 获取按钮文本列表
        #     button_texts = button_lists.locator("button").all_text_contents()
        #     # 断言
        #     assert "Cancel order" in button_texts, "'Cancel order' button not found"
        #     assert "Pay now" in button_texts, "'Pay now' button not found"
        #     # 点击Pay now
        #     for index, item2 in enumerate(button_lists.all()):
        #         if item2.locator(u"//button").text_content() == "Pay now":
        #             # 点击 Pay now 按钮
        #             item2.locator(u"//button").click()
        #             p.wait_for_timeout(2000)
        #             assert "/order/dealpay/" in p.url, "未成功跳转dealpay页面"
        #             p.go_back()
        #         elif item2.locator(u"//button").text_content() == "Cancel order":
        #             # 点击 Cancel order 按钮
        #             item2.locator(u"//button").click()
        #             p.wait_for_timeout(2000)
        #             assert p.locator(dweb_order_list_ele.pending_cancel_order_pop).is_visible(), "未成功弹出取消订单pop"
        #             # 关闭取消订单pop
        #             p.locator(dweb_order_list_ele.pending_cancel_order_pop_btn[1]).click()
        #

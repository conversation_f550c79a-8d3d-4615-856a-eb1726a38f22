{"name": "【108219】 订单列表已取消tab-订单流程验证", "status": "broken", "statusDetails": {"message": "playwright._impl._errors.Error: Unexpected token \"\" while parsing selector \"\"", "trace": "self = <src.Dweb.EC.testcases.dweb_account.dweb_order.test_108219_dWeb_my_canceled_order_ui_ux.TestDWebMyCanceledOrderUIUX object at 0x00000192FE54BE10>\npage = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppDat...1091\\chrome-win\\chrome.exe> version=120.0.6099.28>>, 'page': <Page url='https://www.sayweee.com/en/account/my_orders'>}\npc_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...x5RjbahE4GiFtlHJQSmWnCJxRkeWMXpH3sNFUbC6Sl1zeypRliMB6xr2_SKhiEiISpd77_cMg6dCIcqC_3cW4qArmHjocXe7IkOMfj4AHbMYGSh8', ...}\nlogin_trace = None\n\n    @allure.title(\"【108219】 订单列表已取消tab-订单流程验证\")\n    def test_108219_dWeb_my_canceled_order_ui_ux(self, page: dict, pc_autotest_header, login_trace):\n        \"\"\"\n        【108219】 订单列表已取消tab-订单流程验证\n        测试步骤：\n        1、进入订单列表页面，点击切换到已发货tab下\n        2、如果存在订单，订单上会存在各种按钮\n        3、点击 Return/ Refund 按钮，进入售后退款页面，再返回已送达页面\n        6、点击reorder 按钮 ，右侧拉出再来一单选择商品页面，关闭返回已送达页面\n        \"\"\"\n        p: Page = page.get(\"page\")\n        c = page.get(\"context\")\n    \n        # 1. 进入订单列表页面\n        order_page = DWebOrderListPage(p, pc_autotest_header, browser_context=c, page_url=\"/account/my_orders\")\n        p.wait_for_timeout(3000)\n        log.info(\"成功进入订单列表页面\")\n    \n        # 切换到已取消tab\n        shipped_tab = p.locator(dweb_order_list_ele.order_cancelled_tab_ele)\n>       assert shipped_tab.is_visible(), \"未找到已取消tab\"\n\ntest_108219_dWeb_my_canceled_order_ui_ux.py:36: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py:17546: in is_visible\n    self._sync(self._impl_obj.is_visible(timeout=timeout))\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_locator.py:499: in is_visible\n    return await self._frame.is_visible(\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_frame.py:348: in is_visible\n    return await self._channel.send(\"isVisible\", locals_to_params(locals()))\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:62: in send\n    return await self._connection.wrap_api_call(\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:492: in wrap_api_call\n    return await cb()\n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n\nself = <playwright._impl._connection.Channel object at 0x00000192FE758310>\nmethod = 'isVisible', params = {'selector': '', 'strict': True}\nreturn_as_dict = False\n\n    async def inner_send(\n        self, method: str, params: Optional[Dict], return_as_dict: bool\n    ) -> Any:\n        if params is None:\n            params = {}\n        callback = self._connection._send_message_to_server(\n            self._object, method, params\n        )\n        if self._connection._error:\n            error = self._connection._error\n            self._connection._error = None\n            raise error\n        done, _ = await asyncio.wait(\n            {\n                self._connection._transport.on_error_future,\n                callback.future,\n            },\n            return_when=asyncio.FIRST_COMPLETED,\n        )\n        if not callback.future.done():\n            callback.future.cancel()\n>       result = next(iter(done)).result()\nE       playwright._impl._errors.Error: Unexpected token \"\" while parsing selector \"\"\n\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:100: Error"}, "description": "\n        【108219】 订单列表已取消tab-订单流程验证\n        测试步骤：\n        1、进入订单列表页面，点击切换到已发货tab下\n        2、如果存在订单，订单上会存在各种按钮\n        3、点击 Return/ Refund 按钮，进入售后退款页面，再返回已送达页面\n        6、点击reorder 按钮 ，右侧拉出再来一单选择商品页面，关闭返回已送达页面\n        ", "start": *************, "stop": *************, "uuid": "e4ef6e15-c555-4c44-a3e8-af41edec93b7", "historyId": "087d402edd47ff5f4ce5085386529994", "testCaseId": "087d402edd47ff5f4ce5085386529994", "fullName": "src.Dweb.EC.testcases.dweb_account.dweb_order.test_108219_dWeb_my_canceled_order_ui_ux.TestDWebMyCanceledOrderUIUX#test_108219_dWeb_my_canceled_order_ui_ux", "labels": [{"name": "story", "value": "【108219】 订单列表已取消tab-订单流程验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "dweb_porder"}, {"name": "tag", "value": "dweb_todo"}, {"name": "tag", "value": "transaction"}, {"name": "tag", "value": "suqin"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.dweb_account.dweb_order"}, {"name": "suite", "value": "test_108219_dWeb_my_canceled_order_ui_ux"}, {"name": "subSuite", "value": "TestDWebMyCanceledOrderUIUX"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "12084-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.dweb_account.dweb_order.test_108219_dWeb_my_canceled_order_ui_ux"}]}
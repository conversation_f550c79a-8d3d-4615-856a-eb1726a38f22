{"name": "【108220】 订单列表已发货 tab订单流程验证", "status": "skipped", "statusDetails": {"message": "Skipped: 已发货tab下没有订单，跳过测试", "trace": "('D:\\\\MyWork\\\\Python\\\\EC-demo\\\\qa-ui-dmweb\\\\src\\\\Dweb\\\\EC\\\\testcases\\\\dweb_account\\\\dweb_order\\\\test_108220_dWeb_my_shipped_order_ui_ux.py', 46, 'Skipped: 已发货tab下没有订单，跳过测试')"}, "description": "\n        【108220】 订单列表已发货 tab订单流程验证\n        测试步骤：\n        1、进入订单列表页面，点击切换到已发货tab下\n        2、如果存在订单，订单上会存在各种按钮\n        3、点击 Return/ Refund 按钮，进入售后退款页面，再返回已送达页面\n        4、点击Refund details 按钮，进入售后退款详情页面，再返回已送达页面\n        5、点击Track items 按钮，弹出物流pop，点击conform 按钮，回到已送达页面\n        6、点击reorder 按钮 ，右侧拉出再来一单选择商品页面，关闭返回已送达页面\n        ", "start": *************, "stop": *************, "uuid": "7b86b258-2c7c-4dbf-8e10-26be02a42aa5", "historyId": "ecca26e2f200918b1ccfe550cd430b36", "testCaseId": "ecca26e2f200918b1ccfe550cd430b36", "fullName": "src.Dweb.EC.testcases.dweb_account.dweb_order.test_108220_dWeb_my_shipped_order_ui_ux.TestDWebMyShippedOrderUIUX#test_108220_dWeb_my_shipped_order_ui_ux", "labels": [{"name": "story", "value": "【108220】 订单列表已发货 tab订单流程验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "dweb_porder"}, {"name": "tag", "value": "dweb_todo"}, {"name": "tag", "value": "transaction"}, {"name": "tag", "value": "suqin"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.dweb_account.dweb_order"}, {"name": "suite", "value": "test_108220_dWeb_my_shipped_order_ui_ux"}, {"name": "subSuite", "value": "TestDWebMyShippedOrderUIUX"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "29556-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.dweb_account.dweb_order.test_108220_dWeb_my_shipped_order_ui_ux"}]}
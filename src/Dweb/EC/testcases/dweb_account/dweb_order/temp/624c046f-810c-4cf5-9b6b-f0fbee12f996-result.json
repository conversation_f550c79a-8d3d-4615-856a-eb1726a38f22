{"name": "【108222】 订单列表已发货 tab订单流程验证", "status": "passed", "description": "\n        【108222】 订单列表待发货 tab-订单流程验证\n        测试步骤：\n        1、切换到待发货tab 下，检查是否有待发货订单\n        2、如果没有，点击start shopping，进入首页\n        3、如果有查看待发货订单，验证订单信息正确，订单状态正确\n        4、点击订单列表下各按钮\n        5、点击订单进入订单详情，验证订单详情信息正确，各交互正确\n        3、4、5、 依赖测试数据\n        ", "start": *************, "stop": *************, "uuid": "21688b02-56bd-492e-95e6-a23df738217a", "testCaseId": "35ef8cc5a0923fa13d034bd29e07b0e8", "fullName": "src.Dweb.EC.testcases.dweb_account.dweb_order.test_108222_dWeb_my_unshipped_order_ui_ux.TestDWebMyShippedOrderUIUX#test_108222_dWeb_my_unshipped_order_ui_ux"}
{"name": "【108616】 订单列表已取消tab-再来一单流程验证", "status": "passed", "description": "\n        【108616】 订单列表已取消tab-再来一单流程验证\n        测试步骤：\n        1、进入订单列表页面，点击切换到已取消tab下\n        2、找到订单列表下再来一单按钮，右侧会撤拉出商品选择页面\n        3、商品选择页面，默认勾选全选按钮，勾选掉某个商品，全选去掉，加入购物车按钮置灰，点击全选商品全部选中，这里如果选中几个商品，加入购物车按钮上就会显示几个\n        4、点击加入购物车按钮，自动回到订单列表页面\n        ", "start": *************, "stop": *************, "uuid": "46c1f978-f548-4af5-bc8e-8cd8f10a3040", "historyId": "c07889fa49f3f486089ccb09940f7445", "testCaseId": "c07889fa49f3f486089ccb09940f7445", "fullName": "src.Dweb.EC.testcases.dweb_account.dweb_order.test_108616_dWeb_my_canceled_order_buy_again_ui_ux.TestDWebMyCanceledOrderBuyAgainUIUX#test_108616_dWeb_my_canceled_order_buy_again_ui_ux", "labels": [{"name": "story", "value": "【108616】 订单列表已取消tab-再来一单流程验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "dweb_porder"}, {"name": "tag", "value": "dweb_todo"}, {"name": "tag", "value": "transaction"}, {"name": "tag", "value": "suqin"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.dweb_account.dweb_order"}, {"name": "suite", "value": "test_108616_dWeb_my_canceled_order_buy_again_ui_ux"}, {"name": "subSuite", "value": "TestDWebMyCanceledOrderBuyAgainUIUX"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "29788-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.dweb_account.dweb_order.test_108616_dWeb_my_canceled_order_buy_again_ui_ux"}]}
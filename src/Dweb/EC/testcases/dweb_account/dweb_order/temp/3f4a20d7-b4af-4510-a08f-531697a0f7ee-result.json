{"name": "【108219】 订单列表已取消tab-订单流程验证", "status": "broken", "statusDetails": {"message": "playwright._impl._errors.TimeoutError: Timeout 50000ms exceeded.", "trace": "self = <src.Dweb.EC.testcases.dweb_account.dweb_order.test_108219_dWeb_my_canceled_order_ui_ux.TestDWebMyCanceledOrderUIUX object at 0x000002B5130C4210>\npage = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppDat...> version=120.0.6099.28>>, 'page': <Page url='https://www.sayweee.com/en/account/my_orders?page_no=1&filter_status=4'>}\npc_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...bcJdr9pXoBLXkRH3XF9tSWQdOtF6yd0e8clHYlsNqHhPhjseuxYfyBfUh8CCJrGscyYhWdZiNe_Eo9EQeaZxXICaUDhKpNo8i7zC24frZKMWaWfI', ...}\nlogin_trace = None\n\n    @allure.title(\"【108219】 订单列表已取消tab-订单流程验证\")\n    def test_108219_dWeb_my_canceled_order_ui_ux(self, page: dict, pc_autotest_header, login_trace):\n        \"\"\"\n        【108219】 订单列表已取消tab-订单流程验证\n        测试步骤：\n        1、进入订单列表页面，点击切换到已发货tab下\n        2、如果存在订单，订单上会存在各种按钮\n        3、点击 Return/ Refund 按钮，进入售后退款页面，再返回已送达页面\n        6、点击reorder 按钮 ，右侧拉出再来一单选择商品页面，关闭返回已送达页面\n        \"\"\"\n        p: Page = page.get(\"page\")\n        c = page.get(\"context\")\n    \n        # 1. 进入订单列表页面\n        order_page = DWebOrderListPage(p, pc_autotest_header, browser_context=c, page_url=\"/account/my_orders\")\n        p.wait_for_timeout(3000)\n        log.info(\"成功进入订单列表页面\")\n    \n        # 切换到已取消tab\n        shipped_tab = p.locator(dweb_order_list_ele.order_cancelled_tab_ele)\n        assert shipped_tab.is_visible(), \"未找到已取消tab\"\n        # 切换到已取消tab\n        shipped_tab.click()\n        p.wait_for_timeout(2000)\n        log.info(\"成功切换到已取消tab\")\n        # 验证已切换到已取消tab\n        # assert shipped_tab.get_attribute(\"aria-selected\") == \"true\", \"未成功切换到已取消tab\"\n    \n        # 2. 检查存在已取消订单\n        order_list_items = p.locator(dweb_order_list_ele.order_list_card_ele).all()\n        assert len(order_list_items) > 0,\"已取消tab下没有订单，无法继续测试\"\n        for index, item in enumerate(order_list_items):\n            # 验证前10 个订单内容信息\n            assert item.locator(dweb_order_list_ele.order_list_card_status_ele).is_visible(), \"订单状态不是Cancelled\"\n            assert item.locator(dweb_order_list_ele.order_list_card_detail_ele).is_visible(), \"订单详情不存在\"\n            # seller 订单\n            if item.locator(dweb_order_list_ele.order_list_card_title_ele).is_visible():\n                assert item.locator(dweb_order_list_ele.order_list_card_title_ele)\n    \n            assert item.locator(dweb_order_list_ele.order_list_card_items_ele).is_visible(), \"订单状态不是Cancelled\"\n            # 验证订单卡片上 Delivery date\\Order number\\Items\\Total 信息\n            for item2 in item.locator(dweb_order_list_ele.order_list_card_items_ele).all():\n>               assert item2.locator(dweb_order_list_ele.order_list_card_item_ele).is_enabled()\n\ntest_108219_dWeb_my_canceled_order_ui_ux.py:58: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py:17482: in is_enabled\n    self._sync(self._impl_obj.is_enabled(timeout=timeout))\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_locator.py:483: in is_enabled\n    return await self._frame.is_editable(\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_frame.py:333: in is_editable\n    return await self._channel.send(\"isEditable\", locals_to_params(locals()))\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:62: in send\n    return await self._connection.wrap_api_call(\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:492: in wrap_api_call\n    return await cb()\n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n\nself = <playwright._impl._connection.Channel object at 0x000002B50FF49650>\nmethod = 'isEditable'\nparams = {'selector': \"//div[contains(@class,'order-card_orderCard')] >> nth=0 >> //div[contains(@class,'order-card_items')] >> nth=0 >> //div[contains(@class,'order-card_item__')]\", 'strict': True}\nreturn_as_dict = False\n\n    async def inner_send(\n        self, method: str, params: Optional[Dict], return_as_dict: bool\n    ) -> Any:\n        if params is None:\n            params = {}\n        callback = self._connection._send_message_to_server(\n            self._object, method, params\n        )\n        if self._connection._error:\n            error = self._connection._error\n            self._connection._error = None\n            raise error\n        done, _ = await asyncio.wait(\n            {\n                self._connection._transport.on_error_future,\n                callback.future,\n            },\n            return_when=asyncio.FIRST_COMPLETED,\n        )\n        if not callback.future.done():\n            callback.future.cancel()\n>       result = next(iter(done)).result()\nE       playwright._impl._errors.TimeoutError: Timeout 50000ms exceeded.\n\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:100: TimeoutError"}, "description": "\n        【108219】 订单列表已取消tab-订单流程验证\n        测试步骤：\n        1、进入订单列表页面，点击切换到已发货tab下\n        2、如果存在订单，订单上会存在各种按钮\n        3、点击 Return/ Refund 按钮，进入售后退款页面，再返回已送达页面\n        6、点击reorder 按钮 ，右侧拉出再来一单选择商品页面，关闭返回已送达页面\n        ", "start": *************, "stop": *************, "uuid": "7796cd1b-def3-496c-9527-b285da006d81", "historyId": "087d402edd47ff5f4ce5085386529994", "testCaseId": "087d402edd47ff5f4ce5085386529994", "fullName": "src.Dweb.EC.testcases.dweb_account.dweb_order.test_108219_dWeb_my_canceled_order_ui_ux.TestDWebMyCanceledOrderUIUX#test_108219_dWeb_my_canceled_order_ui_ux", "labels": [{"name": "story", "value": "【108219】 订单列表已取消tab-订单流程验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "dweb_porder"}, {"name": "tag", "value": "dweb_todo"}, {"name": "tag", "value": "transaction"}, {"name": "tag", "value": "suqin"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.dweb_account.dweb_order"}, {"name": "suite", "value": "test_108219_dWeb_my_canceled_order_ui_ux"}, {"name": "subSuite", "value": "TestDWebMyCanceledOrderUIUX"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "10936-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.dweb_account.dweb_order.test_108219_dWeb_my_canceled_order_ui_ux"}]}
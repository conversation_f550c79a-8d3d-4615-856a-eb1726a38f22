{"name": "【108219】 订单列表已取消tab-订单流程验证", "status": "failed", "statusDetails": {"message": "AssertionError: assert False\n +  where False = <bound method Locator.is_visible of <Locator frame=<Frame name= url='https://www.sayweee.com/en/account/my_orders?page_no=1&filter_status=4'> selector=\"//div[contains(@class,'order-card_orderCard')] >> nth=0 >> //div[contains(@class,'order-card_items')] >> nth=0 >> //div[contains(@class,'order-card_item')]\">>()\n +    where <bound method Locator.is_visible of <Locator frame=<Frame name= url='https://www.sayweee.com/en/account/my_orders?page_no=1&filter_status=4'> selector=\"//div[contains(@class,'order-card_orderCard')] >> nth=0 >> //div[contains(@class,'order-card_items')] >> nth=0 >> //div[contains(@class,'order-card_item')]\">> = <Locator frame=<Frame name= url='https://www.sayweee.com/en/account/my_orders?page_no=1&filter_status=4'> selector=\"//div[contains(@class,'order-card_orderCard')] >> nth=0 >> //div[contains(@class,'order-card_items')] >> nth=0 >> //div[contains(@class,'order-card_item')]\">.is_visible\n +      where <Locator frame=<Frame name= url='https://www.sayweee.com/en/account/my_orders?page_no=1&filter_status=4'> selector=\"//div[contains(@class,'order-card_orderCard')] >> nth=0 >> //div[contains(@class,'order-card_items')] >> nth=0 >> //div[contains(@class,'order-card_item')]\"> = <bound method Locator.locator of <Locator frame=<Frame name= url='https://www.sayweee.com/en/account/my_orders?page_no=1&filter_status=4'> selector=\"//div[contains(@class,'order-card_orderCard')] >> nth=0 >> //div[contains(@class,'order-card_items')] >> nth=0\">>(\"//div[contains(@class,'order-card_item')]\")\n +        where <bound method Locator.locator of <Locator frame=<Frame name= url='https://www.sayweee.com/en/account/my_orders?page_no=1&filter_status=4'> selector=\"//div[contains(@class,'order-card_orderCard')] >> nth=0 >> //div[contains(@class,'order-card_items')] >> nth=0\">> = <Locator frame=<Frame name= url='https://www.sayweee.com/en/account/my_orders?page_no=1&filter_status=4'> selector=\"//div[contains(@class,'order-card_orderCard')] >> nth=0 >> //div[contains(@class,'order-card_items')] >> nth=0\">.locator\n +        and   \"//div[contains(@class,'order-card_item')]\" = dweb_order_list_ele.order_list_card_item_ele", "trace": "self = <src.Dweb.EC.testcases.dweb_account.dweb_order.test_108219_dWeb_my_canceled_order_ui_ux.TestDWebMyCanceledOrderUIUX object at 0x000001DA075B3E10>\npage = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppDat...> version=120.0.6099.28>>, 'page': <Page url='https://www.sayweee.com/en/account/my_orders?page_no=1&filter_status=4'>}\npc_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...somBWIVdIvMAkdG5rDWnRoU611dyMpSzIACqtO0auEprJhpBG874YH3aGdr0FRvt8gUYmkpkaSgnbddUrQ4ftmp9y6cOJvnFgHCBSsliyHYZ6UsU', ...}\nlogin_trace = None\n\n    @allure.title(\"【108219】 订单列表已取消tab-订单流程验证\")\n    def test_108219_dWeb_my_canceled_order_ui_ux(self, page: dict, pc_autotest_header, login_trace):\n        \"\"\"\n        【108219】 订单列表已取消tab-订单流程验证\n        测试步骤：\n        1、进入订单列表页面，点击切换到已发货tab下\n        2、如果存在订单，订单上会存在各种按钮\n        3、点击 Return/ Refund 按钮，进入售后退款页面，再返回已送达页面\n        6、点击reorder 按钮 ，右侧拉出再来一单选择商品页面，关闭返回已送达页面\n        \"\"\"\n        p: Page = page.get(\"page\")\n        c = page.get(\"context\")\n    \n        # 1. 进入订单列表页面\n        order_page = DWebOrderListPage(p, pc_autotest_header, browser_context=c, page_url=\"/account/my_orders\")\n        p.wait_for_timeout(3000)\n        log.info(\"成功进入订单列表页面\")\n    \n        # 切换到已取消tab\n        shipped_tab = p.locator(dweb_order_list_ele.order_cancelled_tab_ele)\n        assert shipped_tab.is_visible(), \"未找到已取消tab\"\n        # 切换到已取消tab\n        shipped_tab.click()\n        p.wait_for_timeout(2000)\n        log.info(\"成功切换到已取消tab\")\n        # 验证已切换到已取消tab\n        # assert shipped_tab.get_attribute(\"aria-selected\") == \"true\", \"未成功切换到已取消tab\"\n    \n        # 2. 检查存在已取消订单\n        order_list_items = p.locator(dweb_order_list_ele.order_list_card_ele).all()\n        assert len(order_list_items) > 0,\"已取消tab下没有订单，无法继续测试\"\n        for index, item in enumerate(order_list_items):\n            # 验证前10 个订单内容信息\n            assert item.locator(dweb_order_list_ele.order_list_card_status_ele).is_visible(), \"订单状态不是Cancelled\"\n            assert item.locator(dweb_order_list_ele.order_list_card_detail_ele).is_visible(), \"订单详情不存在\"\n            # seller 订单\n            if item.locator(dweb_order_list_ele.order_list_card_title_ele).is_visible():\n                assert item.locator(dweb_order_list_ele.order_list_card_title_ele)\n    \n            assert item.locator(dweb_order_list_ele.order_list_card_items_ele).is_visible(), \"订单状态不是Cancelled\"\n            # 验证订单卡片上 Delivery date\\Order number\\Items\\Total 信息\n            for item2 in item.locator(dweb_order_list_ele.order_list_card_items_ele).all():\n>               assert item2.locator(dweb_order_list_ele.order_list_card_item_ele).is_visible()\nE               assert False\nE                +  where False = <bound method Locator.is_visible of <Locator frame=<Frame name= url='https://www.sayweee.com/en/account/my_orders?page_no=1&filter_status=4'> selector=\"//div[contains(@class,'order-card_orderCard')] >> nth=0 >> //div[contains(@class,'order-card_items')] >> nth=0 >> //div[contains(@class,'order-card_item')]\">>()\nE                +    where <bound method Locator.is_visible of <Locator frame=<Frame name= url='https://www.sayweee.com/en/account/my_orders?page_no=1&filter_status=4'> selector=\"//div[contains(@class,'order-card_orderCard')] >> nth=0 >> //div[contains(@class,'order-card_items')] >> nth=0 >> //div[contains(@class,'order-card_item')]\">> = <Locator frame=<Frame name= url='https://www.sayweee.com/en/account/my_orders?page_no=1&filter_status=4'> selector=\"//div[contains(@class,'order-card_orderCard')] >> nth=0 >> //div[contains(@class,'order-card_items')] >> nth=0 >> //div[contains(@class,'order-card_item')]\">.is_visible\nE                +      where <Locator frame=<Frame name= url='https://www.sayweee.com/en/account/my_orders?page_no=1&filter_status=4'> selector=\"//div[contains(@class,'order-card_orderCard')] >> nth=0 >> //div[contains(@class,'order-card_items')] >> nth=0 >> //div[contains(@class,'order-card_item')]\"> = <bound method Locator.locator of <Locator frame=<Frame name= url='https://www.sayweee.com/en/account/my_orders?page_no=1&filter_status=4'> selector=\"//div[contains(@class,'order-card_orderCard')] >> nth=0 >> //div[contains(@class,'order-card_items')] >> nth=0\">>(\"//div[contains(@class,'order-card_item')]\")\nE                +        where <bound method Locator.locator of <Locator frame=<Frame name= url='https://www.sayweee.com/en/account/my_orders?page_no=1&filter_status=4'> selector=\"//div[contains(@class,'order-card_orderCard')] >> nth=0 >> //div[contains(@class,'order-card_items')] >> nth=0\">> = <Locator frame=<Frame name= url='https://www.sayweee.com/en/account/my_orders?page_no=1&filter_status=4'> selector=\"//div[contains(@class,'order-card_orderCard')] >> nth=0 >> //div[contains(@class,'order-card_items')] >> nth=0\">.locator\nE                +        and   \"//div[contains(@class,'order-card_item')]\" = dweb_order_list_ele.order_list_card_item_ele\n\ntest_108219_dWeb_my_canceled_order_ui_ux.py:58: AssertionError"}, "description": "\n        【108219】 订单列表已取消tab-订单流程验证\n        测试步骤：\n        1、进入订单列表页面，点击切换到已发货tab下\n        2、如果存在订单，订单上会存在各种按钮\n        3、点击 Return/ Refund 按钮，进入售后退款页面，再返回已送达页面\n        6、点击reorder 按钮 ，右侧拉出再来一单选择商品页面，关闭返回已送达页面\n        ", "start": *************, "stop": *************, "uuid": "093ae119-61d2-4e68-8c6c-e25b7e73facf", "historyId": "087d402edd47ff5f4ce5085386529994", "testCaseId": "087d402edd47ff5f4ce5085386529994", "fullName": "src.Dweb.EC.testcases.dweb_account.dweb_order.test_108219_dWeb_my_canceled_order_ui_ux.TestDWebMyCanceledOrderUIUX#test_108219_dWeb_my_canceled_order_ui_ux", "labels": [{"name": "story", "value": "【108219】 订单列表已取消tab-订单流程验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "dweb_porder"}, {"name": "tag", "value": "dweb_todo"}, {"name": "tag", "value": "transaction"}, {"name": "tag", "value": "suqin"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.dweb_account.dweb_order"}, {"name": "suite", "value": "test_108219_dWeb_my_canceled_order_ui_ux"}, {"name": "subSuite", "value": "TestDWebMyCanceledOrderUIUX"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "11364-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.dweb_account.dweb_order.test_108219_dWeb_my_canceled_order_ui_ux"}]}
{"name": "【108218】 订单列表All tab-列表流程验证", "status": "skipped", "statusDetails": {"message": "Skipped: 全部订单tab下没有normal+seller 订单，跳过测试", "trace": "('D:\\\\MyWork\\\\Python\\\\EC-demo\\\\qa-ui-dmweb\\\\src\\\\Dweb\\\\EC\\\\testcases\\\\dweb_account\\\\dweb_order\\\\test_108218_dWeb_my_all_order_ui_ux.py', 63, 'Skipped: 全部订单tab下没有normal+seller 订单，跳过测试')"}, "description": "\n        【108218】 订单列表All tab-列表流程验证\n        测试步骤：\n        1、进入订单列表页面，点击切换到全部tab下\n        2、如果存在订单，订单上会存在各种按钮\n        3、点击 Return/ Refund 按钮，进入售后退款页面，再返回已送达页面\n        6、点击reorder 按钮 ，右侧拉出再来一单选择商品页面，关闭返回已送达页面\n        ", "start": *************, "stop": *************, "uuid": "21cabcda-1b7e-468c-a7da-cb820280de13", "historyId": "43afd72eee1e7f3356f400aeed93b783", "testCaseId": "43afd72eee1e7f3356f400aeed93b783", "fullName": "src.Dweb.EC.testcases.dweb_account.dweb_order.test_108218_dWeb_my_all_order_ui_ux.TestDWebMyAllOrderUIUX#test_108218_dWeb_my_all_order_ui_ux", "labels": [{"name": "story", "value": "【108218】 订单列表All tab-列表流程验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "dweb_porder"}, {"name": "tag", "value": "dweb_regression"}, {"name": "tag", "value": "transaction"}, {"name": "tag", "value": "suqin"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.dweb_account.dweb_order"}, {"name": "suite", "value": "test_108218_dWeb_my_all_order_ui_ux"}, {"name": "subSuite", "value": "TestDWebMyAllOrderUIUX"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "27088-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.dweb_account.dweb_order.test_108218_dWeb_my_all_order_ui_ux"}]}
{"name": "【108218】 订单列表All tab-列表流程验证", "status": "broken", "statusDetails": {"message": "playwright._impl._errors.TimeoutError: Timeout 50000ms exceeded.", "trace": "self = <src.Dweb.EC.testcases.dweb_account.dweb_order.test_108218_dWeb_my_all_order_ui_ux.TestDWebMyAllOrderUIUX object at 0x000001BA86EA82D0>\npage = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppDat...version=120.0.6099.28>>, 'page': <Page url='https://www.sayweee.com/en/account/my_orders?page_no=1&filter_status=all'>}\npc_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...Oe3FKRKmxk6VqmxroRmRDxiJh9J0n4Lx-OOwtexfGqKLNcfaUMI6_8nG4_0nBD42x_rM347typ6vERCiRUgKaxn94HHj1B_s11gqfh8wWK8RN0Rg', ...}\nlogin_trace = None\n\n    @allure.title(\"【108218】 订单列表All tab-列表流程验证\")\n    def test_108218_dWeb_my_all_order_ui_ux(self, page: dict, pc_autotest_header, login_trace):\n        \"\"\"\n        【108218】 订单列表All tab-列表流程验证\n        测试步骤：\n        1、进入订单列表页面，点击切换到全部tab下\n        2、如果存在订单，订单上会存在各种按钮\n        3、点击 Return/ Refund 按钮，进入售后退款页面，再返回已送达页面\n        6、点击reorder 按钮 ，右侧拉出再来一单选择商品页面，关闭返回已送达页面\n        \"\"\"\n        p: Page = page.get(\"page\")\n        c = page.get(\"context\")\n    \n        # 1. 进入订单列表页面\n        order_page = DWebOrderListPage(p, pc_autotest_header, browser_context=c, page_url=\"/account/my_orders\")\n        p.wait_for_timeout(3000)\n        log.info(\"成功进入订单列表页面\")\n    \n        # 切换到全部订单tab\n        p.get_by_test_id(dweb_order_list_ele.order_all_tab_ele).click()\n        p.wait_for_timeout(2000)\n        # 验证已切换到全部订单tab\n        assert \"filter_status=all\" in p.url, \"未切换到全部订单tab\"\n    \n        # 验证已切换到全部订单 tab\n        # assert \"/order/list?filter_status=all\" in p.url, \"未切换到全部订单tab\"\n        # 2. 检查是否存在订单\n        empty_state = p.get_by_test_id(dweb_order_list_ele.empty_image_ele)\n        if empty_state.is_visible():\n            # 全部订单tab下没有订单\n            # 2. 如果没有订单，点击start shopping按钮\n            start_shopping_btn = p.get_by_test_id(dweb_order_list_ele.start_shopping_btn_ele)\n            assert start_shopping_btn.is_visible(), \"未找到start shopping按钮\"\n            assert p.get_by_test_id(dweb_order_list_ele.empty_title_ele)\n            start_shopping_btn.click()\n            p.wait_for_timeout(3000)\n            # 判断进入首页\n            assert p.get_by_test_id(\"btn-main-banner-img-0\").is_visible(), \"未成功跳转到首页\"\n        else:\n            # 获取全部订单tab下的normal订单\n            order_R_items = p.get_by_test_id(dweb_order_list_ele.order_list_R_card_ele).all()\n            order_S_items = p.get_by_test_id(dweb_order_list_ele.order_list_S_card_ele).all()\n            order_P_items = p.get_by_test_id(dweb_order_list_ele.order_list_P_card_ele).all()\n            order_G_items = p.get_by_test_id(dweb_order_list_ele.order_list_G_card_ele).all()\n            if len(order_R_items) == 0 and len(order_S_items) == 0:\n                log.warning(\"全部订单tab下没有normal+seller 订单，无法继续测试\")\n                pytest.skip(\"全部订单tab下没有normal+seller 订单，跳过测试\")\n            elif len(order_R_items) > 0:\n                for index, item_R in enumerate(order_R_items):\n                    # 使用公共断言方法验证seller订单卡片信息\n>                   assert order_page.assert_order_tab_info(item_R, \"all\")\n\ntest_108218_dWeb_my_all_order_ui_ux.py:66: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n..\\..\\..\\dweb_pages\\dweb_page_account\\dweb_page_order\\dweb_page_order_list.py:40: in assert_order_tab_info\n    status_text = order_item.get_by_test_id(dweb_order_list_ele.order_list_card_status_ele).text_content()\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py:18050: in text_content\n    self._sync(self._impl_obj.text_content(timeout=timeout))\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_locator.py:607: in text_content\n    return await self._frame.text_content(\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_frame.py:609: in text_content\n    return await self._channel.send(\"textContent\", locals_to_params(locals()))\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:62: in send\n    return await self._connection.wrap_api_call(\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:492: in wrap_api_call\n    return await cb()\n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n\nself = <playwright._impl._connection.Channel object at 0x000001BA87328410>\nmethod = 'textContent'\nparams = {'selector': 'internal:testid=[data-testid=\"wid-order-list-order-card-R-normal-0\"s] >> nth=0 >> internal:testid=[data-testid=\"wid-order-list-status\"s]', 'strict': True}\nreturn_as_dict = False\n\n    async def inner_send(\n        self, method: str, params: Optional[Dict], return_as_dict: bool\n    ) -> Any:\n        if params is None:\n            params = {}\n        callback = self._connection._send_message_to_server(\n            self._object, method, params\n        )\n        if self._connection._error:\n            error = self._connection._error\n            self._connection._error = None\n            raise error\n        done, _ = await asyncio.wait(\n            {\n                self._connection._transport.on_error_future,\n                callback.future,\n            },\n            return_when=asyncio.FIRST_COMPLETED,\n        )\n        if not callback.future.done():\n            callback.future.cancel()\n>       result = next(iter(done)).result()\nE       playwright._impl._errors.TimeoutError: Timeout 50000ms exceeded.\n\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:100: TimeoutError"}, "description": "\n        【108218】 订单列表All tab-列表流程验证\n        测试步骤：\n        1、进入订单列表页面，点击切换到全部tab下\n        2、如果存在订单，订单上会存在各种按钮\n        3、点击 Return/ Refund 按钮，进入售后退款页面，再返回已送达页面\n        6、点击reorder 按钮 ，右侧拉出再来一单选择商品页面，关闭返回已送达页面\n        ", "start": *************, "stop": *************, "uuid": "b4f819ed-b72c-4f2e-acaf-ca860db71bac", "historyId": "43afd72eee1e7f3356f400aeed93b783", "testCaseId": "43afd72eee1e7f3356f400aeed93b783", "fullName": "src.Dweb.EC.testcases.dweb_account.dweb_order.test_108218_dWeb_my_all_order_ui_ux.TestDWebMyAllOrderUIUX#test_108218_dWeb_my_all_order_ui_ux", "labels": [{"name": "story", "value": "【108218】 订单列表All tab-列表流程验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "dweb_porder"}, {"name": "tag", "value": "dweb_regression"}, {"name": "tag", "value": "transaction"}, {"name": "tag", "value": "suqin"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.dweb_account.dweb_order"}, {"name": "suite", "value": "test_108218_dWeb_my_all_order_ui_ux"}, {"name": "subSuite", "value": "TestDWebMyAllOrderUIUX"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "29728-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.dweb_account.dweb_order.test_108218_dWeb_my_all_order_ui_ux"}]}
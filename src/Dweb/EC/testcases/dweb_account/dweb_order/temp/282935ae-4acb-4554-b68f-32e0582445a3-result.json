{"name": "【108616】 订单列表已取消tab-再来一单流程验证", "status": "broken", "statusDetails": {"message": "TypeError: cannot unpack non-iterable Locator object", "trace": "self = <src.Dweb.EC.testcases.dweb_account.dweb_order.test_108616_dWeb_my_canceled_order_buy_again_ui_ux.TestDWebMyCanceledOrderBuyAgainUIUX object at 0x00000226E31C0390>\npage = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppDat...> version=120.0.6099.28>>, 'page': <Page url='https://www.sayweee.com/en/account/my_orders?page_no=1&filter_status=4'>}\npc_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...zpp41UoLdHg0xCCZkcWH1nwc7_HrRPeGoZfgqzeDZ3jVdIcX-HmHnc8_GTguSnMuydxzdM5OPdxrz87M2vneeykIgoH_qgV_3dX2HoMkH-WEoG6E', ...}\nlogin_trace = None\n\n    @allure.title(\"【108616】 订单列表已取消tab-再来一单流程验证\")\n    def test_108616_dWeb_my_canceled_order_buy_again_ui_ux(self, page: dict, pc_autotest_header, login_trace):\n        \"\"\"\n        【108616】 订单列表已取消tab-再来一单流程验证\n        测试步骤：\n        1、进入订单列表页面，点击切换到已取消tab下\n        2、找到订单列表下再来一单按钮，右侧会撤拉出商品选择页面\n        3、商品选择页面，默认勾选全选按钮，勾选掉某个商品，全选去掉，加入购物车按钮置灰，点击全选商品全部选中，这里如果选中几个商品，加入购物车按钮上就会显示几个\n        4、点击加入购物车按钮，自动回到订单列表页面\n        \"\"\"\n        p: Page = page.get(\"page\")\n        c = page.get(\"context\")\n    \n        # 1. 进入订单列表页面\n        order_page = DWebOrderListPage(p, pc_autotest_header, browser_context=c, page_url=\"/account/my_orders\")\n        p.wait_for_timeout(3000)\n        log.info(\"成功进入订单列表页面\")\n    \n        # 切换到已取消tab\n        canceled_tab = p.locator(dweb_order_list_ele.order_cancelled_tab_ele)\n        assert canceled_tab.is_visible(), \"未找到已取消tab\"\n        canceled_tab.click()\n        p.wait_for_timeout(2000)\n        log.info(\"成功切换到已取消tab\")\n        # 2. 检查存在已取消订单\n        order_list_items = p.locator(dweb_order_list_ele.order_list_card_ele).all()\n        assert len(order_list_items) > 0,\"已取消tab下没有订单，无法继续测试\"\n        for index, item in enumerate(order_list_items):\n            # 查找订单列表中的按钮\n            button_lists = item.locator(dweb_order_list_ele.order_list_cart_btn_ele).all()\n>           for index,item2 in button_lists:\nE           TypeError: cannot unpack non-iterable Locator object\n\ntest_108616_dWeb_my_canceled_order_buy_again_ui_ux.py:45: TypeError"}, "description": "\n        【108616】 订单列表已取消tab-再来一单流程验证\n        测试步骤：\n        1、进入订单列表页面，点击切换到已取消tab下\n        2、找到订单列表下再来一单按钮，右侧会撤拉出商品选择页面\n        3、商品选择页面，默认勾选全选按钮，勾选掉某个商品，全选去掉，加入购物车按钮置灰，点击全选商品全部选中，这里如果选中几个商品，加入购物车按钮上就会显示几个\n        4、点击加入购物车按钮，自动回到订单列表页面\n        ", "start": *************, "stop": *************, "uuid": "514ebf7e-055e-4b00-8b2a-903c6ae3ce3e", "historyId": "c07889fa49f3f486089ccb09940f7445", "testCaseId": "c07889fa49f3f486089ccb09940f7445", "fullName": "src.Dweb.EC.testcases.dweb_account.dweb_order.test_108616_dWeb_my_canceled_order_buy_again_ui_ux.TestDWebMyCanceledOrderBuyAgainUIUX#test_108616_dWeb_my_canceled_order_buy_again_ui_ux", "labels": [{"name": "story", "value": "【108616】 订单列表已取消tab-再来一单流程验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "dweb_porder"}, {"name": "tag", "value": "dweb_todo"}, {"name": "tag", "value": "transaction"}, {"name": "tag", "value": "suqin"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.dweb_account.dweb_order"}, {"name": "suite", "value": "test_108616_dWeb_my_canceled_order_buy_again_ui_ux"}, {"name": "subSuite", "value": "TestDWebMyCanceledOrderBuyAgainUIUX"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "9332-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.dweb_account.dweb_order.test_108616_dWeb_my_canceled_order_buy_again_ui_ux"}]}
import allure
import pytest

from playwright.sync_api import Page

from src.Dweb.EC.dweb_ele.dweb_account.dweb_order import dweb_buy_again_ele, dweb_order_list_ele
from src.Dweb.EC.dweb_pages.dweb_page_account.dweb_page_order.dweb_page_buy_again import DWebBuyAgainPage
from src.Dweb.EC.dweb_pages.dweb_page_account.dweb_page_order.dweb_page_order_list import DWebOrderListPage
from src.config.weee.log_help import log


@allure.story("【108219】 订单列表已取消tab-订单流程验证")
class TestDWebMyCanceledOrderUIUX:
    pytestmark = [pytest.mark.dweb_porder, pytest.mark.dweb_todo, pytest.mark.transaction, pytest.mark.suqin]

    @allure.title("【108219】 订单列表已取消tab-订单流程验证")
    def test_108219_dWeb_my_canceled_order_ui_ux(self, page: dict, pc_autotest_header, login_trace):
        """
        【108219】 订单列表已取消tab-订单流程验证
        测试步骤：
        1、进入订单列表页面，点击切换到已发货tab下
        2、如果存在订单，订单上会存在各种按钮
        3、点击 Return/ Refund 按钮，进入售后退款页面，再返回已送达页面
        6、点击reorder 按钮 ，右侧拉出再来一单选择商品页面，关闭返回已送达页面
        """
        p: Page = page.get("page")
        c = page.get("context")

        # 1. 进入订单列表页面
        order_page = DWebOrderListPage(p, pc_autotest_header, browser_context=c, page_url="/account/my_orders")
        p.wait_for_timeout(3000)
        log.info("成功进入订单列表页面")
        
        # 切换到已取消tab
        shipped_tab = p.locator(dweb_order_list_ele.order_cancelled_tab_ele)
        assert shipped_tab.is_visible(), "未找到已取消tab"
        # 切换到已取消tab
        shipped_tab.click()
        p.wait_for_timeout(2000)
        log.info("成功切换到已取消tab")
        # 验证已切换到已取消tab
        # assert shipped_tab.get_attribute("aria-selected") == "true", "未成功切换到已取消tab"
        
        # 2. 检查存在已取消订单
        order_list_items = p.locator(dweb_order_list_ele.order_list_card_ele).all()
        assert len(order_list_items) > 0,"已取消tab下没有订单，无法继续测试"
        for index, item in enumerate(order_list_items):
            # 验证前10 个订单内容信息
            assert item.locator(dweb_order_list_ele.order_list_card_status_ele).is_visible(), "订单状态不是Cancelled"

            assert item.locator(dweb_order_list_ele.order_list_card_detail_ele).is_visible(), "订单状态不是Cancelled"

            assert item.locator(dweb_order_list_ele.order_list_card_title_ele).is_visible(), "订单状态不是Cancelled"

            assert item.locator(dweb_order_list_ele.order_list_card_status_ele).is_visible(), "订单状态不是Cancelled"
            assert item.locator(dweb_order_list_ele.order_list_card_status_ele).text_content() == "Cancelled", "订单状态不是Cancelled"

        # 选择第一个订单进行操作
        first_order = order_list_items[0]
        
        # 3. 点击 Reorder 按钮
        reorder_btn = first_order.locator(
            dweb_order_list_ele.order_list_cart_btn_ele).locator(u"//button:has-text('Reorder')")
        if reorder_btn.is_visible():
            log.info("找到 Reorder 按钮")
            reorder_btn.click()
            p.wait_for_timeout(3000)
            
            # 验证右侧拉出再来一单选择商品页面
            buy_again_drawer = p.locator(dweb_buy_again_ele.buy_again_reorder_header_ele)
            assert buy_again_drawer.is_visible(), "未成功拉出再来一单选择商品页面"
            log.info("成功拉出再来一单选择商品页面")
            # select all 默认选中
            DWebBuyAgainPage.verify_select_all_state(True)
            # assert p.locator(dweb_buy_again_ele.buy_again_select_all_ele).is_visible()
            log.info("select all 默认选中")
            # 断言加购按钮存在
            assert p.locator(dweb_buy_again_ele.buy_again_add_cart_button_ele).is_visible()
            # 断言加购按钮没有置灰
            DWebBuyAgainPage.verify_add_cart_btn_state(True)
            # assert buy_again_add_cart_button_ele.is_visible()
            # 点击取消 select all按钮
            p.locator(dweb_buy_again_ele.buy_again_select_all_ele).locator(u"/span[1]").click()
            DWebBuyAgainPage.verify_select_all_state(False)
            # 断言select all 被取消
            # assert p.locator(dweb_buy_again_ele.buy_again_unselect_all_ele).is_visible()
            # 断言加购按钮被置灰
            DWebBuyAgainPage.verify_add_cart_btn_state(False)
            # assert buy_again_add_cart_button_ele.get_attribute("disabled") is not None
            # 勾选其中一个商品
            p.locator(dweb_buy_again_ele.buy_again_product_card_ele).first.click()
            # 断言选中
            DWebBuyAgainPage.verify_product_item_state(True)
            # 再次选这select all
            p.locator(dweb_buy_again_ele.buy_again_select_all_ele).locator(u"/span[1]").click()
            DWebBuyAgainPage.verify_select_all_state(True)
            # 点击加入购物车购
            p.locator(dweb_buy_again_ele.buy_again_add_cart_button_ele).click()

            # buy again 自动关闭
            assert buy_again_drawer.is_disabled(), "buy again 自动关闭失败"




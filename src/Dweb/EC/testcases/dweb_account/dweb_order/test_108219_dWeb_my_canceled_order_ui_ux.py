import allure
import pytest

from playwright.sync_api import Page

from src.Dweb.EC.dweb_ele.dweb_account.dweb_order import dweb_buy_again_ele, dweb_order_list_ele
from src.Dweb.EC.dweb_pages.dweb_page_account.dweb_page_order.dweb_page_buy_again import DWebBuyAgainPage
from src.Dweb.EC.dweb_pages.dweb_page_account.dweb_page_order.dweb_page_order_list import DWebOrderListPage
from src.config.weee.log_help import log


@allure.story("【108219】 订单列表已取消tab-订单流程验证")
class TestDWebMyCanceledOrderUIUX:
    pytestmark = [pytest.mark.dweb_porder, pytest.mark.dweb_regression, pytest.mark.transaction, pytest.mark.suqin]

    @allure.title("【108219】 订单列表已取消tab-订单流程验证")
    def test_108219_dWeb_my_canceled_order_ui_ux(self, page: dict, pc_autotest_header, login_trace):
        """
        【108219】 订单列表已取消tab-订单流程验证
        测试步骤：
        1、进入订单列表页面，点击切换到已发货tab下
        2、如果存在订单，订单上会存在各种按钮
        3、点击 Return/ Refund 按钮，进入售后退款页面，再返回已送达页面
        6、点击reorder 按钮 ，右侧拉出再来一单选择商品页面，关闭返回已送达页面
        """
        p: Page = page.get("page")
        c = page.get("context")

        # 1. 进入订单列表页面
        order_page = DWebOrderListPage(p, pc_autotest_header, browser_context=c, page_url="/account/my_orders")
        p.wait_for_timeout(3000)
        log.info("成功进入订单列表页面")
        
        # 切换到已取消tab
        canceled_tab = p.locator(dweb_order_list_ele.order_cancelled_tab_ele)
        assert canceled_tab.is_visible(), "未找到已取消tab"
        # 切换到已取消tab
        canceled_tab.click()
        p.wait_for_timeout(2000)
        # 验证已切换到已取消tab
        assert "filter_status=4" in p.url, "未切换到已取消tab"
        if p.locator(dweb_order_list_ele.order_list_card_ele).is_visible():
            log.info("已取消tab下有订单")
        else:
            log.info("已取消tab下没有订单")
            pytest.skip("已取消tab下没有订单，跳过测试")
        # 2. 检查存在已取消订单
        order_list_items = p.locator(dweb_order_list_ele.order_list_card_ele).all()
        assert len(order_list_items) > 0,"已取消tab下没有订单，无法继续测试"
        for index, item in enumerate(order_list_items):
            # 验证前10 个订单内容信息
            assert item.locator(dweb_order_list_ele.order_list_card_status_ele).is_visible(), "订单内容信息不存在"
            assert item.locator(dweb_order_list_ele.order_list_card_detail_ele).is_visible(), "订单内容信息不存在"
            # seller 订单
            if item.locator(dweb_order_list_ele.order_list_card_title_ele).is_visible():
                assert item.locator(dweb_order_list_ele.order_list_card_title_icon_ele), "seller订单icon信息不存在"

            assert item.locator(dweb_order_list_ele.order_list_card_items_ele).is_visible(), " Delivery date\Order number\Items\Total 信息不存在"
            # 验证订单卡片上 Delivery date\Order number\Items\Total 信息
            for item2 in item.locator(dweb_order_list_ele.order_list_card_items_ele).all():
                for item3 in item2.locator(dweb_order_list_ele.order_list_card_item_ele).all():
                    assert item3.locator(dweb_order_list_ele.order_list_card_info_ele).is_visible()
                    assert item3.locator(dweb_order_list_ele.order_list_card_label_ele).is_visible()
            # 断言订单状态是Cancelled
            assert item.locator(dweb_order_list_ele.order_list_card_statu_ele).text_content() in ("Canceled","Cancelled"), "订单状态不是Cancelled"
            assert item.locator(dweb_order_list_ele.order_list_card_product_ele).is_visible()
        # 点击卡片进入pdp
        order_list_items[0].locator(dweb_order_list_ele.order_list_card_product_ele).click()
        p.wait_for_timeout(2000)
        # 断言页面进入订单详情页面
        assert "order/detail/" in p.url , "没有进入订单详情页面"
        p.go_back()





import allure
import pytest

from playwright.sync_api import Page

from src.Dweb.EC.dweb_pages.dweb_page_account.dweb_page_order.dweb_page_order_list import DWebOrderListPage
from src.config.weee.log_help import log


@allure.story("【108616】 订单列表已取消tab-再来一单流程验证")
class TestDWebMyCanceledOrderBuyAgainUIUX:
    pytestmark = [pytest.mark.dweb_porder, pytest.mark.dweb_todo, pytest.mark.transaction, pytest.mark.suqin]

    @allure.title("【108616】 订单列表已取消tab-再来一单流程验证")
    def test_108616_dWeb_my_canceled_order_buy_again_ui_ux(self, page: dict, pc_autotest_header, login_trace):
        """
        【108616】 订单列表已取消tab-再来一单流程验证
        测试步骤：
        1、进入订单列表页面，点击切换到已取消tab下
        2、找到订单列表下再来一单按钮，右侧会撤拉出商品选择页面
        3、商品选择页面，默认勾选全选按钮，勾选掉某个商品，全选去掉，加入购物车按钮置灰，点击全选商品全部选中，这里如果选中几个商品，加入购物车按钮上就会显示几个
        4、点击加入购物车按钮，自动回到订单列表页面
        """
        p: Page = page.get("page")
        c = page.get("context")
        
        # 1. 进入订单列表页面
        order_page = DWebOrderListPage(p, pc_autotest_header, browser_context=c, page_url="/account/my_orders")
        p.wait_for_timeout(3000)
        log.info("成功进入订单列表页面")
        
        # 切换到已取消tab
        canceled_tab = p.get_by_test_id("order-tab-canceled")
        assert canceled_tab.is_visible(), "未找到已取消tab"
        canceled_tab.click()
        p.wait_for_timeout(2000)
        log.info("成功切换到已取消tab")
        
        # 验证已切换到已取消tab
        assert canceled_tab.get_attribute("aria-selected") == "true", "未成功切换到已取消tab"
        
        # 2. 找到订单列表下再来一单按钮
        # 查找订单列表中的再来一单按钮
        buy_again_buttons = p.get_by_test_id("btn-buy-again").all()
        
        # 如果在已取消tab下没有找到再来一单按钮，则切换回全部订单tab
        if len(buy_again_buttons) == 0:
            log.info("在已取消tab下未找到再来一单按钮，切换回全部订单tab")
            all_orders_tab = p.get_by_test_id("order-tab-all")
            assert all_orders_tab.is_visible(), "未找到全部订单tab"
            all_orders_tab.click()
            p.wait_for_timeout(2000)
            
            # 重新查找再来一单按钮
            buy_again_buttons = p.get_by_test_id("btn-buy-again").all()
        
        assert len(buy_again_buttons) > 0, "订单列表中未找到再来一单按钮"
        
        # 点击第一个再来一单按钮
        buy_again_buttons[0].click()
        p.wait_for_timeout(3000)
        log.info("点击再来一单按钮")
        
        # 验证右侧撤拉出商品选择页面
        select_product_drawer = p.get_by_test_id("buy-again-product-drawer")
        assert select_product_drawer.is_visible(), "未成功撤拉出商品选择页面"
        log.info("成功撤拉出商品选择页面")
        
        # 3. 商品选择页面操作
        # 验证默认勾选全选按钮
        select_all_checkbox = p.get_by_test_id("select-all-checkbox")
        assert select_all_checkbox.is_checked(), "全选按钮默认未被勾选"
        log.info("验证全选按钮默认被勾选")
        
        # 获取所有商品的复选框
        product_checkboxes = p.get_by_test_id("product-checkbox").all()
        assert len(product_checkboxes) > 0, "商品选择页面没有商品"
        log.info(f"商品选择页面有{len(product_checkboxes)}个商品")
        
        # 取消勾选第一个商品
        if len(product_checkboxes) > 0:
            product_checkboxes[0].uncheck()
            p.wait_for_timeout(1000)
            log.info("取消勾选第一个商品")
            
            # 验证全选按钮被取消勾选
            assert not select_all_checkbox.is_checked(), "取消勾选商品后，全选按钮仍被勾选"
            log.info("验证取消勾选商品后，全选按钮也被取消勾选")
            
            # 验证加入购物车按钮是否置灰
            add_to_cart_btn = p.get_by_test_id("btn-add-to-cart")
            assert add_to_cart_btn.is_disabled(), "取消勾选所有商品后，加入购物车按钮未置灰"
            log.info("验证取消勾选所有商品后，加入购物车按钮已置灰")
        
        # 点击全选按钮，选中所有商品
        select_all_checkbox.check()
        p.wait_for_timeout(1000)
        log.info("点击全选按钮，选中所有商品")
        
        # 验证所有商品都被选中
        for i, checkbox in enumerate(product_checkboxes):
            assert checkbox.is_checked(), f"第{i+1}个商品未被选中"
        log.info("验证所有商品都被选中")
        
        # 获取加入购物车按钮上显示的商品数量
        add_to_cart_btn = p.get_by_test_id("btn-add-to-cart")
        assert add_to_cart_btn.is_visible(), "未找到加入购物车按钮"
        assert add_to_cart_btn.is_enabled(), "加入购物车按钮仍然置灰"
        
        # 获取按钮上显示的商品数量
        btn_text = add_to_cart_btn.text_content()
        log.info(f"加入购物车按钮文本: {btn_text}")
        
        # 验证按钮上显示的商品数量与选中的商品数量一致
        assert str(len(product_checkboxes)) in btn_text, "加入购物车按钮上显示的商品数量与选中的商品数量不一致"
        log.info("验证加入购物车按钮上显示的商品数量正确")
        
        # 4. 点击加入购物车按钮
        add_to_cart_btn.click()
        p.wait_for_timeout(3000)
        log.info("点击加入购物车按钮")
        
        # 验证商品选择页面已关闭
        assert not select_product_drawer.is_visible(), "点击加入购物车后，商品选择页面未关闭"
        log.info("商品选择页面已关闭")
        
        # 验证成功回到订单列表页面
        order_list = p.get_by_test_id("order-list")
        assert order_list.is_visible(), "未成功回到订单列表页面"
        log.info("成功回到订单列表页面")
        


{"name": "【112145】 PC-PDP-video list UI/UX验证证", "status": "failed", "statusDetails": {"message": "AssertionError: assert '50' != '50'", "trace": "self = <src.Dweb.EC.testcases.pdp.test_112145_dWeb_pdp_video_list_ui_ux.TestWebSignupOnboardingUIUX object at 0x0000021492495910>\npage = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppDat...943.142>>, 'page': <Page url='https://www.sayweee.com/en/product/DuoDuo-Vegetarian-Rice-Mushroom-Buns-Frozen-1/34460'>}\npc_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...RVkgeJ4xVooQSQ7fPGwZ44qEmEFB89vLcsby3rQSh93pqHdTtH9STkckuVO_yY8IfwH1C35NmD3bmxpNyDdcdQDhFhfCAwjZso0R_j4HVz6CGB_Y', ...}\nlogin_trace = None\n\n    @allure.title(\"【112145】 PC-PDP-video list UI/UX验证证\")\n    # @pytest.mark.list('Transaction', 'suqin')\n    @pytest.mark.present\n    def test_112145_dWeb_pdp_video_list_ui_ux(self, page: dict, pc_autotest_header, login_trace):\n        \"\"\"\n        【112145】 PC-PDP-video list UI/UX验证\n        \"\"\"\n        p: Page = page.get(\"page\")\n        c = page.get(\"context\")\n        # 直接进入指定pdp页面\n        pdp_page = PDPPage(p, pc_autotest_header, browser_context=c,\n                           page_url=\"/product/DuoDuo-Vegetarian-Rice-Mushroom-Buns-Frozen-1/34460\")\n        # 滚动到指定位置\n        scroll_one_page_until(p, pdp_elements.ele_mod_videos)\n        # 获取视频卡片\n        video_cards = pdp_page.FE.eles(pdp_elements.ele_video_card)\n        for index, item in enumerate(video_cards):\n            # 验证视频卡片下方文案\n            assert item.query_selector(pdp_elements.ele_video_card_title).text_content()\n            assert item.query_selector(pdp_elements.ele_video_card_avatar).is_visible()\n            assert item.query_selector(pdp_elements.ele_video_card_like).is_visible()\n            # 点击视频卡片下的点赞/取消点赞\n            item.query_selector(pdp_elements.ele_video_card_like+u\"//span[contains(@class,'flex items-center justify-start')]\").click()\n            like1 = pdp_page.FE.ele((u\"//div[@data-testid='wid-set-like']//div[contains(@class,'text-surface')]\")).text_content()\n            item.query_selector(pdp_elements.ele_video_card_like+u\"//span[contains(@class,'flex items-center justify-start')]\").click()\n            like2 = pdp_page.FE.ele((u\"//div[@data-testid='wid-set-like']//div[contains(@class,'text-surface')]\")).text_content()\n            # 点赞前后数字不一样\n>           assert like1 != like2\nE           AssertionError: assert '50' != '50'\n\ntest_112145_dWeb_pdp_video_list_ui_ux.py:46: AssertionError"}, "description": "\n        【112145】 PC-PDP-video list UI/UX验证\n        ", "start": 1741660907361, "stop": 1741660921745, "uuid": "de55ba51-a48b-47d4-a594-e5d84b660f7c", "historyId": "4b524df509f50ec367c45364efb29cb6", "testCaseId": "4b524df509f50ec367c45364efb29cb6", "fullName": "src.Dweb.EC.testcases.pdp.test_112145_dWeb_pdp_video_list_ui_ux.TestWebSignupOnboardingUIUX#test_112145_dWeb_pdp_video_list_ui_ux", "labels": [{"name": "story", "value": "【112145】 PC-PDP-video list UI/UX验证证"}, {"name": "tag", "value": "present"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.pdp"}, {"name": "suite", "value": "test_112145_dWeb_pdp_video_list_ui_ux"}, {"name": "subSuite", "value": "TestWebSignupOnboardingUIUX"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "12716-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.pdp.test_112145_dWeb_pdp_video_list_ui_ux"}]}
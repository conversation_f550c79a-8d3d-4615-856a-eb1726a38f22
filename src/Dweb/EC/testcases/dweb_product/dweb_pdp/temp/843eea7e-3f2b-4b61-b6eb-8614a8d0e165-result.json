{"name": "【112145】 PC-PDP-video list UI/UX验证证", "status": "broken", "statusDetails": {"message": "AttributeError: 'list' object has no attribute 'all'", "trace": "self = <src.Dweb.EC.testcases.pdp.test_112145_dWeb_pdp_video_list_ui_ux.TestWebSignupOnboardingUIUX object at 0x000001CCF94D5A50>\npage = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppDat...6099.28>>, 'page': <Page url='https://www.sayweee.com/en/product/DuoDuo-Vegetarian-Rice-Mushroom-Buns-Frozen-1/34460'>}\npc_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...5rTT7x_-fUFwx5gmR5Vi9B5eZV1iozHGnpoQ8AnJhBoYnHksLb8CQDkvqp1xqzTSL0YNZOYblKf7_ksAgoNmDrTiYlIZPlILR8QjB1u_Rwz06m_4', ...}\nlogin_trace = None\n\n    @allure.title(\"【112145】 PC-PDP-video list UI/UX验证证\")\n    # @pytest.mark.list('Transaction', 'suqin')\n    @pytest.mark.present\n    def test_112145_dWeb_pdp_video_list_ui_ux(self, page: dict, pc_autotest_header, login_trace):\n        \"\"\"\n        【112145】 PC-PDP-video list UI/UX验证\n        \"\"\"\n        p: Page = page.get(\"page\")\n        c = page.get(\"context\")\n        pdp_page = PDPPage(p, pc_autotest_header, browser_context=c)\n        # 直接进入指定pdp页面\n        p.goto(TEST_URL + \"/product/DuoDuo-Vegetarian-Rice-Mushroom-Buns-Frozen-1/34460\")\n        # 滚动到指定位置\n        scroll_one_page_until(p, pdp_elements.ele_mod_videos)\n        # 获取视频卡片\n>       video_cards = pdp_page.FE.eles(pdp_elements.ele_video_card).all()\nE       AttributeError: 'list' object has no attribute 'all'\n\ntest_112145_dWeb_pdp_video_list_ui_ux.py:34: AttributeError"}, "description": "\n        【112145】 PC-PDP-video list UI/UX验证\n        ", "start": 1741599290326, "stop": 1741599304688, "uuid": "90676b26-60ae-4c5c-bc99-5fe14da9c5c2", "historyId": "4b524df509f50ec367c45364efb29cb6", "testCaseId": "4b524df509f50ec367c45364efb29cb6", "fullName": "src.Dweb.EC.testcases.pdp.test_112145_dWeb_pdp_video_list_ui_ux.TestWebSignupOnboardingUIUX#test_112145_dWeb_pdp_video_list_ui_ux", "labels": [{"name": "story", "value": "【112145】 PC-PDP-video list UI/UX验证证"}, {"name": "tag", "value": "present"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.pdp"}, {"name": "suite", "value": "test_112145_dWeb_pdp_video_list_ui_ux"}, {"name": "subSuite", "value": "TestWebSignupOnboardingUIUX"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "14200-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.pdp.test_112145_dWeb_pdp_video_list_ui_ux"}]}
{"name": "【112065】 PC-PDP-商品组-只有一个属性模块UI/UX验证", "status": "passed", "description": "\n        【112065】 PC-PDP-商品组-只有一个属性模块UI/UX验证\n        ", "start": 1741753595388, "stop": 1741753646143, "uuid": "04169d91-1ca9-4238-ad83-2fd2c6a703ea", "historyId": "e77c573895dc8763bfa660c6db804312", "testCaseId": "e77c573895dc8763bfa660c6db804312", "fullName": "src.Dweb.EC.testcases.pdp.test_112065_dWeb_pdp_product_group_ui_ux.TestDWebPDPProductGroupUIUX#test_112065_dWeb_pdp_product_group_ui_ux", "labels": [{"name": "story", "value": "【112065】 PC-PDP-商品组-只有一个属性模块UI/UX验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "pcpdp"}, {"name": "tag", "value": "transaction"}, {"name": "tag", "value": "suqin"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.pdp"}, {"name": "suite", "value": "test_112065_dWeb_pdp_product_group_ui_ux"}, {"name": "subSuite", "value": "TestDWebPDPProductGroupUIUX"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "1676-Main<PERSON>hread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.pdp.test_112065_dWeb_pdp_product_group_ui_ux"}]}
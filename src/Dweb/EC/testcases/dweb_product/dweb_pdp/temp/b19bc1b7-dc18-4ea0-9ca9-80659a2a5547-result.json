{"name": "【112065】 PC-PDP-商品组-只有一个属性模块UI/UX验证", "status": "broken", "statusDetails": {"message": "playwright._impl._errors.Error: Element is not attached to the DOM", "trace": "self = <src.Dweb.EC.testcases.pdp.test_112065_dWeb_pdp_product_group_ui_ux.TestDWebPDPProductGroupUIUX object at 0x0000024E50780690>\npage = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppDat...20.0.6099.28>>, 'page': <Page url='https://www.sayweee.com/en/product/Genki-Forest-Jasmine-Milk-Tea-Less-Sugar/31532'>}\npc_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...og0PAUjESF-y21cLsyxF5pxb9dHCPfycAQ4SqqDurMrHIvibuF6RwOzYplhq_Q6nVrx6KoqQM0-txywKU9S-gFTAM_mNcZaKaPyWjr_ko_x_xrfg', ...}\nlogin_trace = None\n\n    @allure.title(\"【112065】 PC-PDP-商品组-只有一个属性模块UI/UX验证\")\n    def test_112065_dWeb_pdp_product_group_ui_ux(self, page: dict, pc_autotest_header, login_trace):\n        \"\"\"\n        【112065】 PC-PDP-商品组-只有一个属性模块UI/UX验证\n        \"\"\"\n        p: Page = page.get(\"page\")\n        c = page.get(\"context\")\n        # 直接进入指定pdp页面\n        pdp_page = PDPPage(p, pc_autotest_header, browser_context=c,\n                           page_url=\"/product/Genki-Forest-Milk-Tea--Original-Flavor-Less-Sugar/31533\")\n        # 滚动到指定位置\n        scroll_one_page_until(p, pdp_elements.ele_product_group)\n        # 断言product group 文案存在\n        assert pdp_page.FE.ele(pdp_elements.ele_product_group_title).is_visible()\n        assert pdp_page.FE.ele(pdp_elements.ele_product_group_title + u\"/span\").text_content()\n        assert pdp_page.FE.ele(pdp_elements.ele_product_group_title + u\"/strong\").text_content()\n    \n        # 获取product group 组件商品list\n        product_group_list = pdp_page.FE.eles(pdp_elements.ele_product_group_item_list)\n        for index, item in enumerate(product_group_list):\n            # 验证product_group 商品信息\n            ele_item = u\"//div[contains(@data-testid,'wid-pdp-variation-card-')]\"\n            # 验证product_group 商品图片\n            assert item.query_selector(ele_item + u\"//div[@data-component='CroppedImage']/img\")\n            # 验证product_group 商品title\n            assert item.query_selector(\n                ele_item + u\"//div[contains(@class,'imageCard_right')]//div[contains(@class,'imageCard_title')]\").text_content()\n            # 验证product_group 商品价格\n            assert item.query_selector(\n                ele_item + u\"//div[contains(@class,'imageCard_right')]//div[contains(@class,'imageCard_price')]/div\").text_content()\n            # 点击product group 商品\n>           item.query_selector(ele_item).click()\n\ntest_112065_dWeb_pdp_product_group_ui_ux.py:51: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py:2110: in click\n    self._sync(\n..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_element_handle.py:131: in click\n    await self._channel.send(\"click\", locals_to_params(locals()))\n..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:62: in send\n    return await self._connection.wrap_api_call(\n..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:492: in wrap_api_call\n    return await cb()\n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n\nself = <playwright._impl._connection.Channel object at 0x0000024E50892AD0>\nmethod = 'click', params = {}, return_as_dict = False\n\n    async def inner_send(\n        self, method: str, params: Optional[Dict], return_as_dict: bool\n    ) -> Any:\n        if params is None:\n            params = {}\n        callback = self._connection._send_message_to_server(\n            self._object, method, params\n        )\n        if self._connection._error:\n            error = self._connection._error\n            self._connection._error = None\n            raise error\n        done, _ = await asyncio.wait(\n            {\n                self._connection._transport.on_error_future,\n                callback.future,\n            },\n            return_when=asyncio.FIRST_COMPLETED,\n        )\n        if not callback.future.done():\n            callback.future.cancel()\n>       result = next(iter(done)).result()\nE       playwright._impl._errors.Error: Element is not attached to the DOM\n\n..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:100: Error"}, "description": "\n        【112065】 PC-PDP-商品组-只有一个属性模块UI/UX验证\n        ", "start": 1741749397436, "stop": 1741749939863, "uuid": "bd813d0e-2ba5-49a7-a7fd-b1f7149b53ec", "historyId": "e77c573895dc8763bfa660c6db804312", "testCaseId": "e77c573895dc8763bfa660c6db804312", "fullName": "src.Dweb.EC.testcases.pdp.test_112065_dWeb_pdp_product_group_ui_ux.TestDWebPDPProductGroupUIUX#test_112065_dWeb_pdp_product_group_ui_ux", "labels": [{"name": "story", "value": "【112065】 PC-PDP-商品组-只有一个属性模块UI/UX验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "pcpdp"}, {"name": "tag", "value": "transaction"}, {"name": "tag", "value": "suqin"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.pdp"}, {"name": "suite", "value": "test_112065_dWeb_pdp_product_group_ui_ux"}, {"name": "subSuite", "value": "TestDWebPDPProductGroupUIUX"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "11492-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.pdp.test_112065_dWeb_pdp_product_group_ui_ux"}]}
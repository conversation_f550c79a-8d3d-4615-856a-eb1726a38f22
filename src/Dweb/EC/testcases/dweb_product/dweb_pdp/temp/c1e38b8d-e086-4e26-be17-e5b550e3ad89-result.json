{"name": "【112065】 PC-PDP-商品组-只有一个属性模块UI/UX验证", "status": "failed", "statusDetails": {"message": "AssertionError: assert 'https://www.sayweee.com/en/product/Genki-Forest-Milk-Tea--Original-Flavor-Less-Sugar/31533' != 'https://www.sayweee.com/en/product/Genki-Forest-Milk-Tea--Original-Flavor-Less-Sugar/31533'", "trace": "self = <src.Dweb.EC.testcases.pdp.test_112065_dWeb_pdp_product_group_ui_ux.TestDWebPDPProductGroupUIUX object at 0x000001EEB1CA67D0>\npage = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppDat....28>>, 'page': <Page url='https://www.sayweee.com/en/product/Genki-Forest-Milk-Tea--Original-Flavor-Less-Sugar/31533'>}\npc_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...EQvnFaBXqoa0J6DtXSxyEHFBDAVB0TmNKfm0MN2iEyHnd4Xq5mwFP8sknkRS4nyhfQknR2csn_WhPyb08g_s46qZETDzBOq87_a09y4YtiwfBNOg', ...}\nlogin_trace = None\n\n    @allure.title(\"【112065】 PC-PDP-商品组-只有一个属性模块UI/UX验证\")\n    def test_112065_dWeb_pdp_product_group_ui_ux(self, page: dict, pc_autotest_header, login_trace):\n        \"\"\"\n        【112065】 PC-PDP-商品组-只有一个属性模块UI/UX验证\n        \"\"\"\n        p: Page = page.get(\"page\")\n        c = page.get(\"context\")\n        # 直接进入指定pdp页面\n        pdp_page = PDPPage(p, pc_autotest_header, browser_context=c,\n                           page_url=\"/product/Genki-Forest-Milk-Tea--Original-Flavor-Less-Sugar/31533\")\n        initial_url = p.url\n        # 滚动到指定位置\n        scroll_one_page_until(p, pdp_elements.ele_product_group)\n        # 断言product group 文案存在\n        assert pdp_page.FE.ele(pdp_elements.ele_product_group_title).is_visible()\n        assert pdp_page.FE.ele(pdp_elements.ele_product_group_title + u\"/span\").text_content()\n        assert pdp_page.FE.ele(pdp_elements.ele_product_group_title + u\"/strong\").text_content()\n    \n        # 获取product group 组件商品list\n        product_group_list = pdp_page.FE.eles(pdp_elements.ele_product_group_item_list)\n        for index, item in enumerate(product_group_list):\n            # 验证product_group 商品信息\n    \n            ele_item = u\"//div[contains(@data-testid,wid-pdp-variation-card-)]\"\n            # 验证product_group 商品图片\n            assert item.query_selector(ele_item + u\"//div[@data-component='CroppedImage']/img\")\n            # 验证product_group 商品title\n            assert item.query_selector(\n                ele_item + u\"//div[contains(@class,'imageCard_right')]//div[contains(@class,'imageCard_title')]\").text_content()\n            # 验证product_group 商品价格\n            assert item.query_selector(\n                ele_item + u\"//div[contains(@class,'imageCard_right')]//div[contains(@class,'imageCard_price')]/div\").text_content()\n            # 点击product group 商品\n            variation_card = \"wid-pdp-variation-card-\" + str(index)\n            p.get_by_test_id(variation_card).click()\n            p.wait_for_timeout(2000)\n            # 断言页面url 会变化\n            new_url = p.url\n>           assert new_url != initial_url\nE           AssertionError: assert 'https://www.sayweee.com/en/product/Genki-Forest-Milk-Tea--Original-Flavor-Less-Sugar/31533' != 'https://www.sayweee.com/en/product/Genki-Forest-Milk-Tea--Original-Flavor-Less-Sugar/31533'\n\ntest_112065_dWeb_pdp_product_group_ui_ux.py:58: AssertionError"}, "description": "\n        【112065】 PC-PDP-商品组-只有一个属性模块UI/UX验证\n        ", "start": 1741751873831, "stop": 1741751893224, "uuid": "62ae8eef-f2b0-44ce-9dac-13c5a76c95d0", "historyId": "e77c573895dc8763bfa660c6db804312", "testCaseId": "e77c573895dc8763bfa660c6db804312", "fullName": "src.Dweb.EC.testcases.pdp.test_112065_dWeb_pdp_product_group_ui_ux.TestDWebPDPProductGroupUIUX#test_112065_dWeb_pdp_product_group_ui_ux", "labels": [{"name": "story", "value": "【112065】 PC-PDP-商品组-只有一个属性模块UI/UX验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "pcpdp"}, {"name": "tag", "value": "transaction"}, {"name": "tag", "value": "suqin"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.pdp"}, {"name": "suite", "value": "test_112065_dWeb_pdp_product_group_ui_ux"}, {"name": "subSuite", "value": "TestDWebPDPProductGroupUIUX"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "22336-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.pdp.test_112065_dWeb_pdp_product_group_ui_ux"}]}
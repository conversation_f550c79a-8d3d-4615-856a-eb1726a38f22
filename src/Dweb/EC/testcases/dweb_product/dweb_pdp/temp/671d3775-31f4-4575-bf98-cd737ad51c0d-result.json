{"name": "【112255】 PDP-晒单数量及跳转晒单列表页流程验证", "status": "failed", "statusDetails": {"message": "AssertionError: assert None\n +  where None = <bound method ElementHandle.query_selector of <JSHandle preview=JSHandle@<a data-testid=\"wid-review-card\" aria-label=\"Yennie…>…</a>>>((\"//div[@data-testid='mod-reviews']//a[@data-testid='wid-review-card']\" + '//img'))\n +    where <bound method ElementHandle.query_selector of <JSHandle preview=JSHandle@<a data-testid=\"wid-review-card\" aria-label=\"Yennie…>…</a>>> = <JSHandle preview=JSHandle@<a data-testid=\"wid-review-card\" aria-label=\"Yennie…>…</a>>.query_selector\n +    and   \"//div[@data-testid='mod-reviews']//a[@data-testid='wid-review-card']\" = pdp_elements.ele_mod_review_card", "trace": "self = <src.Dweb.EC.testcases.pdp.test_112255_dWeb_pdp_review_list_ui_ux.TestWebSignupOnboardingUIUX object at 0x000001E090FD6D90>\npage = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppDat...6099.28>>, 'page': <Page url='https://www.sayweee.com/en/product/DuoDuo-Vegetarian-Rice-Mushroom-Buns-Frozen-1/34460'>}\npc_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...WbpRYmIW19hKgNnywckUjbT6yHvLl6Q0pbQyvjLW14-VfGs_wus2i1iCh4ncAuTfeFqpRNB0sZTKxh6P7zAIS1bOjUDCZoFxsr5A0bjin0nk0rx4', ...}\nlogin_trace = None\n\n    @allure.title(\"【112255】 PDP-晒单数量及跳转晒单列表页流程验证\")\n    def test_112255_dWeb_pdp_review_list_ui_ux(self, page: dict, pc_autotest_header, login_trace):\n        \"\"\"\n        【112255】 PDP-晒单数量及跳转晒单列表页流程验证\n        \"\"\"\n        p: Page = page.get(\"page\")\n        c = page.get(\"context\")\n        # 直接进入指定pdp页面\n        pdp_page = PDPPage(p, pc_autotest_header, browser_context=c,\n                           page_url=\"/product/DuoDuo-Vegetarian-Rice-Mushroom-Buns-Frozen-1/34460\")\n        # 滚动到指定位置\n        scroll_one_page_until(p, pdp_elements.ele_mod_review)\n        # pdp_page.FE.ele(pdp_elements.ele_mod_review).hover()\n        # p.get_by_test_id(\"wid-slide-panel-arrow-right\").click()\n        # p.get_by_test_id(\"btn-start-over\").click()\n        # 获取review卡片\n        review_cards = pdp_page.FE.eles(pdp_elements.ele_mod_review_card)\n        for index, item in enumerate(review_cards):\n            # 验证头像存在\n>           assert item.query_selector(pdp_elements.ele_mod_review_card + u\"//img\")\nE           assert None\nE            +  where None = <bound method ElementHandle.query_selector of <JSHandle preview=JSHandle@<a data-testid=\"wid-review-card\" aria-label=\"Yennie…>…</a>>>((\"//div[@data-testid='mod-reviews']//a[@data-testid='wid-review-card']\" + '//img'))\nE            +    where <bound method ElementHandle.query_selector of <JSHandle preview=JSHandle@<a data-testid=\"wid-review-card\" aria-label=\"Yennie…>…</a>>> = <JSHandle preview=JSHandle@<a data-testid=\"wid-review-card\" aria-label=\"Yennie…>…</a>>.query_selector\nE            +    and   \"//div[@data-testid='mod-reviews']//a[@data-testid='wid-review-card']\" = pdp_elements.ele_mod_review_card\n\ntest_112255_dWeb_pdp_review_list_ui_ux.py:39: AssertionError"}, "description": "\n        【112255】 PDP-晒单数量及跳转晒单列表页流程验证\n        ", "start": 1741687212062, "stop": 1741687222600, "uuid": "07f1bacd-6ced-444f-adaf-c346867ba4e7", "historyId": "2454a11dd1be967f8665d656b85e03b8", "testCaseId": "2454a11dd1be967f8665d656b85e03b8", "fullName": "src.Dweb.EC.testcases.pdp.test_112255_dWeb_pdp_review_list_ui_ux.TestWebSignupOnboardingUIUX#test_112255_dWeb_pdp_review_list_ui_ux", "labels": [{"name": "story", "value": "【112255】 PDP-晒单数量及跳转晒单列表页流程验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "pcpdp"}, {"name": "tag", "value": "transaction"}, {"name": "tag", "value": "suqin"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.pdp"}, {"name": "suite", "value": "test_112255_dWeb_pdp_review_list_ui_ux"}, {"name": "subSuite", "value": "TestWebSignupOnboardingUIUX"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "18116-<PERSON><PERSON><PERSON><PERSON>"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.pdp.test_112255_dWeb_pdp_review_list_ui_ux"}]}
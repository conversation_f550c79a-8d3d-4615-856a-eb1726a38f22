{"name": "【112255】 PDP-晒单数量及跳转晒单列表页流程验证", "status": "broken", "statusDetails": {"message": "playwright._impl._errors.TimeoutError: Timeout 50000ms exceeded.", "trace": "self = <src.Dweb.EC.testcases.pdp.test_112255_dWeb_pdp_review_list_ui_ux.TestWebSignupOnboardingUIUX object at 0x0000020B20226D90>\npage = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppDat...6099.28>>, 'page': <Page url='https://www.sayweee.com/en/product/DuoDuo-Vegetarian-Rice-Mushroom-Buns-Frozen-1/34460'>}\npc_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...dzc6NMGuSXizRw2BdHIAFWdkJLw_i6c3XYGJGanaqvz6LtBN3FefF9-yigb93W_ZHzdIfaM5nwRKiEJUYuXXxGngW_vxgxpcPCSf_iSQ0rrFUw5s', ...}\nlogin_trace = None\n\n    @allure.title(\"【112255】 PDP-晒单数量及跳转晒单列表页流程验证\")\n    def test_112255_dWeb_pdp_review_list_ui_ux(self, page: dict, pc_autotest_header, login_trace):\n        \"\"\"\n        【112255】 PDP-晒单数量及跳转晒单列表页流程验证\n        \"\"\"\n        p: Page = page.get(\"page\")\n        c = page.get(\"context\")\n        # 直接进入指定pdp页面\n        pdp_page = PDPPage(p, pc_autotest_header, browser_context=c,\n                           page_url=\"/product/DuoDuo-Vegetarian-Rice-Mushroom-Buns-Frozen-1/34460\")\n        # 滚动到指定位置\n        scroll_one_page_until(p, pdp_elements.ele_mod_review)\n        pdp_page.FE.ele(pdp_elements.ele_mod_review).hover()\n        p.get_by_test_id(\"wid-slide-panel-arrow-right\").click()\n>       p.get_by_test_id(\"btn-start-over\").click()\n\ntest_112255_dWeb_pdp_review_list_ui_ux.py:34: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py:15784: in click\n    self._sync(\n..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_locator.py:159: in click\n    return await self._frame.click(self._selector, strict=True, **params)\n..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_frame.py:484: in click\n    await self._channel.send(\"click\", locals_to_params(locals()))\n..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:62: in send\n    return await self._connection.wrap_api_call(\n..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:492: in wrap_api_call\n    return await cb()\n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n\nself = <playwright._impl._connection.Channel object at 0x0000020B202271D0>\nmethod = 'click'\nparams = {'selector': 'internal:testid=[data-testid=\"btn-start-over\"s]', 'strict': True}\nreturn_as_dict = False\n\n    async def inner_send(\n        self, method: str, params: Optional[Dict], return_as_dict: bool\n    ) -> Any:\n        if params is None:\n            params = {}\n        callback = self._connection._send_message_to_server(\n            self._object, method, params\n        )\n        if self._connection._error:\n            error = self._connection._error\n            self._connection._error = None\n            raise error\n        done, _ = await asyncio.wait(\n            {\n                self._connection._transport.on_error_future,\n                callback.future,\n            },\n            return_when=asyncio.FIRST_COMPLETED,\n        )\n        if not callback.future.done():\n            callback.future.cancel()\n>       result = next(iter(done)).result()\nE       playwright._impl._errors.TimeoutError: Timeout 50000ms exceeded.\n\n..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:100: TimeoutError"}, "description": "\n        【112255】 PDP-晒单数量及跳转晒单列表页流程验证\n        ", "start": 1741687068348, "stop": 1741687141280, "uuid": "d65f5ca6-cad6-4afa-8686-39bfd4d3cbfa", "historyId": "2454a11dd1be967f8665d656b85e03b8", "testCaseId": "2454a11dd1be967f8665d656b85e03b8", "fullName": "src.Dweb.EC.testcases.pdp.test_112255_dWeb_pdp_review_list_ui_ux.TestWebSignupOnboardingUIUX#test_112255_dWeb_pdp_review_list_ui_ux", "labels": [{"name": "story", "value": "【112255】 PDP-晒单数量及跳转晒单列表页流程验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "pcpdp"}, {"name": "tag", "value": "transaction"}, {"name": "tag", "value": "suqin"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.pdp"}, {"name": "suite", "value": "test_112255_dWeb_pdp_review_list_ui_ux"}, {"name": "subSuite", "value": "TestWebSignupOnboardingUIUX"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "15600-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.pdp.test_112255_dWeb_pdp_review_list_ui_ux"}]}
{"name": "【110082】 PDP-Review-晒单数量流程验证", "status": "broken", "statusDetails": {"message": "AttributeError: type object 'typing.re' has no attribute 'search'", "trace": "self = <src.Dweb.EC.testcases.pdp.test_110082_dWeb_pdp_review_num_check.TestWebSignupOnboardingUIUX object at 0x00000278CB23D950>\npage = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppDat...943.142>>, 'page': <Page url='https://www.sayweee.com/en/product/DuoDuo-Vegetarian-Rice-Mushroom-Buns-Frozen-1/34460'>}\npc_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...jqynebWb8ycasSPjruBL8evTKunNiibyMDMZ3DoN1_A7I1iyqKwaqqqo4kcL-o3zhPxd6VyRG91vXguNZAwFOpiHcl15EN6r_Kl7vsedxBaCnbok', ...}\nlogin_trace = None\n\n    @allure.title(\"【110082】 PDP-Review-晒单数量流程验证\")\n    def test_112145_dWeb_pdp_review_num_check(self, page: dict, pc_autotest_header, login_trace):\n        \"\"\"\n        【112145】 PC-PDP-video list UI/UX验证\n        \"\"\"\n        p: Page = page.get(\"page\")\n        c = page.get(\"context\")\n        # 直接进入指定pdp页面\n        pdp_page = PDPPage(p, pc_autotest_header, browser_context=c,\n                           page_url=\"/product/DuoDuo-Vegetarian-Rice-Mushroom-Buns-Frozen-1/34460\")\n        # 滚动到指定位置\n        scroll_one_page_until(p, pdp_elements.ele_mod_review)\n        # 获取review 上的review 数量\n        review_num = pdp_page.FE.ele(pdp_elements.ele_mod_review+u\"//strong\").text_content()\n>       match = re.search(r\"\\('(\\d+)'\\)\", review_num)\n\ntest_110082_dWeb_pdp_review_num_check.py:31: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n\ncls = <class 'typing.re'>, name = 'search'\n\n    def __getattribute__(cls, name):\n        if name not in (\"__dict__\", \"__module__\") and name in cls.__dict__:\n            warnings.warn(\n                f\"{cls.__name__} is deprecated, import directly \"\n                f\"from typing instead. {cls.__name__} will be removed \"\n                \"in Python 3.12.\",\n                DeprecationWarning,\n                stacklevel=2,\n            )\n>       return super().__getattribute__(name)\nE       AttributeError: type object 'typing.re' has no attribute 'search'\n\nC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\typing.py:3339: AttributeError"}, "description": "\n        【112145】 PC-PDP-video list UI/UX验证\n        ", "start": 1741675255709, "stop": 1741675272031, "uuid": "f40b94af-a030-4184-b915-2a91ffdfb281", "historyId": "b66ee85db62c650b9886a2215f3b272f", "testCaseId": "b66ee85db62c650b9886a2215f3b272f", "fullName": "src.Dweb.EC.testcases.pdp.test_110082_dWeb_pdp_review_num_check.TestWebSignupOnboardingUIUX#test_112145_dWeb_pdp_review_num_check", "labels": [{"name": "story", "value": "【110082】 PDP-Review-晒单数量流程验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "pcpdp"}, {"name": "tag", "value": "transaction"}, {"name": "tag", "value": "suqin"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.pdp"}, {"name": "suite", "value": "test_110082_dWeb_pdp_review_num_check"}, {"name": "subSuite", "value": "TestWebSignupOnboardingUIUX"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "22552-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.pdp.test_110082_dWeb_pdp_review_num_check"}]}
{"name": "【112255】 PDP-晒单数量及跳转晒单列表页流程验证", "status": "broken", "statusDetails": {"message": "AttributeError: 'NoneType' object has no attribute 'click'", "trace": "self = <src.Dweb.EC.testcases.pdp.test_112255_dWeb_pdp_review_list_ui_ux.TestWebSignupOnboardingUIUX object at 0x000001D6F43DA210>\npage = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppDat...6099.28>>, 'page': <Page url='https://www.sayweee.com/en/product/DuoDuo-Vegetarian-Rice-Mushroom-Buns-Frozen-1/34460'>}\npc_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...c1_KdSG0fj2C5mdb_bMYXU5OGpxiLYjP5ZpJY2uab9hcALL-IfIEnDTvN-nu8tSCYMT1tl7ZC78D3v7VJ8ZtiK04TZvBXquvPH-lSUBUNWXKIM3Q', ...}\nlogin_trace = None\n\n    @allure.title(\"【112255】 PDP-晒单数量及跳转晒单列表页流程验证\")\n    def test_112255_dWeb_pdp_review_list_ui_ux(self, page: dict, pc_autotest_header, login_trace):\n        \"\"\"\n        【112255】 PDP-晒单数量及跳转晒单列表页流程验证\n        \"\"\"\n        p: Page = page.get(\"page\")\n        c = page.get(\"context\")\n        # 直接进入指定pdp页面\n        pdp_page = PDPPage(p, pc_autotest_header, browser_context=c,\n                           page_url=\"/product/DuoDuo-Vegetarian-Rice-Mushroom-Buns-Frozen-1/34460\")\n        # 滚动到指定位置\n        scroll_one_page_until(p, pdp_elements.ele_mod_review)\n        # pdp_page.FE.ele(pdp_elements.ele_mod_review).hover()\n        # p.get_by_test_id(\"wid-slide-panel-arrow-right\").click()\n        # p.get_by_test_id(\"btn-start-over\").click()\n        # 获取review卡片\n        review_cards = pdp_page.FE.eles(pdp_elements.ele_mod_review_card)\n        for index, item in enumerate(review_cards):\n            # 验证头像存在\n            assert item.query_selector(u\"//div[contains(@class,'ReviewCard_header')]//img\")\n            # 验证创建时间存在\n            assert item.query_selector(u\"//div[contains(@class,'ReviewCard_reviewCreateTime')]\")\n            # 获取review 卡片文案\n            review_comment = item.query_selector(u\"//span[contains(@class,'ReviewCard_comments')]\").text_content()\n            # 点击review卡片弹出pop\n            item.click()\n            p.wait_for_timeout(2000)\n            # 断言review pop\n            assert pdp_page.FE.ele(pdp_elements.ele_review_pop_review_list).is_visible()\n            review_pop_item_list = pdp_page.FE.eles(pdp_elements.ele_review_pop_item_list)\n            for index2, item2 in enumerate(review_pop_item_list):\n                # 获取第一个review_comment\n                review_comment_item2 = item2.query_selector(\n                    u\"//div[contains(@class,'mb-2 tracking')]//span\").text_content()\n                # 断言pdp点击进入的那条晒单会置顶显示\n                assert review_comment == review_comment_item2\n                if index2 == 0:\n                    break\n    \n            # 关闭pop\n>           pdp_page.FE.ele(pdp_elements.video_pop_close_button).click()\nE           AttributeError: 'NoneType' object has no attribute 'click'\n\ntest_112255_dWeb_pdp_review_list_ui_ux.py:60: AttributeError"}, "description": "\n        【112255】 PDP-晒单数量及跳转晒单列表页流程验证\n        ", "start": 1741687891334, "stop": 1741687930587, "uuid": "a465fa40-0269-4b3c-b536-c59e6fd16804", "historyId": "2454a11dd1be967f8665d656b85e03b8", "testCaseId": "2454a11dd1be967f8665d656b85e03b8", "fullName": "src.Dweb.EC.testcases.pdp.test_112255_dWeb_pdp_review_list_ui_ux.TestWebSignupOnboardingUIUX#test_112255_dWeb_pdp_review_list_ui_ux", "labels": [{"name": "story", "value": "【112255】 PDP-晒单数量及跳转晒单列表页流程验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "pcpdp"}, {"name": "tag", "value": "transaction"}, {"name": "tag", "value": "suqin"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.pdp"}, {"name": "suite", "value": "test_112255_dWeb_pdp_review_list_ui_ux"}, {"name": "subSuite", "value": "TestWebSignupOnboardingUIUX"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "18384-Main<PERSON>hread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.pdp.test_112255_dWeb_pdp_review_list_ui_ux"}]}
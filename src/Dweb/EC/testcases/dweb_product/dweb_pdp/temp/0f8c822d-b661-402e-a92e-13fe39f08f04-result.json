{"name": "【112255】 PDP-晒单数量及跳转晒单列表页流程验证", "status": "failed", "statusDetails": {"message": "AssertionError: assert 'Looks good first time trying' == 'Very filling. Delicious. Easy to make. 15 minutes in the steamer and they’re perfect every time!'\n  - Very filling. Delicious. Easy to make. 15 minutes in the steamer and they’re perfect every time!\n  + Looks good first time trying", "trace": "self = <src.Dweb.EC.testcases.pdp.test_112255_dWeb_pdp_review_list_ui_ux.TestWebSignupOnboardingUIUX object at 0x000001FC3DB7B390>\npage = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppDat...6099.28>>, 'page': <Page url='https://www.sayweee.com/en/product/DuoDuo-Vegetarian-Rice-Mushroom-Buns-Frozen-1/34460'>}\npc_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...YRN2Zmde9TBg39Bmx7VOiLK4Bm9tr-8jqtlmDlvcICtDcJwnCUwc8aMHtbYahA-4F9krkdHYDlxfBjtMr6Q9y380VWCM4-90afthgnY3cIQ2xNZ4', ...}\nlogin_trace = None\n\n    @allure.title(\"【112255】 PDP-晒单数量及跳转晒单列表页流程验证\")\n    def test_112255_dWeb_pdp_review_list_ui_ux(self, page: dict, pc_autotest_header, login_trace):\n        \"\"\"\n        【112255】 PDP-晒单数量及跳转晒单列表页流程验证\n        \"\"\"\n        p: Page = page.get(\"page\")\n        c = page.get(\"context\")\n        # 直接进入指定pdp页面\n        pdp_page = PDPPage(p, pc_autotest_header, browser_context=c,\n                           page_url=\"/product/DuoDuo-Vegetarian-Rice-Mushroom-Buns-Frozen-1/34460\")\n        # 滚动到指定位置\n        scroll_one_page_until(p, pdp_elements.ele_mod_review)\n        # pdp_page.FE.ele(pdp_elements.ele_mod_review).hover()\n        # p.get_by_test_id(\"wid-slide-panel-arrow-right\").click()\n        # p.get_by_test_id(\"btn-start-over\").click()\n        # 获取review卡片\n        review_cards = pdp_page.FE.eles(pdp_elements.ele_mod_review_card)\n        for index, item in enumerate(review_cards):\n            # 验证头像存在\n            assert item.query_selector(u\"//div[contains(@class,'ReviewCard_header')]//img\")\n            # 验证创建时间存在\n            assert item.query_selector(u\"//div[contains(@class,'ReviewCard_reviewCreateTime')]\")\n            # 获取review 卡片文案\n            review_comment = item.query_selector(u\"//span[contains(@class,'ReviewCard_comments')]\").text_content()\n            # 点击review卡片弹出pop\n            item.click()\n            p.wait_for_timeout(2000)\n            # 断言review pop\n            assert pdp_page.FE.ele(pdp_elements.ele_review_pop_review_list).is_visible()\n            review_pop_item_list = pdp_page.FE.eles(pdp_elements.ele_review_pop_item_list)\n            for index2, item2 in enumerate(review_pop_item_list):\n                # 获取第一个review_comment\n                review_comment_item2 = item2.query_selector(\n                    u\"//div[contains(@class,'mb-2 tracking')]//span\").text_content()\n                # 断言pdp点击进入的那条晒单会置顶显示\n>               assert review_comment == review_comment_item2\nE               AssertionError: assert 'Looks good first time trying' == 'Very filling. Delicious. Easy to make. 15 minutes in the steamer and they’re perfect every time!'\nE                 - Very filling. Delicious. Easy to make. 15 minutes in the steamer and they’re perfect every time!\nE                 + Looks good first time trying\n\ntest_112255_dWeb_pdp_review_list_ui_ux.py:55: AssertionError"}, "description": "\n        【112255】 PDP-晒单数量及跳转晒单列表页流程验证\n        ", "start": 1741687710982, "stop": 1741687723773, "uuid": "ab748e8b-63d4-496d-8c76-3bca9cc48422", "historyId": "2454a11dd1be967f8665d656b85e03b8", "testCaseId": "2454a11dd1be967f8665d656b85e03b8", "fullName": "src.Dweb.EC.testcases.pdp.test_112255_dWeb_pdp_review_list_ui_ux.TestWebSignupOnboardingUIUX#test_112255_dWeb_pdp_review_list_ui_ux", "labels": [{"name": "story", "value": "【112255】 PDP-晒单数量及跳转晒单列表页流程验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "pcpdp"}, {"name": "tag", "value": "transaction"}, {"name": "tag", "value": "suqin"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.pdp"}, {"name": "suite", "value": "test_112255_dWeb_pdp_review_list_ui_ux"}, {"name": "subSuite", "value": "TestWebSignupOnboardingUIUX"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "26504-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.pdp.test_112255_dWeb_pdp_review_list_ui_ux"}]}
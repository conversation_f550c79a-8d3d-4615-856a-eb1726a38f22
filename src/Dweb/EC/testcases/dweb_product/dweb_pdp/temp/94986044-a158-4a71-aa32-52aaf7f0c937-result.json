{"name": "【112145】 PC-PDP-video list UI/UX验证证", "status": "broken", "statusDetails": {"message": "AttributeError: 'NoneType' object has no attribute 'text_content'", "trace": "self = <src.Dweb.EC.testcases.pdp.test_112145_dWeb_pdp_video_list_ui_ux.TestWebSignupOnboardingUIUX object at 0x00000270ABB43690>\npage = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppDat...943.142>>, 'page': <Page url='https://www.sayweee.com/en/product/DuoDuo-Vegetarian-Rice-Mushroom-Buns-Frozen-1/34460'>}\npc_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...racmZusF8Nmiu9FqW3s9PkSilQTkns55nDQbLwOgV1AlWFlwYgH4nssohff7vIVW96hFF1ON9qYHVwGEwjEnDJfWti_rL3MCFl5B2V8CxEBQ2gCI', ...}\nlogin_trace = None\n\n    @allure.title(\"【112145】 PC-PDP-video list UI/UX验证证\")\n    # @pytest.mark.list('Transaction', 'suqin')\n    @pytest.mark.present\n    def test_112145_dWeb_pdp_video_list_ui_ux(self, page: dict, pc_autotest_header, login_trace):\n        \"\"\"\n        【112145】 PC-PDP-video list UI/UX验证\n        \"\"\"\n        p: Page = page.get(\"page\")\n        c = page.get(\"context\")\n        # 直接进入指定pdp页面\n        pdp_page = PDPPage(p, pc_autotest_header, browser_context=c,\n                           page_url=\"/product/DuoDuo-Vegetarian-Rice-Mushroom-Buns-Frozen-1/34460\")\n        # 滚动到指定位置\n        scroll_one_page_until(p, pdp_elements.ele_mod_videos)\n        # 获取视频卡片\n        video_cards = pdp_page.FE.eles(pdp_elements.ele_video_card)\n        for index, item in enumerate(video_cards):\n            # 验证视频卡片下方文案\n            assert item.query_selector(pdp_elements.ele_video_card_title).text_content()\n            assert item.query_selector(pdp_elements.ele_video_card_avatar).is_visible()\n            assert item.query_selector(pdp_elements.ele_video_card_like).is_visible()\n            # 点击视频卡片下的点赞/取消点赞\n            like1 = pdp_page.FE.ele(pdp_elements.ele_video_card_like+u\"//div[contains(@class,'text-surface')]\").text_content()\n            pdp_page.FE.ele(pdp_elements.ele_video_card_like+u\"//div[contains(@class,'text-surface')]\").click()\n            p.wait_for_timeout(2000)\n            like2 = pdp_page.FE.ele(pdp_elements.ele_video_card_like+u\"//div[contains(@class,'text-surface')]\").text_content()\n            pdp_page.FE.ele(pdp_elements.ele_video_card_like+u\"//div[contains(@class,'text-surface')]\").click()\n            # 点赞前后数字不一样\n            assert like1 != like2\n            # 点击视频卡片弹出pop\n            item.click()\n            p.wait_for_timeout(2000)\n            # 断言video pop\n            assert pdp_page.FE.ele(pdp_elements.video_pop).is_visible()\n            # 点击视频播放\n            pdp_page.FE.ele(pdp_elements.video_pop_video).click()\n    \n            # 点击关注、取消关注---点击关注之后无法取消关注\n    \n            # 点击pop 下的点赞按钮、取消点赞\n>           like3 = pdp_page.FE.ele((pdp_elements.ele_video_card_like+u\"/div\")[2]).text_content()\nE           AttributeError: 'NoneType' object has no attribute 'text_content'\n\ntest_112145_dWeb_pdp_video_list_ui_ux.py:59: AttributeError"}, "description": "\n        【112145】 PC-PDP-video list UI/UX验证\n        ", "start": 1741670836069, "stop": 1741670892968, "uuid": "9336af4f-c36d-4450-8515-fb25b3c79181", "historyId": "4b524df509f50ec367c45364efb29cb6", "testCaseId": "4b524df509f50ec367c45364efb29cb6", "fullName": "src.Dweb.EC.testcases.pdp.test_112145_dWeb_pdp_video_list_ui_ux.TestWebSignupOnboardingUIUX#test_112145_dWeb_pdp_video_list_ui_ux", "labels": [{"name": "story", "value": "【112145】 PC-PDP-video list UI/UX验证证"}, {"name": "tag", "value": "present"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.pdp"}, {"name": "suite", "value": "test_112145_dWeb_pdp_video_list_ui_ux"}, {"name": "subSuite", "value": "TestWebSignupOnboardingUIUX"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "12388-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.pdp.test_112145_dWeb_pdp_video_list_ui_ux"}]}
import allure
import pytest

from src.Dweb.EC.dweb_ele.dweb_product import dweb_pdp_ele
from playwright.sync_api import Page

from src.Dweb.EC.dweb_pages.dweb_page_pdp.dweb_page_pdp import DWebPDPPage
from src.common.commonui import scroll_one_page_until


@allure.story("【112145】 PC-PDP-video list UI/UX验证证")
class TestDWebPDPVideoListUIUX:
    pytestmark = [pytest.mark.pcpdp,pytest.mark.dweb_regression, pytest.mark.transaction, pytest.mark.suqin]
    @allure.title("【112145】 PC-PDP-video list UI/UX验证证")
    def test_112145_dWeb_pdp_video_list_ui_ux(self, page: dict, pc_autotest_header, login_trace):
        """
        【112145】 PC-PDP-video list UI/UX验证
        """
        p: Page = page.get("page")
        c = page.get("context")
        # 1.直接进入指定pdp页面
        pdp_page = DWebPDPPage(p, pc_autotest_header, browser_context=c,
                               page_url="/product/DuoDuo-Vegetarian-Rice-Mushroom-Buns-Frozen-1/34460")
        # 2.滚动到指定位置
        scroll_one_page_until(p, dweb_pdp_ele.ele_mod_videos)
        pdp_page.FE.ele(dweb_pdp_ele.ele_video_card).hover()
        p.get_by_test_id("wid-slide-panel-arrow-right").click()
        p.get_by_test_id("btn-start-over").click()
        # 3.获取视频卡片
        video_cards = pdp_page.FE.eles(dweb_pdp_ele.ele_video_card)
        for index, item in enumerate(video_cards):
            # 3.1验证视频卡片下方文案
            assert item.query_selector(dweb_pdp_ele.ele_video_card_title).text_content()
            assert item.query_selector(dweb_pdp_ele.ele_video_card_avatar).is_visible()
            assert item.query_selector(dweb_pdp_ele.ele_video_card_like).is_visible()
            # 3.2 点击视频卡片下的点赞/取消点赞
            like1 = pdp_page.FE.ele(
                dweb_pdp_ele.ele_video_card_like + u"//div[contains(@class,'text-surface')]").text_content()
            pdp_page.FE.ele(dweb_pdp_ele.ele_video_card_like + u"//div[contains(@class,'text-surface')]").click()
            p.wait_for_timeout(2000)
            like2 = pdp_page.FE.ele(
                dweb_pdp_ele.ele_video_card_like + u"//div[contains(@class,'text-surface')]").text_content()
            pdp_page.FE.ele(dweb_pdp_ele.ele_video_card_like + u"//div[contains(@class,'text-surface')]").click()
            # 3.3 点赞前后数字不一样
            assert like1 != like2
            # 3.4 点击视频卡片弹出pop
            item.click()
            p.wait_for_timeout(2000)
            # 3.5 断言video pop
            assert pdp_page.FE.ele(dweb_pdp_ele.ele_video_pop).is_visible()
            # 3.6 点击视频播放
            pdp_page.FE.ele(dweb_pdp_ele.video_pop_video).click()

            # 3.7 点击关注、取消关注---点击关注之后无法取消关注

            # 3.8 点击pop 下的点赞按钮、取消点赞
            like3 = pdp_page.FE.ele(
                dweb_pdp_ele.ele_video_pop + u"//div[contains(@class,'flex items-end w-full')]//div[@data-testid='wid-set-like']//div").text_content()
            pdp_page.FE.ele(
                dweb_pdp_ele.ele_video_pop + u"//div[contains(@class,'flex items-end w-full')]//div[@data-testid='wid-set-like']//div").click()
            p.wait_for_timeout(2000)
            like4 = pdp_page.FE.ele(
                dweb_pdp_ele.ele_video_pop + u"//div[contains(@class,'flex items-end w-full')]//div[@data-testid='wid-set-like']//div").text_content()
            pdp_page.FE.ele(
                dweb_pdp_ele.ele_video_pop + u"//div[contains(@class,'flex items-end w-full')]//div[@data-testid='wid-set-like']//div").click()
            # 点赞前后数字不一样
            assert like3 != like4
            # 点击全屏按钮
            # 验证post在输入评论前是置灰状态
            post_status1 = pdp_page.FE.ele(dweb_pdp_ele.video_pop_post).evaluate("el => el.getAttributeNames()")
            assert "disabled" in post_status1

            # 输入评论，验证post 高亮
            # pdp_page.FE.ele(pdp_elements.video_pop_comment).click()
            p.get_by_placeholder("Add comments...").fill("test")
            post_status2 = pdp_page.FE.ele(dweb_pdp_ele.video_pop_post).evaluate("el => el.getAttributeNames()")

            # 验证post高亮
            assert "disabled" not in post_status2
            # 3.9 断言右侧评论存在
            # 3.10 点击replay 回复，验证下拉输入框
            # 3.11 点击查看原始评论
            video_pop_product_card = pdp_page.FE.eles(dweb_pdp_ele.video_pop_product_card)
            # assert video_pop_product_card.is_visible()
            for index2, item2 in enumerate(video_pop_product_card):
                # 点击加购商品
                pdp_page.FE.ele(dweb_pdp_ele.video_pop_product_card + u"//div[@data-testid='btn-atc-plus']").click()
                if index2 == 0:
                    break
            # 4关闭pop
            pdp_page.FE.ele(dweb_pdp_ele.video_pop_close_button).click()
            if index == 0:
                break

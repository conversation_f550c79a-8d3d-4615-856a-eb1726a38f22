{"name": "【110171】 PC/mobile购物车-匿名用户操作稍后再买", "status": "broken", "statusDetails": {"message": "AttributeError: 'NoneType' object has no attribute 'click'", "trace": "self = <test_110171_dWeb_anny_cart_save_for_later_ui_ux.TestDWebAnnyCartSaveForLaterUIUX object at 0x000001B5EF3C2B90>\nnot_login_page = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppDat...ght\\chromium-1091\\chrome-win\\chrome.exe> version=120.0.6099.28>>, 'page': <Page url='https://www.sayweee.com/en/cart'>}\npc_anony_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...bRaXq4nvfbmqBkAzy414COjro04gd_M9LX-RVHTpn97qMovf5kyXKFLsTy3HFBIsIwx-0U7QyLu-T3mER-zTpXAVkrolQCIN7ypG5EEnvuYXnuac', ...}\n\n    @allure.title(\"【110171】 PC/mobile购物车-匿名用户操作稍后再买\")\n    def test_110171_dWeb_anny_cart_save_for_later_ui_ux(self, not_login_page: dict, pc_anony_header):\n        \"\"\"\n        【110171】 PC/mobile购物车-匿名用户操作稍后再买\n        \"\"\"\n        p: Page = not_login_page.get(\"page\")\n        c = not_login_page.get(\"context\")\n    \n        # 直接进入指定页面\n        cart_page = DWebCartPage(p, pc_anony_header, browser_context=c, page_url=\"/cart\")\n        p.wait_for_timeout(3000)\n        # 滚动到指定位置-猜你喜欢\n        scroll_one_page_until(p, dweb_cart_ele.ele_recommend_module)\n    \n        # 获取猜你喜欢商品\n        recommend_card = cart_page.FE.eles(dweb_cart_ele.ele_recommend_module_card)\n        for index1, item1 in enumerate(recommend_card):\n            # 加购推荐商品\n            item1.query_selector(u\"//div[@data-testid='btn-atc-plus']\").click()\n            p.wait_for_timeout(1000)\n            if index1 == 2:\n                break\n        # 回到购物车第一个商品位置\n        p.query_selector(f\"{dweb_cart_ele.ele_cart_normal_card}\" + \"[1]\").scroll_into_view_if_needed()\n        p.wait_for_timeout(2000)\n        # 点击购物车商品 稍后购买按钮\n        normal_card = cart_page.FE.eles(dweb_cart_ele.ele_cart_normal_card)\n        for index2, item2 in enumerate(normal_card):\n>           item2.query_selector(u\"//div[@data-testid='btn-save-for-later']\").click()\nE           AttributeError: 'NoneType' object has no attribute 'click'\n\ntest_110171_dWeb_anny_cart_save_for_later_ui_ux.py:43: AttributeError"}, "description": "\n        【110171】 PC/mobile购物车-匿名用户操作稍后再买\n        ", "start": 1744102642455, "stop": 1744102673711, "uuid": "faf162dd-01ff-4dd7-929b-c95dcf9a3669", "historyId": "543af7cfc8da0f2860cb6658ed4ba843", "testCaseId": "543af7cfc8da0f2860cb6658ed4ba843", "fullName": "src.Dweb.EC.testcases.dweb_cart.test_110171_dWeb_anny_cart_save_for_later_ui_ux.TestDWebAnnyCartSaveForLaterUIUX#test_110171_dWeb_anny_cart_save_for_later_ui_ux", "labels": [{"name": "story", "value": "【110171】 PC/mobile购物车-匿名用户操作稍后再买"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "h5pdp"}, {"name": "tag", "value": "regression"}, {"name": "tag", "value": "transaction"}, {"name": "tag", "value": "suqin"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.dweb_cart"}, {"name": "suite", "value": "test_110171_dWeb_anny_cart_save_for_later_ui_ux"}, {"name": "subSuite", "value": "TestDWebAnnyCartSaveForLaterUIUX"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "972-Main<PERSON>hread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.dweb_cart.test_110171_dWeb_anny_cart_save_for_later_ui_ux"}]}
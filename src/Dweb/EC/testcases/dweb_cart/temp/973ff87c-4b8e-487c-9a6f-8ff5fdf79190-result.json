{"name": "【109702】 PC首页右上角小购物车-小购物车的交互", "status": "failed", "statusDetails": {"message": "AssertionError: assert False\n +  where False = <bound method ElementHandle.is_visible of <JSHandle preview=JSHandle@<img width=\"80\" height=\"80\" class=\"rounded object-cov…/>>>()\n +    where <bound method ElementHandle.is_visible of <JSHandle preview=JSHandle@<img width=\"80\" height=\"80\" class=\"rounded object-cov…/>>> = <JSHandle preview=JSHandle@<img width=\"80\" height=\"80\" class=\"rounded object-cov…/>>.is_visible", "trace": "self = <test_109702_dWeb_mini_grocery_cart_ui_ux.TestDWebMiniGroceryCartUIUX object at 0x0000026F54F28190>\npage = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppDat...om/en/category/sale?filter_sub_category=sale&filters=%7B%22delivery_type%22%3A%22%22%7D&trigger_type=filter&offset=0'>}\npc_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...vBgKn7NwdDnH8t2OS-4n5DfyBPJwlup57eVokMlqTLze0n6vnTLg7z3raXyuv1pfssIqP0nebLbWxiTEMRYWj3e7hvvmdU6bPInDfkmdKJeOctlk', ...}\nlogin_trace = None\n\n    @allure.title(\"【109702】 PC首页右上角小购物车-小购物车的交互\")\n    @pytest.mark.present\n    def test_109702_dWeb_mini_grocery_cart_ui_ux(self, page: dict, pc_autotest_header, login_trace):\n        \"\"\"\n        【109702】 PC首页右上角小购物车-小购物车的交互\n        \"\"\"\n        p: Page = page.get(\"page\")\n        c = page.get(\"context\")\n        # 构造的首页页面\n        home_page = HomePage(p, pc_autotest_header, browser_context=c)\n        # 清空购物车\n        try:\n            empty_cart(pc_autotest_header)\n            # 清空购物车之后刷新页面\n            p.reload()\n            p.wait_for_timeout(2000)\n        except Exception as e:\n            log.info(\"清空购物车发生异常\" + str(e))\n        # 鼠标悬停又上角的mini购物车\n        p.get_by_test_id(\"wid-mini-cart\").hover()\n        p.wait_for_timeout(2000)\n        # 断言mini 空购物车img存在\n        assert home_page.FE.ele(cart_elements.ele_mini_cart_img).is_visible()\n        # 断言mini空购物车文案存在\n        assert home_page.FE.ele(cart_elements.ele_mini_cart_text).is_visible()\n    \n        # 构造的分类页面\n        category_page = CategoryPage(p, pc_autotest_header, browser_context=c)\n        # 去分类页加购Local类型的商品进购物车\n        category_page.add_to_local_product_cart_from_category()\n        p.reload()\n        p.wait_for_timeout(2000)\n        # 断言购物车商品数字大于0\n        cart_num = p.get_by_test_id(\"wid-mini-cart\").locator(u\"//span[contains(@class,'MiniCart_cartIconCountQty')]\")\n        assert cart_num, f'购物车商品是null'\n    \n        # 鼠标悬停又上角的mini购物车\n        p.get_by_test_id(\"wid-mini-cart\").hover()\n        p.wait_for_timeout(2000)\n        # 断言mini 购物车有商品\n        assert home_page.FE.ele(cart_elements.ele_mini_items_num)\n        # 断言goto cart 按钮存在\n        goto_cart_button = home_page.FE.ele(cart_elements.ele_mini_goto_cart_button)\n        assert goto_cart_button.is_visible()\n        # mini_items_list: List[ElementHandle] = acd.query_selector_all(cart_elements.ele_cart_products)\n    \n        mini_items_list = home_page.FE.eles(cart_elements.ele_mini_items_list)\n    \n        for item in mini_items_list:\n            # 验证mini 购物车商品image 存在\n            item_img = item.query_selector(u\"//div[@data-component='CroppedImage']/img[@src]\")\n>           assert item_img.is_visible()\nE           assert False\nE            +  where False = <bound method ElementHandle.is_visible of <JSHandle preview=JSHandle@<img width=\"80\" height=\"80\" class=\"rounded object-cov…/>>>()\nE            +    where <bound method ElementHandle.is_visible of <JSHandle preview=JSHandle@<img width=\"80\" height=\"80\" class=\"rounded object-cov…/>>> = <JSHandle preview=JSHandle@<img width=\"80\" height=\"80\" class=\"rounded object-cov…/>>.is_visible\n\ntest_109702_dWeb_mini_grocery_cart_ui_ux.py:72: AssertionError"}, "description": "\n        【109702】 PC首页右上角小购物车-小购物车的交互\n        ", "start": 1741166899011, "stop": 1741166975117, "uuid": "3a03cab9-cccb-4f24-ba8c-55b67b041d97", "historyId": "3617f75bd014acb27d4ae0c667d0e476", "testCaseId": "3617f75bd014acb27d4ae0c667d0e476", "fullName": "src.Dweb.EC.testcases.cart.test_109702_dWeb_mini_grocery_cart_ui_ux.TestDWebMiniGroceryCartUIUX#test_109702_dWeb_mini_grocery_cart_ui_ux", "labels": [{"name": "story", "value": "【109702】 PC首页右上角小购物车-小购物车的交互"}, {"name": "tag", "value": "present"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.cart"}, {"name": "suite", "value": "test_109702_dWeb_mini_grocery_cart_ui_ux"}, {"name": "subSuite", "value": "TestDWebMiniGroceryCartUIUX"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "17056-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.cart.test_109702_dWeb_mini_grocery_cart_ui_ux"}]}
{"name": "【109702】 PC首页右上角小购物车-小购物车的交互", "status": "broken", "statusDetails": {"message": "AttributeError: 'NoneType' object has no attribute 'is_visible'", "trace": "self = <test_109702_dWeb_mini_grocery_cart_ui_ux.TestDWebMiniGroceryCartUIUX object at 0x0000025F3B08C110>\npage = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppDat...aywright\\chromium-1091\\chrome-win\\chrome.exe> version=120.0.6099.28>>, 'page': <Page url='https://www.sayweee.com/en'>}\npc_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...IRJSoTtsHg9pE9fc2g0CREUu8jj7Nrka0VrAtufELhEJ3bZ149sZhoRTSfLm49bQiCl2p651vMoUwVAJF0sf6xoF-khlBmzyGKhDJtDwiY9MS29k', ...}\nlogin_trace = None\n\n    @allure.title(\"【109702】 PC首页右上角小购物车-小购物车的交互\")\n    @pytest.mark.present\n    def test_109702_dWeb_mini_grocery_cart_ui_ux(self, page: dict, pc_autotest_header, login_trace):\n        \"\"\"\n        【109702】 PC首页右上角小购物车-小购物车的交互\n        \"\"\"\n        p: Page = page.get(\"page\")\n        c = page.get(\"context\")\n        # 构造的首页页面\n        home_page = HomePage(p, pc_autotest_header, browser_context=c)\n        # 清空购物车\n        try:\n            empty_cart(pc_autotest_header)\n            # 清空购物车之后刷新页面\n            p.reload()\n            p.wait_for_timeout(2000)\n        except Exception as e:\n            log.info(\"清空购物车发生异常\" + str(e))\n        # 鼠标悬停又上角的mini购物车\n        p.get_by_test_id(\"wid-mini-cart\").hover()\n        p.wait_for_timeout(2000)\n        # 断言mini 空购物车img存在\n>       assert home_page.FE.ele(cart_elements.ele_mini_cart_img).is_visible()\nE       AttributeError: 'NoneType' object has no attribute 'is_visible'\n\ntest_109702_dWeb_mini_grocery_cart_ui_ux.py:43: AttributeError"}, "description": "\n        【109702】 PC首页右上角小购物车-小购物车的交互\n        ", "start": 1741164605843, "stop": 1741164628176, "uuid": "f4b1a581-886f-44a4-8874-a691362c0b95", "historyId": "3617f75bd014acb27d4ae0c667d0e476", "testCaseId": "3617f75bd014acb27d4ae0c667d0e476", "fullName": "src.Dweb.EC.testcases.cart.test_109702_dWeb_mini_grocery_cart_ui_ux.TestDWebMiniGroceryCartUIUX#test_109702_dWeb_mini_grocery_cart_ui_ux", "labels": [{"name": "story", "value": "【109702】 PC首页右上角小购物车-小购物车的交互"}, {"name": "tag", "value": "present"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.cart"}, {"name": "suite", "value": "test_109702_dWeb_mini_grocery_cart_ui_ux"}, {"name": "subSuite", "value": "TestDWebMiniGroceryCartUIUX"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "28184-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.cart.test_109702_dWeb_mini_grocery_cart_ui_ux"}]}
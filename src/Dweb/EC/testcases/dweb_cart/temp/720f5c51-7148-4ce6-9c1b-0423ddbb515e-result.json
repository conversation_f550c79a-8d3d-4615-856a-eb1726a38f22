{"name": "PC购物车-空购物车UI/UX验证", "status": "failed", "statusDetails": {"message": "AssertionError: assert False\n +  where False = <bound method Locator.is_visible of <Locator frame=<Frame name= url='https://www.sayweee.com/en'> selector=\"//main[@id='cart-main']//div[text()='Your cart is empty']\">>()\n +    where <bound method Locator.is_visible of <Locator frame=<Frame name= url='https://www.sayweee.com/en'> selector=\"//main[@id='cart-main']//div[text()='Your cart is empty']\">> = <Locator frame=<Frame name= url='https://www.sayweee.com/en'> selector=\"//main[@id='cart-main']//div[text()='Your cart is empty']\">.is_visible\n +      where <Locator frame=<Frame name= url='https://www.sayweee.com/en'> selector=\"//main[@id='cart-main']//div[text()='Your cart is empty']\"> = <bound method Page.locator of <Page url='https://www.sayweee.com/en'>>(\"//main[@id='cart-main']//div[text()='Your cart is empty']\")\n +        where <bound method Page.locator of <Page url='https://www.sayweee.com/en'>> = <Page url='https://www.sayweee.com/en'>.locator\n +        and   \"//main[@id='cart-main']//div[text()='Your cart is empty']\" = cart_elements.ele_cart_text", "trace": "self = <test_113360_dWeb_empty_cart_ui_ux.TestWebSignupOnboardingUIUX object at 0x000002182C929B50>\npage = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppDat...aywright\\chromium-1091\\chrome-win\\chrome.exe> version=120.0.6099.28>>, 'page': <Page url='https://www.sayweee.com/en'>}\npc_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...3jI2curtQe_-fvGogJhxcsZwiPl7Q9jwrhPbtzEM2grp80xbjuxGwJlWjMGdzpFYUP1MYCSggECO4l-ZC9pe1yf5LZCCHddShvC7IqOi42WmCanU', ...}\nlogin_trace = None\n\n    @allure.title(\"PC购物车-空购物车UI/UX验证\")\n    @pytest.mark.present\n    def test_113360_dWeb_empty_cart_ui_ux(self, page: dict, pc_autotest_header, login_trace):\n        \"\"\"\n        【113360】 PC购物车-空购物车UI/UX验证\n        \"\"\"\n        p: Page = page.get(\"page\")\n        c = page.get(\"context\")\n        cart_page = CartPage(p, pc_autotest_header, browser_context=c)\n        # 点击进入购物车\n        p.get_by_test_id(\"wid-mini-cart\").click()\n    \n        # 清空购物车\n        try:\n            empty_cart(self.header)\n        except Exception as e:\n            log.info(\"清空购物车发生异常\" + str(e))\n        # 断言空购物车img存在\n        assert p.locator(cart_elements.ele_cart_img).is_visible()\n        # 断言空购物车 文案存在\n>       assert p.locator(cart_elements.ele_cart_text).is_visible()\nE       assert False\nE        +  where False = <bound method Locator.is_visible of <Locator frame=<Frame name= url='https://www.sayweee.com/en'> selector=\"//main[@id='cart-main']//div[text()='Your cart is empty']\">>()\nE        +    where <bound method Locator.is_visible of <Locator frame=<Frame name= url='https://www.sayweee.com/en'> selector=\"//main[@id='cart-main']//div[text()='Your cart is empty']\">> = <Locator frame=<Frame name= url='https://www.sayweee.com/en'> selector=\"//main[@id='cart-main']//div[text()='Your cart is empty']\">.is_visible\nE        +      where <Locator frame=<Frame name= url='https://www.sayweee.com/en'> selector=\"//main[@id='cart-main']//div[text()='Your cart is empty']\"> = <bound method Page.locator of <Page url='https://www.sayweee.com/en'>>(\"//main[@id='cart-main']//div[text()='Your cart is empty']\")\nE        +        where <bound method Page.locator of <Page url='https://www.sayweee.com/en'>> = <Page url='https://www.sayweee.com/en'>.locator\nE        +        and   \"//main[@id='cart-main']//div[text()='Your cart is empty']\" = cart_elements.ele_cart_text\n\ntest_113360_dWeb_empty_cart_ui_ux.py:36: AssertionError"}, "description": "\n        【113360】 PC购物车-空购物车UI/UX验证\n        ", "start": 1740469120320, "stop": 1740469148617, "uuid": "dc3c9683-d479-4290-94d2-708b4a1755f1", "historyId": "330a60b1c1dd5b375c18d6a4fcfadc3d", "testCaseId": "330a60b1c1dd5b375c18d6a4fcfadc3d", "fullName": "src.Dweb.EC.testcases.cart.test_113360_dWeb_empty_cart_ui_ux.TestWebSignupOnboardingUIUX#test_113360_dWeb_empty_cart_ui_ux", "labels": [{"name": "story", "value": "PC购物车-空购物车UI/UX验证"}, {"name": "tag", "value": "present"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.cart"}, {"name": "suite", "value": "test_113360_dWeb_empty_cart_ui_ux"}, {"name": "subSuite", "value": "TestWebSignupOnboardingUIUX"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "8004-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.cart.test_113360_dWeb_empty_cart_ui_ux"}]}
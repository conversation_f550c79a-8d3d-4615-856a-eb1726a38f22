{"uuid": "d950f7ec-01f2-4e11-9478-7fb14c1f8a0d", "children": ["78fcc177-20b1-42dc-984e-235cb99663e6"], "befores": [{"name": "playwright", "status": "broken", "statusDetails": {"message": "KeyboardInterrupt\n", "trace": "  File \"D:\\MyWork\\Python\\EC-demo\\qa-ui-dmweb\\venv\\Lib\\site-packages\\pluggy\\_callers.py\", line 77, in _multicall\n    res = hook_impl.function(*args)\n          ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\MyWork\\Python\\EC-demo\\qa-ui-dmweb\\venv\\Lib\\site-packages\\_pytest\\fixtures.py\", line 1123, in pytest_fixture_setup\n    result = call_fixture_func(fixturefunc, request, kwargs)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\MyWork\\Python\\EC-demo\\qa-ui-dmweb\\venv\\Lib\\site-packages\\_pytest\\fixtures.py\", line 895, in call_fixture_func\n    fixture_result = next(generator)\n                     ^^^^^^^^^^^^^^^\n  File \"D:\\MyWork\\Python\\EC-demo\\qa-ui-dmweb\\src\\Dweb\\EC\\conftest.py\", line 153, in playwright\n    playwright = sync_playwright().start()\n                 ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\MyWork\\Python\\EC-demo\\qa-ui-dmweb\\venv\\Lib\\site-packages\\playwright\\sync_api\\_context_manager.py\", line 84, in start\n    return self.__enter__()\n           ^^^^^^^^^^^^^^^^\n  File \"D:\\MyWork\\Python\\EC-demo\\qa-ui-dmweb\\venv\\Lib\\site-packages\\playwright\\sync_api\\_context_manager.py\", line 77, in __enter__\n    dispatcher_fiber.switch()\n  File \"D:\\MyWork\\Python\\EC-demo\\qa-ui-dmweb\\venv\\Lib\\site-packages\\playwright\\sync_api\\_context_manager.py\", line 56, in greenlet_main\n    self._loop.run_until_complete(self._connection.run_as_sync())\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\base_events.py\", line 640, in run_until_complete\n    self.run_forever()\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\windows_events.py\", line 321, in run_forever\n    super().run_forever()\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\base_events.py\", line 607, in run_forever\n    self._run_once()\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\base_events.py\", line 1884, in _run_once\n    event_list = self._selector.select(timeout)\n                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\windows_events.py\", line 444, in select\n    self._poll(timeout)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\windows_events.py\", line 817, in _poll\n    status = _overlapped.GetQueuedCompletionStatus(self._iocp, ms)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n"}, "start": 1744108491624, "stop": 1744108495868}], "start": 1744108491624, "stop": 1744108497947}
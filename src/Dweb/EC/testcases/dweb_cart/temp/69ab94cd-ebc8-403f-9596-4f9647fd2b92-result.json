{"name": "PC购物车-多个购物车样式", "status": "broken", "statusDetails": {"message": "playwright._impl._errors.Error: Clicking the checkbox did not change its state", "trace": "self = <test_109546_dWeb_multi_grocery_cart_ui_ux.TestDWebMultiGroceryCartUIUX object at 0x0000025132BE1F50>\npage = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppDat...e.exe> version=120.0.6099.28>>, 'page': <Page url='https://www.sayweee.com/en/category/sale?filter_sub_category=sale'>}\npc_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...jqVVaSwCTqutUJyWhXHzqwX_C0v5cqKzvT32Okqqe3YdteVCKxRJQg1_J_OYJkXFfuC4bh4eqghGl1IFdM16xZnPyz07L5WFT98YemjfWdgCX1bM', ...}\nlogin_trace = None\n\n    @allure.title(\"PC购物车-多个购物车样式\")\n    @pytest.mark.present\n    def test_109546_dWeb_multi_grocery_cart_ui_ux(self, page: dict, pc_autotest_header, login_trace):\n        \"\"\"\n        【109546】 PC购物车-多个购物车样式\n        \"\"\"\n        p: Page = page.get(\"page\")\n        c = page.get(\"context\")\n        # 构造的购物车页面\n        cart_page = CartPage(p, pc_autotest_header, browser_context=c)\n        # # 点击进入购物车\n        # p.get_by_test_id(\"wid-mini-cart\").click()\n        # p.get_by_test_id(\"wid-direct-from-japan\").hover()\n        # p.wait_for_timeout(3000)\n        # 清空购物车\n        try:\n            empty_cart(pc_autotest_header)\n        except Exception as e:\n            log.info(\"清空购物车发生异常\" + str(e))\n        # 构造的分类页面\n        category_page = CategoryPage(p, pc_autotest_header, browser_context=c)\n        # 去分类页加购各种类型的商品进购物车\n>       category_page.add_to_many_cart_from_category()\n\ntest_109546_dWeb_multi_grocery_cart_ui_ux.py:41: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n..\\..\\dweb_pages\\page_category\\page_category.py:84: in add_to_many_cart_from_category\n    self.category_filter_delivery_type_check(delivery_type_local)\n..\\..\\dweb_pages\\page_category\\page_category.py:44: in category_filter_delivery_type_check\n    filter_delivery_type.check()\n..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py:15686: in check\n    self._sync(\n..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_locator.py:144: in check\n    return await self._frame.check(self._selector, strict=True, **params)\n..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_frame.py:741: in check\n    await self._channel.send(\"check\", locals_to_params(locals()))\n..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:62: in send\n    return await self._connection.wrap_api_call(\n..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:492: in wrap_api_call\n    return await cb()\n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n\nself = <playwright._impl._connection.Channel object at 0x0000025133191690>\nmethod = 'check'\nparams = {'selector': 'internal:testid=[data-testid=\"btn-delivery_type-delivery_type_local\"s]', 'strict': True}\nreturn_as_dict = False\n\n    async def inner_send(\n        self, method: str, params: Optional[Dict], return_as_dict: bool\n    ) -> Any:\n        if params is None:\n            params = {}\n        callback = self._connection._send_message_to_server(\n            self._object, method, params\n        )\n        if self._connection._error:\n            error = self._connection._error\n            self._connection._error = None\n            raise error\n        done, _ = await asyncio.wait(\n            {\n                self._connection._transport.on_error_future,\n                callback.future,\n            },\n            return_when=asyncio.FIRST_COMPLETED,\n        )\n        if not callback.future.done():\n            callback.future.cancel()\n>       result = next(iter(done)).result()\nE       playwright._impl._errors.Error: Clicking the checkbox did not change its state\n\n..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:100: Error"}, "description": "\n        【109546】 PC购物车-多个购物车样式\n        ", "start": 1741078237583, "stop": 1741078329677, "uuid": "75b753ae-0c63-42a7-b024-325fde1d4788", "historyId": "6adf790d4dfdedef5d1582fde0b092dc", "testCaseId": "6adf790d4dfdedef5d1582fde0b092dc", "fullName": "src.Dweb.EC.testcases.cart.test_109546_dWeb_multi_grocery_cart_ui_ux.TestDWebMultiGroceryCartUIUX#test_109546_dWeb_multi_grocery_cart_ui_ux", "labels": [{"name": "story", "value": "PC购物车-多个购物车样式"}, {"name": "tag", "value": "present"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.cart"}, {"name": "suite", "value": "test_109546_dWeb_multi_grocery_cart_ui_ux"}, {"name": "subSuite", "value": "TestDWebMultiGroceryCartUIUX"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "27212-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.cart.test_109546_dWeb_multi_grocery_cart_ui_ux"}]}
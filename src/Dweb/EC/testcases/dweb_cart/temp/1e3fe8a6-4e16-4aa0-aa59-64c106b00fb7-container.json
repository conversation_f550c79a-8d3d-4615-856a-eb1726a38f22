{"uuid": "6ca01af7-e380-4746-ac0c-22031740f477", "children": ["09625783-dfba-4658-857b-96f4696364c2"], "befores": [{"name": "porder", "status": "broken", "statusDetails": {"message": "KeyboardInterrupt\n", "trace": "  File \"D:\\MyWork\\Python\\EC-demo\\qa-ui-dmweb\\venv\\Lib\\site-packages\\pluggy\\_callers.py\", line 77, in _multicall\n    res = hook_impl.function(*args)\n          ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\MyWork\\Python\\EC-demo\\qa-ui-dmweb\\venv\\Lib\\site-packages\\_pytest\\fixtures.py\", line 1123, in pytest_fixture_setup\n    result = call_fixture_func(fixturefunc, request, kwargs)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\MyWork\\Python\\EC-demo\\qa-ui-dmweb\\venv\\Lib\\site-packages\\_pytest\\fixtures.py\", line 902, in call_fixture_func\n    fixture_result = fixturefunc(**kwargs)\n                     ^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\MyWork\\Python\\EC-demo\\qa-ui-dmweb\\src\\Dweb\\EC\\conftest.py\", line 138, in porder\n    porder = query_simple_preorder_v1(pc_autotest_header)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\MyWork\\Python\\EC-demo\\qa-ui-dmweb\\src\\api\\porder.py\", line 9, in query_simple_preorder_v1\n    res = HR.request({\n          ^^^^^^^^^^^^\n  File \"D:\\MyWork\\Python\\EC-demo\\qa-ui-dmweb\\src\\utils\\HttpRequest.py\", line 31, in request\n    response = cls.__get(url=data.get(\"path\"), params=data.get(\"param\"), headers=data.get(\"headers\"), **kwargs)\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\MyWork\\Python\\EC-demo\\qa-ui-dmweb\\src\\utils\\HttpRequest.py\", line 51, in __get\n    response = requests.get(url, params=params, headers=headers, **kwargs)\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\MyWork\\Python\\EC-demo\\qa-ui-dmweb\\venv\\Lib\\site-packages\\requests\\api.py\", line 73, in get\n    return request(\"get\", url, params=params, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\MyWork\\Python\\EC-demo\\qa-ui-dmweb\\venv\\Lib\\site-packages\\requests\\api.py\", line 59, in request\n    return session.request(method=method, url=url, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\MyWork\\Python\\EC-demo\\qa-ui-dmweb\\venv\\Lib\\site-packages\\requests\\sessions.py\", line 589, in request\n    resp = self.send(prep, **send_kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\MyWork\\Python\\EC-demo\\qa-ui-dmweb\\venv\\Lib\\site-packages\\requests\\sessions.py\", line 703, in send\n    r = adapter.send(request, **kwargs)\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\MyWork\\Python\\EC-demo\\qa-ui-dmweb\\venv\\Lib\\site-packages\\requests\\adapters.py\", line 667, in send\n    resp = conn.urlopen(\n           ^^^^^^^^^^^^^\n  File \"D:\\MyWork\\Python\\EC-demo\\qa-ui-dmweb\\venv\\Lib\\site-packages\\urllib3\\connectionpool.py\", line 789, in urlopen\n    response = self._make_request(\n               ^^^^^^^^^^^^^^^^^^^\n  File \"D:\\MyWork\\Python\\EC-demo\\qa-ui-dmweb\\venv\\Lib\\site-packages\\urllib3\\connectionpool.py\", line 536, in _make_request\n    response = conn.getresponse()\n               ^^^^^^^^^^^^^^^^^^\n  File \"D:\\MyWork\\Python\\EC-demo\\qa-ui-dmweb\\venv\\Lib\\site-packages\\urllib3\\connection.py\", line 464, in getresponse\n    httplib_response = super().getresponse()\n                       ^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\http\\client.py\", line 1378, in getresponse\n    response.begin()\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\http\\client.py\", line 318, in begin\n    version, status, reason = self._read_status()\n                              ^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\http\\client.py\", line 279, in _read_status\n    line = str(self.fp.readline(_MAXLINE + 1), \"iso-8859-1\")\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\socket.py\", line 706, in readinto\n    return self._sock.recv_into(b)\n           ^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\ssl.py\", line 1278, in recv_into\n    return self.read(nbytes, buffer)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\ssl.py\", line 1134, in read\n    return self._sslobj.read(len, buffer)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n"}, "start": 1741164967034, "stop": 1741164967834}], "start": 1741164967034, "stop": 1741164968185}
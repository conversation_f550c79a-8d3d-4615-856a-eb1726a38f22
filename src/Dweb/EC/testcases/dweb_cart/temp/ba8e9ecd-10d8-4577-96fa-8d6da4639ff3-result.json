{"name": "PC购物车-空购物车UI/UX验证", "status": "passed", "description": "\n        【113360】 PC购物车-空购物车UI/UX验证\n        ", "start": 1740469546368, "stop": 1740469588241, "uuid": "33dcb7a8-9046-4a41-8d7e-e77609bec708", "historyId": "330a60b1c1dd5b375c18d6a4fcfadc3d", "testCaseId": "330a60b1c1dd5b375c18d6a4fcfadc3d", "fullName": "src.Dweb.EC.testcases.cart.test_113360_dWeb_empty_cart_ui_ux.TestWebSignupOnboardingUIUX#test_113360_dWeb_empty_cart_ui_ux", "labels": [{"name": "story", "value": "PC购物车-空购物车UI/UX验证"}, {"name": "tag", "value": "present"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.cart"}, {"name": "suite", "value": "test_113360_dWeb_empty_cart_ui_ux"}, {"name": "subSuite", "value": "TestWebSignupOnboardingUIUX"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "20268-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.cart.test_113360_dWeb_empty_cart_ui_ux"}]}
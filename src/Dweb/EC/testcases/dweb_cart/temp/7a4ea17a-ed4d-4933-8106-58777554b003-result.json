{"name": "【109702】 PC首页右上角小购物车-小购物车的交互", "status": "failed", "statusDetails": {"message": "AssertionError: assert (True and '$' in 'Go to cart')\n +  where True = <bound method ElementHandle.is_visible of <JSHandle preview=JSHandle@<button shape=\"round\" type=\"submit\" class=\"Button_button…>Go to cart</button>>>()\n +    where <bound method ElementHandle.is_visible of <JSHandle preview=J<PERSON>Handle@<button shape=\"round\" type=\"submit\" class=\"Button_button…>Go to cart</button>>> = <JSHandle preview=JSHandle@<button shape=\"round\" type=\"submit\" class=\"Button_button…>Go to cart</button>>.is_visible\n +  and   'Go to cart' = <bound method ElementHandle.text_content of <JSHandle preview=JSHandle@<button shape=\"round\" type=\"submit\" class=\"Button_button…>Go to cart</button>>>()\n +    where <bound method ElementHandle.text_content of <JSHandle preview=JSHandle@<button shape=\"round\" type=\"submit\" class=\"Button_button…>Go to cart</button>>> = <JSHandle preview=JSHandle@<button shape=\"round\" type=\"submit\" class=\"Button_button…>Go to cart</button>>.text_content", "trace": "self = <test_109702_dWeb_mini_grocery_cart_ui_ux.TestDWebMiniGroceryCartUIUX object at 0x000001D04ABA32D0>\npage = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppDat...om/en/category/sale?filter_sub_category=sale&filters=%7B%22delivery_type%22%3A%22%22%7D&trigger_type=filter&offset=0'>}\npc_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...gUC-4OzmqUEiKlMwyxbsP9vSVCjggDBZowvTNOurvCDXMRdV1Nii48Nk_Mfcchcagj7p_mqO-e_Se-dIYFWxVoA3kD_sdZvZvvJl0g-8dfxPYdEQ', ...}\nlogin_trace = None\n\n    @allure.title(\"【109702】 PC首页右上角小购物车-小购物车的交互\")\n    @pytest.mark.present\n    def test_109702_dWeb_mini_grocery_cart_ui_ux(self, page: dict, pc_autotest_header, login_trace):\n        \"\"\"\n        【109702】 PC首页右上角小购物车-小购物车的交互\n        \"\"\"\n        p: Page = page.get(\"page\")\n        c = page.get(\"context\")\n        # 构造的首页页面\n        home_page = HomePage(p, pc_autotest_header, browser_context=c)\n        # 清空购物车\n        try:\n            empty_cart(pc_autotest_header)\n            # 清空购物车之后刷新页面\n            p.reload()\n            p.wait_for_timeout(2000)\n        except Exception as e:\n            log.info(\"清空购物车发生异常\" + str(e))\n        # 鼠标悬停又上角的mini购物车\n        p.get_by_test_id(\"wid-mini-cart\").hover()\n        p.wait_for_timeout(2000)\n        # 断言mini 空购物车img存在\n        assert home_page.FE.ele(cart_elements.ele_mini_cart_img).is_visible()\n        # 断言mini空购物车文案存在\n        assert home_page.FE.ele(cart_elements.ele_mini_cart_text).is_visible()\n    \n        # 构造的分类页面\n        category_page = CategoryPage(p, pc_autotest_header, browser_context=c)\n        # 去分类页加购Local类型的商品进购物车\n        category_page.add_to_local_product_cart_from_category()\n        # 断言购物车商品数字大于0\n        cart_num = p.get_by_test_id(\"wid-mini-cart\").locator(u\"//span[contains(@class,'MiniCart_cartIconCountQty')]\")\n        assert cart_num, f'购物车商品是null'\n    \n        # 鼠标悬停又上角的mini购物车\n        p.get_by_test_id(\"wid-mini-cart\").hover()\n        # 断言mini 购物车有商品\n        assert home_page.FE.ele(cart_elements.ele_mini_items_num)\n        # 断言goto cart 按钮存在\n        goto_cart_button = home_page.FE.ele(cart_elements.ele_mini_goto_cart_button)\n>       assert goto_cart_button.is_visible() and \"$\" in goto_cart_button.text_content()\nE       assert (True and '$' in 'Go to cart')\nE        +  where True = <bound method ElementHandle.is_visible of <JSHandle preview=JSHandle@<button shape=\"round\" type=\"submit\" class=\"Button_button…>Go to cart</button>>>()\nE        +    where <bound method ElementHandle.is_visible of <JSHandle preview=JSHandle@<button shape=\"round\" type=\"submit\" class=\"Button_button…>Go to cart</button>>> = <JSHandle preview=JSHandle@<button shape=\"round\" type=\"submit\" class=\"Button_button…>Go to cart</button>>.is_visible\nE        +  and   'Go to cart' = <bound method ElementHandle.text_content of <JSHandle preview=JSHandle@<button shape=\"round\" type=\"submit\" class=\"Button_button…>Go to cart</button>>>()\nE        +    where <bound method ElementHandle.text_content of <JSHandle preview=JSHandle@<button shape=\"round\" type=\"submit\" class=\"Button_button…>Go to cart</button>>> = <JSHandle preview=JSHandle@<button shape=\"round\" type=\"submit\" class=\"Button_button…>Go to cart</button>>.text_content\n\ntest_109702_dWeb_mini_grocery_cart_ui_ux.py:61: AssertionError"}, "description": "\n        【109702】 PC首页右上角小购物车-小购物车的交互\n        ", "start": 1741164876287, "stop": 1741164917978, "uuid": "f4a34b2e-4edb-4c99-a13f-d6cdccd60692", "historyId": "3617f75bd014acb27d4ae0c667d0e476", "testCaseId": "3617f75bd014acb27d4ae0c667d0e476", "fullName": "src.Dweb.EC.testcases.cart.test_109702_dWeb_mini_grocery_cart_ui_ux.TestDWebMiniGroceryCartUIUX#test_109702_dWeb_mini_grocery_cart_ui_ux", "labels": [{"name": "story", "value": "【109702】 PC首页右上角小购物车-小购物车的交互"}, {"name": "tag", "value": "present"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.cart"}, {"name": "suite", "value": "test_109702_dWeb_mini_grocery_cart_ui_ux"}, {"name": "subSuite", "value": "TestDWebMiniGroceryCartUIUX"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "22096-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.cart.test_109702_dWeb_mini_grocery_cart_ui_ux"}]}
{"name": "【109702】 PC首页右上角小购物车-小购物车的交互", "status": "broken", "statusDetails": {"message": "TypeError: argument of type 'ElementHandle' is not iterable", "trace": "self = <test_109702_dWeb_mini_grocery_cart_ui_ux.TestDWebMiniGroceryCartUIUX object at 0x000001F25B0F0E10>\npage = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppDat...om/en/category/sale?filter_sub_category=sale&filters=%7B%22delivery_type%22%3A%22%22%7D&trigger_type=filter&offset=0'>}\npc_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...oPKYQE6z9Xi4oLZF796Z8JVDFnfiIcCbx4kbQq9N-6wkLkgu0LBVfmEHcLP-yiWQq0kWEwX9-hLpjIj9ymq7Xo5C16F8P_RQLgtCZd-bN44uBko8', ...}\nlogin_trace = None\n\n    @allure.title(\"【109702】 PC首页右上角小购物车-小购物车的交互\")\n    @pytest.mark.present\n    def test_109702_dWeb_mini_grocery_cart_ui_ux(self, page: dict, pc_autotest_header, login_trace):\n        \"\"\"\n        【109702】 PC首页右上角小购物车-小购物车的交互\n        \"\"\"\n        p: Page = page.get(\"page\")\n        c = page.get(\"context\")\n        # 构造的首页页面\n        home_page = HomePage(p, pc_autotest_header, browser_context=c)\n        # 清空购物车\n        try:\n            empty_cart(pc_autotest_header)\n            # 清空购物车之后刷新页面\n            p.reload()\n            p.wait_for_timeout(2000)\n        except Exception as e:\n            log.info(\"清空购物车发生异常\" + str(e))\n        # 鼠标悬停又上角的mini购物车\n        p.get_by_test_id(\"wid-mini-cart\").hover()\n        p.wait_for_timeout(2000)\n        # 断言mini 空购物车img存在\n        assert home_page.FE.ele(cart_elements.ele_mini_cart_img).is_visible()\n        # 断言mini空购物车文案存在\n        assert home_page.FE.ele(cart_elements.ele_mini_cart_text).is_visible()\n    \n        # 构造的分类页面\n        category_page = CategoryPage(p, pc_autotest_header, browser_context=c)\n        # 去分类页加购Local类型的商品进购物车\n        category_page.add_to_local_product_cart_from_category()\n        p.reload()\n        p.wait_for_timeout(2000)\n        # 断言购物车商品数字大于0\n        cart_num = p.get_by_test_id(\"wid-mini-cart\").locator(u\"//span[contains(@class,'MiniCart_cartIconCountQty')]\")\n        assert cart_num, f'购物车商品是null'\n    \n        # 鼠标悬停又上角的mini购物车\n        p.get_by_test_id(\"wid-mini-cart\").hover()\n        p.wait_for_timeout(2000)\n        # 断言progress bar 进度条模块\n        progress_tip_bar = home_page.FE.ele(cart_elements.ele_mini_progress_tip_bar).get_attribute(\"class\")\n        if \"success\" in progress_tip_bar:\n            # 进度条满了\n            assert \"unlocked. Nicely done!\" in home_page.FE.ele(cart_elements.ele_mini_progress_tip_copy)\n        else:\n            # 未满\n>           assert \"Add\" in home_page.FE.ele(cart_elements.ele_mini_progress_tip_copy)\nE           TypeError: argument of type 'ElementHandle' is not iterable\n\ntest_109702_dWeb_mini_grocery_cart_ui_ux.py:67: TypeError"}, "description": "\n        【109702】 PC首页右上角小购物车-小购物车的交互\n        ", "start": 1741168370309, "stop": 1741168436774, "uuid": "3b8dc836-6d4d-4018-9254-3e109c92823d", "historyId": "3617f75bd014acb27d4ae0c667d0e476", "testCaseId": "3617f75bd014acb27d4ae0c667d0e476", "fullName": "src.Dweb.EC.testcases.cart.test_109702_dWeb_mini_grocery_cart_ui_ux.TestDWebMiniGroceryCartUIUX#test_109702_dWeb_mini_grocery_cart_ui_ux", "labels": [{"name": "story", "value": "【109702】 PC首页右上角小购物车-小购物车的交互"}, {"name": "tag", "value": "present"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.cart"}, {"name": "suite", "value": "test_109702_dWeb_mini_grocery_cart_ui_ux"}, {"name": "subSuite", "value": "TestDWebMiniGroceryCartUIUX"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "27492-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.cart.test_109702_dWeb_mini_grocery_cart_ui_ux"}]}
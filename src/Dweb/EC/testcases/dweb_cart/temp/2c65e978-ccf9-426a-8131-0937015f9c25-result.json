{"name": "PC购物车-多个购物车样式", "status": "passed", "description": "\n        【109546】 PC购物车-多个购物车样式\n        ", "start": 1741082740874, "stop": 1741082852276, "uuid": "8a31ceaa-6b60-40d5-bc0b-d26c56f77195", "historyId": "6adf790d4dfdedef5d1582fde0b092dc", "testCaseId": "6adf790d4dfdedef5d1582fde0b092dc", "fullName": "src.Dweb.EC.testcases.cart.test_109546_dWeb_multi_grocery_cart_ui_ux.TestDWebMultiGroceryCartUIUX#test_109546_dWeb_multi_grocery_cart_ui_ux", "labels": [{"name": "story", "value": "PC购物车-多个购物车样式"}, {"name": "tag", "value": "present"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.cart"}, {"name": "suite", "value": "test_109546_dWeb_multi_grocery_cart_ui_ux"}, {"name": "subSuite", "value": "TestDWebMultiGroceryCartUIUX"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "15564-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.cart.test_109546_dWeb_multi_grocery_cart_ui_ux"}]}
{"name": "PC购物车-多个购物车样式", "status": "failed", "statusDetails": {"message": "AssertionError: assert ('$' in 'FREE' or 'Free' == 'FREE'\n +  where 'FREE' = <bound method ElementHandle.text_content of <JSHandle preview=JSHandle@<span class=\"flex-shrink-0\">…</span>>>()\n +    where <bound method ElementHandle.text_content of <JSHandle preview=J<PERSON>Handle@<span class=\"flex-shrink-0\">…</span>>> = <JSHandle preview=JSHandle@<span class=\"flex-shrink-0\">…</span>>.text_content\n  - FREE\n  + Free)", "trace": "self = <test_109546_dWeb_multi_grocery_cart_ui_ux.TestDWebMultiGroceryCartUIUX object at 0x000001FC0FF22510>\npage = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppDat...ght\\chromium-1091\\chrome-win\\chrome.exe> version=120.0.6099.28>>, 'page': <Page url='https://www.sayweee.com/en/cart'>}\npc_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...8dabmeXViuTLlB2qs3HvNN6rTZKYAkenKlU_FqI9RA0gNfY0-pwNe1x53a1_OeHeWnVdaK9DTmhCQd1HNkI590aPgxOuZ2-m6NumuQeCeIj4xz7k', ...}\nlogin_trace = None\n\n    @allure.title(\"PC购物车-多个购物车样式\")\n    @pytest.mark.present\n    def test_109546_dWeb_multi_grocery_cart_ui_ux(self, page: dict, pc_autotest_header, login_trace):\n        \"\"\"\n        【109546】 PC购物车-多个购物车样式\n        \"\"\"\n        p: Page = page.get(\"page\")\n        c = page.get(\"context\")\n        # 构造的购物车页面\n        cart_page = CartPage(p, pc_autotest_header, browser_context=c)\n        # # 点击进入购物车\n        # p.get_by_test_id(\"wid-mini-cart\").click()\n        # p.get_by_test_id(\"wid-direct-from-japan\").hover()\n        # p.wait_for_timeout(3000)\n        # 清空购物车\n        try:\n            empty_cart(pc_autotest_header)\n        except Exception as e:\n            log.info(\"清空购物车发生异常\" + str(e))\n        # 构造的分类页面\n        category_page = CategoryPage(p, pc_autotest_header, browser_context=c)\n        # 去分类页加购各种类型的商品进购物车\n        category_page.add_to_many_cart_from_category()\n        # 光标移到global+ 位置\n        p.get_by_test_id(\"wid-direct-from-japan\").hover()\n    \n        # 断言\n        p.wait_for_selector(cart_elements.ele_cart_summary)\n        assert cart_page.FE.ele(cart_elements.ele_cart_summary).is_visible()\n        assert \"title\" in cart_page.FE.ele(cart_elements.ele_cart_summary).get_attribute(\"class\")\n        # 0. 判断第一个购物车是local delivery\n        assert \"Local delivery\" == cart_page.FE.ele(cart_elements.ele_cart_summary_local_delivery).text_content()\n        # 1. 判断subtotal元素存在\n        assert cart_page.FE.ele(cart_elements.ele_cart_subtatal).is_visible()\n        sub_total_fee = cart_page.FE.ele(cart_elements.ele_cart_subtatal_fee)\n        # 2. 判断subtotal值\n        assert sub_total_fee.is_visible() and \"$\" in sub_total_fee.text_content()\n    \n        # 获取所有的items total\n        items_total = cart_page.FE.eles(cart_elements.ele_cart_items_total)\n        assert items_total, f\"items_total={items_total}\"\n        # 3. 判断items_total中有美元符号存在\n        for item in items_total:\n            log.debug(\"item.text_content===>\" + item.text_content())\n            assert \"$\" in item.text_content()\n    \n        # 4. 判断delivery_fee中有美元符号存在或为free\n        delivery_fee = cart_page.FE.eles(cart_elements.ele_cart_delivery_fee)\n        for df in delivery_fee:\n            log.debug(\"delivery_fee的content===>\" + df.text_content())\n>           assert \"$\" in df.text_content() or 'Free' == df.text_content()\nE           assert ('$' in 'FREE' or 'Free' == 'FREE'\nE            +  where 'FREE' = <bound method ElementHandle.text_content of <JSHandle preview=JSHandle@<span class=\"flex-shrink-0\">…</span>>>()\nE            +    where <bound method ElementHandle.text_content of <JSHandle preview=JSHandle@<span class=\"flex-shrink-0\">…</span>>> = <JSHandle preview=JSHandle@<span class=\"flex-shrink-0\">…</span>>.text_content\nE             - FREE\nE             + Free)\n\ntest_109546_dWeb_multi_grocery_cart_ui_ux.py:69: AssertionError"}, "description": "\n        【109546】 PC购物车-多个购物车样式\n        ", "start": 1741077446815, "stop": 1741077563719, "uuid": "6337f415-69e9-412c-b7f0-aab67f319811", "historyId": "6adf790d4dfdedef5d1582fde0b092dc", "testCaseId": "6adf790d4dfdedef5d1582fde0b092dc", "fullName": "src.Dweb.EC.testcases.cart.test_109546_dWeb_multi_grocery_cart_ui_ux.TestDWebMultiGroceryCartUIUX#test_109546_dWeb_multi_grocery_cart_ui_ux", "labels": [{"name": "story", "value": "PC购物车-多个购物车样式"}, {"name": "tag", "value": "present"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.cart"}, {"name": "suite", "value": "test_109546_dWeb_multi_grocery_cart_ui_ux"}, {"name": "subSuite", "value": "TestDWebMultiGroceryCartUIUX"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "17840-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.cart.test_109546_dWeb_multi_grocery_cart_ui_ux"}]}
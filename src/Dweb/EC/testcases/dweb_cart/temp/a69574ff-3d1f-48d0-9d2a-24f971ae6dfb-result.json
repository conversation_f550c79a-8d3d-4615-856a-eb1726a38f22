{"name": "【109702】 PC首页右上角小购物车-小购物车的交互", "status": "broken", "statusDetails": {"message": "playwright._impl._errors.Error: DOMException: Failed to execute 'evaluate' on 'Document': The string '//div[contains(@class='MiniCart_emptyCartWrapper')]//img[contains(@src,'cart_empty')]' is not a valid XPath expression.\n    at Object.queryAll (<anonymous>:41:25)\n    at InjectedScript._queryEngineAll (<anonymous>:4859:49)\n    at InjectedScript.querySelectorAll (<anonymous>:4846:30)\n    at InjectedScript.querySelector (<anonymous>:4785:25)\n    at eval (eval at evaluate (:226:30), <anonymous>:5:34)\n    at UtilityScript.evaluate (<anonymous>:228:17)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)", "trace": "self = <test_109702_dWeb_mini_grocery_cart_ui_ux.TestDWebMiniGroceryCartUIUX object at 0x00000200954E8C90>\npage = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppDat...aywright\\chromium-1091\\chrome-win\\chrome.exe> version=120.0.6099.28>>, 'page': <Page url='https://www.sayweee.com/en'>}\npc_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...QH1jhe5P48Y7hgPPwHxUXKJOIy-Gk1ME3FpIRSsICoKh5G13flC9iS0dEkHd-rrQ6kP51x3Vq_PuSwjGgzC9ZhXBnYsYooBTWoAx8H1Zw04yFT3Q', ...}\nlogin_trace = None\n\n    @allure.title(\"【109702】 PC首页右上角小购物车-小购物车的交互\")\n    @pytest.mark.present\n    def test_109702_dWeb_mini_grocery_cart_ui_ux(self, page: dict, pc_autotest_header, login_trace):\n        \"\"\"\n        【109702】 PC首页右上角小购物车-小购物车的交互\n        \"\"\"\n        p: Page = page.get(\"page\")\n        c = page.get(\"context\")\n        # 构造的首页页面\n        home_page = HomePage(p, pc_autotest_header, browser_context=c)\n        # 清空购物车\n        try:\n            empty_cart(pc_autotest_header)\n            # 清空购物车之后刷新页面\n            p.reload()\n            p.wait_for_timeout(2000)\n        except Exception as e:\n            log.info(\"清空购物车发生异常\" + str(e))\n        # 鼠标悬停又上角的mini购物车\n        p.get_by_test_id(\"wid-mini-cart\").hover()\n        # 断言mini 空购物车img存在\n>       assert p.locator(cart_elements.ele_mini_cart_img).is_visible()\n\ntest_109702_dWeb_mini_grocery_cart_ui_ux.py:42: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py:17546: in is_visible\n    self._sync(self._impl_obj.is_visible(timeout=timeout))\n..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_locator.py:499: in is_visible\n    return await self._frame.is_visible(\n..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_frame.py:348: in is_visible\n    return await self._channel.send(\"isVisible\", locals_to_params(locals()))\n..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:62: in send\n    return await self._connection.wrap_api_call(\n..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:492: in wrap_api_call\n    return await cb()\n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n\nself = <playwright._impl._connection.Channel object at 0x0000020095485F50>\nmethod = 'isVisible'\nparams = {'selector': \"//div[contains(@class='MiniCart_emptyCartWrapper')]//img[contains(@src,'cart_empty')]\", 'strict': True}\nreturn_as_dict = False\n\n    async def inner_send(\n        self, method: str, params: Optional[Dict], return_as_dict: bool\n    ) -> Any:\n        if params is None:\n            params = {}\n        callback = self._connection._send_message_to_server(\n            self._object, method, params\n        )\n        if self._connection._error:\n            error = self._connection._error\n            self._connection._error = None\n            raise error\n        done, _ = await asyncio.wait(\n            {\n                self._connection._transport.on_error_future,\n                callback.future,\n            },\n            return_when=asyncio.FIRST_COMPLETED,\n        )\n        if not callback.future.done():\n            callback.future.cancel()\n>       result = next(iter(done)).result()\nE       playwright._impl._errors.Error: DOMException: Failed to execute 'evaluate' on 'Document': The string '//div[contains(@class='MiniCart_emptyCartWrapper')]//img[contains(@src,'cart_empty')]' is not a valid XPath expression.\nE           at Object.queryAll (<anonymous>:41:25)\nE           at InjectedScript._queryEngineAll (<anonymous>:4859:49)\nE           at InjectedScript.querySelectorAll (<anonymous>:4846:30)\nE           at InjectedScript.querySelector (<anonymous>:4785:25)\nE           at eval (eval at evaluate (:226:30), <anonymous>:5:34)\nE           at UtilityScript.evaluate (<anonymous>:228:17)\nE           at UtilityScript.<anonymous> (<anonymous>:1:44)\n\n..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:100: Error"}, "description": "\n        【109702】 PC首页右上角小购物车-小购物车的交互\n        ", "start": 1741164468130, "stop": 1741164490306, "uuid": "090bd270-8901-473f-bd6c-885a26efa7f7", "historyId": "3617f75bd014acb27d4ae0c667d0e476", "testCaseId": "3617f75bd014acb27d4ae0c667d0e476", "fullName": "src.Dweb.EC.testcases.cart.test_109702_dWeb_mini_grocery_cart_ui_ux.TestDWebMiniGroceryCartUIUX#test_109702_dWeb_mini_grocery_cart_ui_ux", "labels": [{"name": "story", "value": "【109702】 PC首页右上角小购物车-小购物车的交互"}, {"name": "tag", "value": "present"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.cart"}, {"name": "suite", "value": "test_109702_dWeb_mini_grocery_cart_ui_ux"}, {"name": "subSuite", "value": "TestDWebMiniGroceryCartUIUX"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "13032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.cart.test_109702_dWeb_mini_grocery_cart_ui_ux"}]}
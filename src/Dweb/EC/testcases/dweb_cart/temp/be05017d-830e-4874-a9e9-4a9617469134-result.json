{"name": "PC购物车-空购物车UI/UX验证", "status": "failed", "statusDetails": {"message": "AssertionError: assert False\n +  where False = <bound method Locator.is_visible of <Locator frame=<Frame name= url='https://www.sayweee.com/en'> selector=\"//img[contains(@src,'cart_empty')]\">>()\n +    where <bound method Locator.is_visible of <Locator frame=<Frame name= url='https://www.sayweee.com/en'> selector=\"//img[contains(@src,'cart_empty')]\">> = <Locator frame=<Frame name= url='https://www.sayweee.com/en'> selector=\"//img[contains(@src,'cart_empty')]\">.is_visible\n +      where <Locator frame=<Frame name= url='https://www.sayweee.com/en'> selector=\"//img[contains(@src,'cart_empty')]\"> = <bound method Page.locator of <Page url='https://www.sayweee.com/en'>>(\"//img[contains(@src,'cart_empty')]\")\n +        where <bound method Page.locator of <Page url='https://www.sayweee.com/en'>> = <Page url='https://www.sayweee.com/en'>.locator\n +        and   \"//img[contains(@src,'cart_empty')]\" = cart_elements.ele_cart_img", "trace": "self = <test_113360_dWeb_empty_cart_ui_ux.TestWebSignupOnboardingUIUX object at 0x000001F6A3F4E710>\npage = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppDat...aywright\\chromium-1091\\chrome-win\\chrome.exe> version=120.0.6099.28>>, 'page': <Page url='https://www.sayweee.com/en'>}\npc_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...G8tqW0pEspxrTdq6j1sbdtjyzWAWmREj0EZuFqtk1GZBpoMGIYDZdMlCktwrD96tmorx1hxbhueWY7FCg3i5mmiTowyJ729FalX3IHyQtLVoDkPY', ...}\nlogin_trace = None\n\n    @allure.title(\"PC购物车-空购物车UI/UX验证\")\n    @pytest.mark.present\n    def test_113360_dWeb_empty_cart_ui_ux(self, page: dict, pc_autotest_header, login_trace):\n        \"\"\"\n        【113360】 PC购物车-空购物车UI/UX验证\n        \"\"\"\n        p: Page = page.get(\"page\")\n        c = page.get(\"context\")\n        cart_page = CartPage(p, pc_autotest_header, browser_context=c)\n        # 清空购物车\n        try:\n            empty_cart(self.header)\n        except Exception as e:\n            log.info(\"清空购物车发生异常\" + str(e))\n        cart_page.start_shopping()\n        # 断言空购物车img存在\n>       assert p.locator(cart_elements.ele_cart_img).is_visible()\nE       assert False\nE        +  where False = <bound method Locator.is_visible of <Locator frame=<Frame name= url='https://www.sayweee.com/en'> selector=\"//img[contains(@src,'cart_empty')]\">>()\nE        +    where <bound method Locator.is_visible of <Locator frame=<Frame name= url='https://www.sayweee.com/en'> selector=\"//img[contains(@src,'cart_empty')]\">> = <Locator frame=<Frame name= url='https://www.sayweee.com/en'> selector=\"//img[contains(@src,'cart_empty')]\">.is_visible\nE        +      where <Locator frame=<Frame name= url='https://www.sayweee.com/en'> selector=\"//img[contains(@src,'cart_empty')]\"> = <bound method Page.locator of <Page url='https://www.sayweee.com/en'>>(\"//img[contains(@src,'cart_empty')]\")\nE        +        where <bound method Page.locator of <Page url='https://www.sayweee.com/en'>> = <Page url='https://www.sayweee.com/en'>.locator\nE        +        and   \"//img[contains(@src,'cart_empty')]\" = cart_elements.ele_cart_img\n\ntest_113360_dWeb_empty_cart_ui_ux.py:32: AssertionError"}, "description": "\n        【113360】 PC购物车-空购物车UI/UX验证\n        ", "start": 1740468407722, "stop": 1740468434035, "uuid": "ec038f86-2309-46f1-b64e-3427fe1f004c", "historyId": "330a60b1c1dd5b375c18d6a4fcfadc3d", "testCaseId": "330a60b1c1dd5b375c18d6a4fcfadc3d", "fullName": "src.Dweb.EC.testcases.cart.test_113360_dWeb_empty_cart_ui_ux.TestWebSignupOnboardingUIUX#test_113360_dWeb_empty_cart_ui_ux", "labels": [{"name": "story", "value": "PC购物车-空购物车UI/UX验证"}, {"name": "tag", "value": "present"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.cart"}, {"name": "suite", "value": "test_113360_dWeb_empty_cart_ui_ux"}, {"name": "subSuite", "value": "TestWebSignupOnboardingUIUX"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "12388-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.cart.test_113360_dWeb_empty_cart_ui_ux"}]}
{"name": "PC购物车-空购物车UI/UX验证", "status": "broken", "statusDetails": {"message": "playwright._impl._errors.Error: Error: strict mode violation: get_by_test_id(\"btn-main-banner-img-0\") resolved to 3 elements:\n    1) <a data-testid=\"btn-main-banner-img-0\" aria-label=\"…>…</a> aka get_by_test_id(\"btn-main-banner-img-0\").first\n    2) <a data-testid=\"btn-main-banner-img-0\" aria-label=\"…>…</a> aka get_by_test_id(\"btn-main-banner-img-0\").nth(1)\n    3) <a data-testid=\"btn-main-banner-img-0\" aria-label=\"…>…</a> aka get_by_test_id(\"btn-main-banner-img-0\").nth(2)", "trace": "self = <test_113360_dWeb_empty_cart_ui_ux.TestWebSignupOnboardingUIUX object at 0x0000028DB2BD7B90>\npage = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppDat...aywright\\chromium-1091\\chrome-win\\chrome.exe> version=120.0.6099.28>>, 'page': <Page url='https://www.sayweee.com/en'>}\npc_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...GjuMUuYY7n_qwM3Z7zjSj5Y4XNWoWxJmzzGAAWUmmQK3nywtJzp6-_ppk8njnAVFIC6ebldPNgTxaTZMd8lbnz3_PBw0BUTaID1gRPfZnSc2wJ8w', ...}\nlogin_trace = None\n\n    @allure.title(\"PC购物车-空购物车UI/UX验证\")\n    @pytest.mark.present\n    def test_113360_dWeb_empty_cart_ui_ux(self, page: dict, pc_autotest_header, login_trace):\n        \"\"\"\n        【113360】 PC购物车-空购物车UI/UX验证\n        \"\"\"\n        p: Page = page.get(\"page\")\n        c = page.get(\"context\")\n        cart_page = CartPage(p, pc_autotest_header, browser_context=c)\n        # 点击进入购物车\n        p.get_by_test_id(\"wid-mini-cart\").click()\n        p.get_by_test_id(\"wid-direct-from-japan\").hover()\n        p.wait_for_timeout(3000)\n    \n        # 清空购物车\n        try:\n            empty_cart(self.header)\n        except Exception as e:\n            log.info(\"清空购物车发生异常\" + str(e))\n        # 断言空购物车img存在\n        assert p.locator(cart_elements.ele_cart_img).is_visible()\n        # 断言空购物车 文案存在\n        assert p.locator(cart_elements.ele_cart_text).is_visible()\n        # 点击空购物车的start_shopping按钮\n        cart_page.start_shopping()\n        # 断言跳转到了首页,判断能找到首页banner即可\n>       assert p.get_by_test_id(\"btn-main-banner-img-0\").is_visible()\n\ntest_113360_dWeb_empty_cart_ui_ux.py:42: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py:17546: in is_visible\n    self._sync(self._impl_obj.is_visible(timeout=timeout))\n..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_locator.py:499: in is_visible\n    return await self._frame.is_visible(\n..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_frame.py:348: in is_visible\n    return await self._channel.send(\"isVisible\", locals_to_params(locals()))\n..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:62: in send\n    return await self._connection.wrap_api_call(\n..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:492: in wrap_api_call\n    return await cb()\n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n\nself = <playwright._impl._connection.Channel object at 0x0000028DB40EFC10>\nmethod = 'isVisible'\nparams = {'selector': 'internal:testid=[data-testid=\"btn-main-banner-img-0\"s]', 'strict': True}\nreturn_as_dict = False\n\n    async def inner_send(\n        self, method: str, params: Optional[Dict], return_as_dict: bool\n    ) -> Any:\n        if params is None:\n            params = {}\n        callback = self._connection._send_message_to_server(\n            self._object, method, params\n        )\n        if self._connection._error:\n            error = self._connection._error\n            self._connection._error = None\n            raise error\n        done, _ = await asyncio.wait(\n            {\n                self._connection._transport.on_error_future,\n                callback.future,\n            },\n            return_when=asyncio.FIRST_COMPLETED,\n        )\n        if not callback.future.done():\n            callback.future.cancel()\n>       result = next(iter(done)).result()\nE       playwright._impl._errors.Error: Error: strict mode violation: get_by_test_id(\"btn-main-banner-img-0\") resolved to 3 elements:\nE           1) <a data-testid=\"btn-main-banner-img-0\" aria-label=\"…>…</a> aka get_by_test_id(\"btn-main-banner-img-0\").first\nE           2) <a data-testid=\"btn-main-banner-img-0\" aria-label=\"…>…</a> aka get_by_test_id(\"btn-main-banner-img-0\").nth(1)\nE           3) <a data-testid=\"btn-main-banner-img-0\" aria-label=\"…>…</a> aka get_by_test_id(\"btn-main-banner-img-0\").nth(2)\n\n..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:100: Error"}, "description": "\n        【113360】 PC购物车-空购物车UI/UX验证\n        ", "start": 1740469401390, "stop": 1740469488453, "uuid": "72ab29a0-58e5-418e-ac3f-7b632e036e75", "historyId": "330a60b1c1dd5b375c18d6a4fcfadc3d", "testCaseId": "330a60b1c1dd5b375c18d6a4fcfadc3d", "fullName": "src.Dweb.EC.testcases.cart.test_113360_dWeb_empty_cart_ui_ux.TestWebSignupOnboardingUIUX#test_113360_dWeb_empty_cart_ui_ux", "labels": [{"name": "story", "value": "PC购物车-空购物车UI/UX验证"}, {"name": "tag", "value": "present"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.cart"}, {"name": "suite", "value": "test_113360_dWeb_empty_cart_ui_ux"}, {"name": "subSuite", "value": "TestWebSignupOnboardingUIUX"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "8500-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.cart.test_113360_dWeb_empty_cart_ui_ux"}]}
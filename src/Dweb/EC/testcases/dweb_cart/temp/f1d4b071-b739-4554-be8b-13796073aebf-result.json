{"name": "PC购物车-多个购物车样式", "status": "broken", "statusDetails": {"message": "AttributeError: 'NoneType' object has no attribute 'query_selector'", "trace": "self = <test_109546_dWeb_multi_grocery_cart_ui_ux.TestDWebMultiGroceryCartUIUX object at 0x0000023437451D90>\npage = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppDat...ght\\chromium-1091\\chrome-win\\chrome.exe> version=120.0.6099.28>>, 'page': <Page url='https://www.sayweee.com/en/cart'>}\npc_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...-pbmibWj2Y2EaGxEeMwYSA15n8zC5jtQ92RG6nCiMvms2w75eKQ-ROrNznIoPjYhWNiV_GA30zGXFG0kuJmQcgn2S-G9eW0wi0OtZHm44S0RuaBo', ...}\nlogin_trace = None\n\n    @allure.title(\"PC购物车-多个购物车样式\")\n    @pytest.mark.present\n    def test_109546_dWeb_multi_grocery_cart_ui_ux(self, page: dict, pc_autotest_header, login_trace):\n        \"\"\"\n        【109546】 PC购物车-多个购物车样式\n        \"\"\"\n        p: Page = page.get(\"page\")\n        c = page.get(\"context\")\n        # 构造的购物车页面\n        cart_page = CartPage(p, pc_autotest_header, browser_context=c)\n        # 清空购物车\n        try:\n            empty_cart(pc_autotest_header)\n            p.reload()\n            p.wait_for_timeout(2000)\n        except Exception as e:\n            log.info(\"清空购物车发生异常\" + str(e))\n    \n        # 构造的分类页面\n        category_page = CategoryPage(p, pc_autotest_header, browser_context=c)\n        # 去分类页加购各种类型的商品进购物车\n        category_page.add_to_many_cart_from_category()\n        # 光标移到global+ 位置\n        p.get_by_test_id(\"wid-direct-from-japan\").hover()\n    \n        # 购物车断言\n        p.wait_for_selector(cart_elements.ele_cart_summary)\n        assert cart_page.FE.ele(cart_elements.ele_cart_summary).is_visible()\n        assert \"title\" in cart_page.FE.ele(cart_elements.ele_cart_summary).get_attribute(\"class\")\n        # 0. 判断第一个购物车是local delivery\n        assert \"Local delivery\" == cart_page.FE.ele(cart_elements.ele_cart_summary_local_delivery).text_content()\n        # 1. 判断subtotal元素存在\n        assert cart_page.FE.ele(cart_elements.ele_cart_subtatal).is_visible()\n        sub_total_fee = cart_page.FE.ele(cart_elements.ele_cart_subtatal_fee)\n        # 2. 判断subtotal值\n        assert sub_total_fee.is_visible() and \"$\" in sub_total_fee.text_content()\n    \n        # 获取所有的items total\n        items_total = cart_page.FE.eles(cart_elements.ele_cart_items_total)\n        assert items_total, f\"items_total={items_total}\"\n        # 3. 判断items_total中有美元符号存在\n        for item in items_total:\n            log.debug(\"item.text_content===>\" + item.text_content())\n            assert \"$\" in item.text_content()\n    \n        # 4. 判断delivery_fee中有美元符号存在或为free\n        delivery_fee = cart_page.FE.eles(cart_elements.ele_cart_delivery_fee)\n        for df in delivery_fee:\n            log.debug(\"delivery_fee的content===>\" + df.text_content())\n            assert \"$\" in df.text_content() or 'FREE' == df.text_content()\n    \n        # 5. 判断左侧的购物车\n        all_cart_div = cart_page.FE.eles(cart_elements.ele_cart_each_cart_div)\n        assert all_cart_div, f\"all_cart_div={all_cart_div}\"\n        for acd in all_cart_div:\n            \"//div[@data-testid='btn-atc-plus']\"\n            all_goods: List[ElementHandle] = acd.query_selector_all(cart_elements.ele_cart_products)\n            # all_goods: List[ElementHandle] = acd.query_selector_all(\"//div[contains(@class, 'GoodsInCart_goods__')]\")\n            assert all_goods, f\"购物车下的所有商品all_goods={all_goods}\"\n            for index, ag in enumerate(all_goods):\n                # 拿到每个商品卡片模块元素信息\n                ag_card = ag.query_selector(cart_elements.ele_cart_products)\n                # 查到购物车商品卡片上加/减元素部分\n>               goods_in_cart_price_action = ag_card.query_selector(u\"//div[contains(@class, 'GoodsInCart_priceAction')]\")\nE               AttributeError: 'NoneType' object has no attribute 'query_selector'\n\ntest_109546_dWeb_multi_grocery_cart_ui_ux.py:82: AttributeError"}, "description": "\n        【109546】 PC购物车-多个购物车样式\n        ", "start": 1741081913426, "stop": 1741082074811, "uuid": "*************-40c1-9dd1-1ee0216cdd8f", "historyId": "6adf790d4dfdedef5d1582fde0b092dc", "testCaseId": "6adf790d4dfdedef5d1582fde0b092dc", "fullName": "src.Dweb.EC.testcases.cart.test_109546_dWeb_multi_grocery_cart_ui_ux.TestDWebMultiGroceryCartUIUX#test_109546_dWeb_multi_grocery_cart_ui_ux", "labels": [{"name": "story", "value": "PC购物车-多个购物车样式"}, {"name": "tag", "value": "present"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.cart"}, {"name": "suite", "value": "test_109546_dWeb_multi_grocery_cart_ui_ux"}, {"name": "subSuite", "value": "TestDWebMultiGroceryCartUIUX"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "27608-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.cart.test_109546_dWeb_multi_grocery_cart_ui_ux"}]}
{"name": "PC购物车-单个生鲜购物车样式", "status": "broken", "statusDetails": {"message": "TypeError: object of type 'ElementHandle' has no len()", "trace": "self = <test_109549_dWeb_single_grocery_cart_ui_ux.TestDWebSingleGroceryCartUIUX object at 0x0000026D8C940410>\npage = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppDat...ght\\chromium-1091\\chrome-win\\chrome.exe> version=120.0.6099.28>>, 'page': <Page url='https://www.sayweee.com/en/cart'>}\npc_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...0p_XG11MW-wvrYFDpHjVbMTbCyKdIl-cQXK9OzUWpQ99v4i2dLmUaimiLqbIf72dXemlzxdD83mypBJxvGQYYCr_lrF9T9-m5aNwDhR2kjeSn0DY', ...}\nlogin_trace = None\n\n    @allure.title(\"PC购物车-单个生鲜购物车样式\")\n    @pytest.mark.present\n    def test_109549_dWeb_single_grocery_cart_ui_ux(self, page: dict, pc_autotest_header, login_trace):\n        \"\"\"\n        【109549】 PC购物车-单个生鲜购物车样式\n        \"\"\"\n        p: Page = page.get(\"page\")\n        c = page.get(\"context\")\n        # 构造的购物车页面\n        cart_page = CartPage(p, pc_autotest_header, browser_context=c)\n        # 清空购物车\n        try:\n            empty_cart(pc_autotest_header)\n            # 清空购物车之后刷新页面\n            p.reload()\n            p.wait_for_timeout(2000)\n        except Exception as e:\n            log.info(\"清空购物车发生异常\" + str(e))\n    \n        # 构造的分类页面\n        category_page = CategoryPage(p, pc_autotest_header, browser_context=c)\n        # 去分类页加购Local类型的商品进购物车\n        category_page.add_to_local_product_cart_from_category()\n        # 去购物车结算\n        category_page.go_to_cart_from_category()\n        # 光标移到global+ 位置\n        p.get_by_test_id(\"wid-direct-from-japan\").hover()\n    \n        # 购物车断言\n        p.wait_for_selector(cart_elements.ele_cart_summary)\n        assert cart_page.FE.ele(cart_elements.ele_cart_summary).is_visible()\n        assert \"title\" in cart_page.FE.ele(cart_elements.ele_cart_summary).get_attribute(\"class\")\n        # 判断只有一个购物车\n>       assert len(cart_page.FE.ele(cart_elements.ele_cart_summary_list)) ==1 and cart_page.FE.ele(cart_elements.ele_cart_summary_list)\nE       TypeError: object of type 'ElementHandle' has no len()\n\ntest_109549_dWeb_single_grocery_cart_ui_ux.py:54: TypeError"}, "description": "\n        【109549】 PC购物车-单个生鲜购物车样式\n        ", "start": 1741153308082, "stop": 1741153362634, "uuid": "12c3d2ba-90a2-43a1-bc84-c2cd1fc4d748", "historyId": "a23054b4fa7b25eaf5ecb5d6836ec69b", "testCaseId": "a23054b4fa7b25eaf5ecb5d6836ec69b", "fullName": "src.Dweb.EC.testcases.cart.test_109549_dWeb_single_grocery_cart_ui_ux.TestDWebSingleGroceryCartUIUX#test_109549_dWeb_single_grocery_cart_ui_ux", "labels": [{"name": "story", "value": "PC购物车-单个生鲜购物车样式"}, {"name": "tag", "value": "present"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.cart"}, {"name": "suite", "value": "test_109549_dWeb_single_grocery_cart_ui_ux"}, {"name": "subSuite", "value": "TestDWebSingleGroceryCartUIUX"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "5164-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.cart.test_109549_dWeb_single_grocery_cart_ui_ux"}]}
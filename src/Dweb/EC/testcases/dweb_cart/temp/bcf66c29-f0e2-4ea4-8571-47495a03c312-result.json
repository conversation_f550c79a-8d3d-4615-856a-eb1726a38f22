{"name": "PC购物车-多个购物车样式", "status": "broken", "statusDetails": {"message": "AttributeError: 'NoneType' object has no attribute 'click'", "trace": "self = <test_109546_dWeb_multi_grocery_cart_ui_ux.TestDWebMultiGroceryCartUIUX object at 0x000002499D034CD0>\npage = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppDat...e.exe> version=120.0.6099.28>>, 'page': <Page url='https://www.sayweee.com/en/category/sale?filter_sub_category=sale'>}\npc_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...QzTwKb5QM-yhYZoaUGFVeftDPptwjXIy6wq_BQqnVuXnohYeuLf3QjPhvBUoMq3LbYHTsMEHk_4KySnJIU7Y2lSW7ltGlf_4FUh2TXo0MnMhrIcM', ...}\nlogin_trace = None\n\n    @allure.title(\"PC购物车-多个购物车样式\")\n    @pytest.mark.present\n    def test_109546_dWeb_multi_grocery_cart_ui_ux(self, page: dict, pc_autotest_header, login_trace):\n        \"\"\"\n        【109546】 PC购物车-多个购物车样式\n        \"\"\"\n        p: Page = page.get(\"page\")\n        c = page.get(\"context\")\n        # 构造的购物车页面\n        cart_page = CartPage(p, pc_autotest_header, browser_context=c)\n        # # 点击进入购物车\n        # p.get_by_test_id(\"wid-mini-cart\").click()\n        # p.get_by_test_id(\"wid-direct-from-japan\").hover()\n        # p.wait_for_timeout(3000)\n        # 清空购物车\n        try:\n            empty_cart(pc_autotest_header)\n        except Exception as e:\n            log.info(\"清空购物车发生异常\" + str(e))\n        # 构造的分类页面\n        category_page = CategoryPage(p, pc_autotest_header, browser_context=c)\n        # 去分类页加购各种类型的商品进购物车\n>       category_page.add_to_many_cart_from_category()\n\ntest_109546_dWeb_multi_grocery_cart_ui_ux.py:41: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n..\\..\\dweb_pages\\page_category\\page_category.py:72: in add_to_many_cart_from_category\n    self.category_filter_delivery_type(delivery_type_local)\n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n\nself = <src.Dweb.EC.dweb_pages.page_category.page_category.CategoryPage object at 0x000002499D081950>\nfilter_delivery_type = <Locator frame=<Frame name= url='https://www.sayweee.com/en/category/sale?filter_sub_category=sale'> selector='internal:testid=[data-testid=\"btn-delivery_type-delivery_type_local\"s]'>\n\n    def category_filter_delivery_type(self, filter_delivery_type):\n        \"\"\" 分类页点击勾选filter:Delivery type \"\"\"\n>       self.FE.ele(filter_delivery_type).click()\nE       AttributeError: 'NoneType' object has no attribute 'click'\n\n..\\..\\dweb_pages\\page_category\\page_category.py:44: AttributeError"}, "description": "\n        【109546】 PC购物车-多个购物车样式\n        ", "start": 1741070407774, "stop": 1741070446723, "uuid": "23cf6cf4-a47c-4633-a6c6-403f07345431", "historyId": "6adf790d4dfdedef5d1582fde0b092dc", "testCaseId": "6adf790d4dfdedef5d1582fde0b092dc", "fullName": "src.Dweb.EC.testcases.cart.test_109546_dWeb_multi_grocery_cart_ui_ux.TestDWebMultiGroceryCartUIUX#test_109546_dWeb_multi_grocery_cart_ui_ux", "labels": [{"name": "story", "value": "PC购物车-多个购物车样式"}, {"name": "tag", "value": "present"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.cart"}, {"name": "suite", "value": "test_109546_dWeb_multi_grocery_cart_ui_ux"}, {"name": "subSuite", "value": "TestDWebMultiGroceryCartUIUX"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "1476-Main<PERSON><PERSON><PERSON>"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.cart.test_109546_dWeb_multi_grocery_cart_ui_ux"}]}
{"name": "【109546】 PC购物车-多个购物车样式", "status": "passed", "description": "\n        【109546】 PC购物车-多个购物车样式\n        ", "start": 1741683239731, "stop": 1741683335513, "uuid": "64de9c85-8bba-4c41-aa8d-4502e7ae6b23", "historyId": "6adf790d4dfdedef5d1582fde0b092dc", "testCaseId": "6adf790d4dfdedef5d1582fde0b092dc", "fullName": "src.Dweb.EC.testcases.cart.test_109546_dWeb_multi_grocery_cart_ui_ux.TestDWebMultiGroceryCartUIUX#test_109546_dWeb_multi_grocery_cart_ui_ux", "labels": [{"name": "story", "value": "【109546】 PC购物车-多个购物车样式"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "pccart"}, {"name": "tag", "value": "transaction"}, {"name": "tag", "value": "suqin"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.cart"}, {"name": "suite", "value": "test_109546_dWeb_multi_grocery_cart_ui_ux"}, {"name": "subSuite", "value": "TestDWebMultiGroceryCartUIUX"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "560-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.cart.test_109546_dWeb_multi_grocery_cart_ui_ux"}]}
{"name": "PC购物车-空购物车UI/UX验证", "status": "failed", "statusDetails": {"message": "AssertionError: assert False\n +  where False = <bound method Locator.is_visible of <Locator frame=<Frame name= url='https://www.sayweee.com/en/cart'> selector=\"//img[contains(@src,'cart_empty')]\">>()\n +    where <bound method Locator.is_visible of <Locator frame=<Frame name= url='https://www.sayweee.com/en/cart'> selector=\"//img[contains(@src,'cart_empty')]\">> = <Locator frame=<Frame name= url='https://www.sayweee.com/en/cart'> selector=\"//img[contains(@src,'cart_empty')]\">.is_visible\n +      where <Locator frame=<Frame name= url='https://www.sayweee.com/en/cart'> selector=\"//img[contains(@src,'cart_empty')]\"> = <bound method Page.locator of <Page url='https://www.sayweee.com/en/cart'>>(\"//img[contains(@src,'cart_empty')]\")\n +        where <bound method Page.locator of <Page url='https://www.sayweee.com/en/cart'>> = <Page url='https://www.sayweee.com/en/cart'>.locator\n +        and   \"//img[contains(@src,'cart_empty')]\" = dweb_cart_ele.ele_cart_img", "trace": "self = <test_113360_dWeb_empty_cart_ui_ux.TestDWebEmptyCartUIUX object at 0x000001C532579750>\npage = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppDat...ght\\chromium-1091\\chrome-win\\chrome.exe> version=120.0.6099.28>>, 'page': <Page url='https://www.sayweee.com/en/cart'>}\npc_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...bDstDLzFtZizF88uGGWNIkJ3VTGnKRDKqeqxozbGa2i2-oFcDCFuOzUfpw3hxM0SqPCoWYzgvwMW89jEkRWRdBrDe_FKaJQwcYH3c5D5WPoSC7ko', ...}\nlogin_trace = None\n\n    @allure.title(\"PC购物车-空购物车UI/UX验证\")\n    def test_113360_dWeb_empty_cart_ui_ux(self, page: dict, pc_autotest_header, login_trace):\n        \"\"\"\n        【113360】 PC购物车-空购物车UI/UX验证\n        \"\"\"\n        p: Page = page.get(\"page\")\n        c = page.get(\"context\")\n        cart_page = CartPage(p, pc_autotest_header, browser_context=c, page_url=\"/cart\")\n        # # 点击进入购物车\n        # p.get_by_test_id(\"wid-mini-cart\").click()\n        # p.get_by_test_id(\"wid-direct-from-japan\").hover()\n        # p.wait_for_timeout(3000)\n    \n        # 清空购物车\n        try:\n            empty_cart(self.header)\n        except Exception as e:\n            log.info(\"清空购物车发生异常\" + str(e))\n        # 刷新页面\n        p.reload()\n        # 断言空购物车img存在\n>       assert p.locator(dweb_cart_ele.ele_cart_img).is_visible()\nE       assert False\nE        +  where False = <bound method Locator.is_visible of <Locator frame=<Frame name= url='https://www.sayweee.com/en/cart'> selector=\"//img[contains(@src,'cart_empty')]\">>()\nE        +    where <bound method Locator.is_visible of <Locator frame=<Frame name= url='https://www.sayweee.com/en/cart'> selector=\"//img[contains(@src,'cart_empty')]\">> = <Locator frame=<Frame name= url='https://www.sayweee.com/en/cart'> selector=\"//img[contains(@src,'cart_empty')]\">.is_visible\nE        +      where <Locator frame=<Frame name= url='https://www.sayweee.com/en/cart'> selector=\"//img[contains(@src,'cart_empty')]\"> = <bound method Page.locator of <Page url='https://www.sayweee.com/en/cart'>>(\"//img[contains(@src,'cart_empty')]\")\nE        +        where <bound method Page.locator of <Page url='https://www.sayweee.com/en/cart'>> = <Page url='https://www.sayweee.com/en/cart'>.locator\nE        +        and   \"//img[contains(@src,'cart_empty')]\" = dweb_cart_ele.ele_cart_img\n\ntest_113360_dWeb_empty_cart_ui_ux.py:36: AssertionError"}, "description": "\n        【113360】 PC购物车-空购物车UI/UX验证\n        ", "start": 1743141731543, "stop": 1743141755204, "uuid": "5733645a-36bb-42e1-813a-b3406bb17277", "historyId": "7d9aebe8791734c5fc119ff1c2a50334", "testCaseId": "7d9aebe8791734c5fc119ff1c2a50334", "fullName": "src.Dweb.EC.testcases.dweb_cart.test_113360_dWeb_empty_cart_ui_ux.TestDWebEmptyCartUIUX#test_113360_dWeb_empty_cart_ui_ux", "labels": [{"name": "story", "value": "PC购物车-空购物车UI/UX验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "pccart"}, {"name": "tag", "value": "transaction"}, {"name": "tag", "value": "suqin"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.dweb_cart"}, {"name": "suite", "value": "test_113360_dWeb_empty_cart_ui_ux"}, {"name": "subSuite", "value": "TestDWebEmptyCartUIUX"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "12912-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.dweb_cart.test_113360_dWeb_empty_cart_ui_ux"}]}
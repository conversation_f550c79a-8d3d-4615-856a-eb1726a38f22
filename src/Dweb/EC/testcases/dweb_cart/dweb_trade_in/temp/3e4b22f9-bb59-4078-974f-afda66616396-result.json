{"name": "PC购物车-金额小于$35时显示免运费banner验证", "status": "broken", "statusDetails": {"message": "playwright._impl._errors.TimeoutError: Timeout 50000ms exceeded.", "trace": "self = <dweb_trade_in.test_107320_dWeb_trade_in_low_35_ui_ux_v3.TestDWebEmptyCartUIUX object at 0x000002060532AD50>\npage = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppDat...ght\\chromium-1091\\chrome-win\\chrome.exe> version=120.0.6099.28>>, 'page': <Page url='https://www.sayweee.com/en/cart'>}\npc_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...BBYBF-w0VMgLSCv3BoCx_53Ef2Q8IRDrkl3VsIb9N-MhqJnySfbQ3UOXdm7X_qYl3p1eCsye2HKDYsbnPtuw0VYVL6FItt-R9TN9xHpYKVSAEpJs', ...}\nlogin_trace = None\n\n    @allure.title(\"PC购物车-金额小于$35时显示免运费banner验证\")\n    def test_107320_dWeb_trade_in_low_35_ui_ux_v3(self, page: dict, pc_autotest_header, login_trace):\n        \"\"\"\n        [107320][dWeb]-金额小于$35时显示免运费banner验证\n        测试步骤：\n        1、访问https://www.sayweee.com/en/cart购物车页面，\n        2、清除购物车\n        3、滚动到购物车底部推荐模块 ele_cart_recommendations\n        4、加购推荐商品不超过35 ele_recommend_module_card 不能超过35\n        5、判断购物车商品金额ele_cart_normal_item_total小于$35\n        6、会显示免运费banner 入口\n        \"\"\"\n        p: Page = page.get(\"page\")\n        c = page.get(\"context\")\n        cart_page = DWebCartPage(p, pc_autotest_header, browser_context=c, page_url=\"/cart\")\n        trade_in_page = DWebTradeInPage(p, pc_autotest_header)\n    \n        # 1-2. 访问购物车页面并清除购物车\n        try:\n            empty_cart(pc_autotest_header)\n            p.reload()\n            p.wait_for_timeout(2000)\n            log.info(\"清空购物车成功\")\n        except Exception as e:\n            log.info(f\"清空购物车发生异常: {str(e)}\")\n    \n        # 断言空购物车状态\n        assert cart_page.is_empty_cart(), \"购物车不为空\"\n        log.info(\"验证空购物车状态成功\")\n    \n        # 3. 滚动到购物车底部推荐模块\n>       cart_page.scroll_to_recommendations()\n\ntest_107320_dWeb_trade_in_low_35_ui_ux_v3.py:47: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n..\\..\\..\\dweb_pages\\dweb_page_cart\\dweb_page_cart.py:142: in scroll_to_recommendations\n    self.page.locator(dweb_cart_ele.ele_recommend_module).scroll_into_view_if_needed()\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py:17744: in scroll_into_view_if_needed\n    self._sync(self._impl_obj.scroll_into_view_if_needed(timeout=timeout))\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_locator.py:539: in scroll_into_view_if_needed\n    return await self._with_element(\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_locator.py:114: in _with_element\n    handle = await self.element_handle(timeout=timeout)\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_locator.py:314: in element_handle\n    handle = await self._frame.wait_for_selector(\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_frame.py:317: in wait_for_selector\n    await self._channel.send(\"waitForSelector\", locals_to_params(locals()))\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:62: in send\n    return await self._connection.wrap_api_call(\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:492: in wrap_api_call\n    return await cb()\n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n\nself = <playwright._impl._connection.Channel object at 0x0000020605E70A10>\nmethod = 'waitForSelector'\nparams = {'selector': \"//div[contains(@class,'cart-v2_recommends')]\", 'state': 'attached', 'strict': True, 'timeout': 50000}\nreturn_as_dict = False\n\n    async def inner_send(\n        self, method: str, params: Optional[Dict], return_as_dict: bool\n    ) -> Any:\n        if params is None:\n            params = {}\n        callback = self._connection._send_message_to_server(\n            self._object, method, params\n        )\n        if self._connection._error:\n            error = self._connection._error\n            self._connection._error = None\n            raise error\n        done, _ = await asyncio.wait(\n            {\n                self._connection._transport.on_error_future,\n                callback.future,\n            },\n            return_when=asyncio.FIRST_COMPLETED,\n        )\n        if not callback.future.done():\n            callback.future.cancel()\n>       result = next(iter(done)).result()\nE       playwright._impl._errors.TimeoutError: Timeout 50000ms exceeded.\n\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:100: TimeoutError"}, "description": "\n        [107320][dWeb]-金额小于$35时显示免运费banner验证\n        测试步骤：\n        1、访问https://www.sayweee.com/en/cart购物车页面，\n        2、清除购物车\n        3、滚动到购物车底部推荐模块 ele_cart_recommendations\n        4、加购推荐商品不超过35 ele_recommend_module_card 不能超过35\n        5、判断购物车商品金额ele_cart_normal_item_total小于$35\n        6、会显示免运费banner 入口\n        ", "start": 1749969903920, "stop": 1749969987390, "uuid": "355adee7-f462-4054-9e78-fc2d9bfceadf", "historyId": "ea5d0b08b72d359561022dbcc8a4698c", "testCaseId": "ea5d0b08b72d359561022dbcc8a4698c", "fullName": "src.Dweb.EC.testcases.dweb_cart.dweb_trade_in.test_107320_dWeb_trade_in_low_35_ui_ux_v3.TestDWebEmptyCartUIUX#test_107320_dWeb_trade_in_low_35_ui_ux_v3", "labels": [{"name": "story", "value": "PC购物车-金额小于$35时显示免运费banner验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "pccart"}, {"name": "tag", "value": "toddo"}, {"name": "tag", "value": "transaction"}, {"name": "tag", "value": "suqin"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.dweb_cart.dweb_trade_in"}, {"name": "suite", "value": "test_107320_dWeb_trade_in_low_35_ui_ux_v3"}, {"name": "subSuite", "value": "TestDWebEmptyCartUIUX"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "27512-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.dweb_cart.dweb_trade_in.test_107320_dWeb_trade_in_low_35_ui_ux_v3"}]}
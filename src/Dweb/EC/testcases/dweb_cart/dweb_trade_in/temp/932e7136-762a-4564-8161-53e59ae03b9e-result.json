{"name": "PC购物车-金额小于$35时显示免运费banner验证", "status": "passed", "description": "\n        [107320][dWeb]-金额小于$35时显示免运费banner验证\n        测试步骤：\n        1、访问https://www.sayweee.com/en/cart购物车页面，\n        2、清除购物车\n        3、滚动到购物车底部推荐模块 ele_cart_recommendations\n        4、加购推荐商品不超过35 ele_recommend_module_card 不能超过35\n        5、判断购物车商品金额ele_cart_normal_item_total小于$35\n        6、会显示免运费banner 入口\n        ", "start": 1749974439830, "stop": 1749974486972, "uuid": "97b8b3e1-df42-48f9-830d-8fc0eaac9c32", "historyId": "cc7ab1c5267b55e45e0461db800eca10", "testCaseId": "cc7ab1c5267b55e45e0461db800eca10", "fullName": "src.Dweb.EC.testcases.dweb_cart.dweb_trade_in.test_107320_dWeb_trade_in_35_x_68_ui_ux_v3.TestDWebEmptyCartUIUX#test_107320_dWeb_trade_in_low_35_ui_ux_v3", "labels": [{"name": "story", "value": "PC购物车-金额小于$35时显示免运费banner验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "pccart"}, {"name": "tag", "value": "toddo"}, {"name": "tag", "value": "transaction"}, {"name": "tag", "value": "suqin"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.dweb_cart.dweb_trade_in"}, {"name": "suite", "value": "test_107320_dWeb_trade_in_35_x_68_ui_ux_v3"}, {"name": "subSuite", "value": "TestDWebEmptyCartUIUX"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "17256-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.dweb_cart.dweb_trade_in.test_107320_dWeb_trade_in_35_x_68_ui_ux_v3"}]}
{"name": "PC购物车-对满68换购列表加购验证", "status": "passed", "description": "\n        [107320][dWeb]-对满68换购列表加购验证\n        测试步骤：\n        1、访问https://www.sayweee.com/en/cart购物车页面，\n        2、清除购物车\n        3、滚动到购物车底部推荐模块 ele_cart_recommendations\n        4、加购推荐商品 ele_recommend_module_card\n        5、判断购物车商品金额ele_cart_normal_item_total\n        6、如果ele_cart_normal_item_total小于$35,会显示免运费banner 入口\n        7、如果ele_cart_normal_item_total加购大于35小于68，显示换购入口banner，\n        8、此时banner文案为$x away from unlocking more discounts!\n        9、点击换购入口ele_cart_trade_in_normal，右侧弹出换购列表ele_trade_in\n        10、此时换购商品ele_trade_in_products不可加购\n        11、关闭右侧的换购ele_trade_in_close\n        12、回到购物车滚动到购物车底部推荐模块 ele_cart_recommendations，\n        13、继续加购推荐商品ele_recommend_module_card\n        14、当购物车金额大于68时，换购入口banner 文案替换为：You have selected 0/5 special deals!\n        15、点击换购入口，右侧再次弹出换购列表ele_trade_in，\n        16、此时换购商品ele_trade_in_products可加购\n        17、加购换购商品ele_trade_in_products，商品会进入购物车\n        18、注意，换购商品最多可支持5件，加购5件之后不可再加购，\n        19、回到购物车，文案会更新为You have selected 5/5 special deals!\n        20、在购物车里删除换购，当金额不足68 时，换购文案会回到：$x away from unlocking more discounts!\n        ", "start": 1749965128789, "stop": 1749968656362, "uuid": "2b0e8d9f-b022-4426-949f-542b24c21869", "testCaseId": "8648a796470ce9e852b0a5f6690fc4f4", "fullName": "src.Dweb.EC.testcases.dweb_cart.dweb_trade_in.test_107320_dWeb_trade_in_35_x_68_ui_ux_v2.TestDWebEmptyCartUIUX#test_107320_dWeb_trade_in_35_x_68_ui_ux_v2"}
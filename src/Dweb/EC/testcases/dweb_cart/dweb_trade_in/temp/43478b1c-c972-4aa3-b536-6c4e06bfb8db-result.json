{"name": "PC购物车-对满68换购列表加购验证", "status": "failed", "statusDetails": {"message": "AssertionError: 换购入口banner文案不正确\nassert 'away from unlocking more discounts' in 'Add $30.51 for FREE SHIPPINGShop more'", "trace": "self = <dweb_trade_in.test_107320_dWeb_trade_in_35_x_68_ui_ux_v2.TestDWebEmptyCartUIUX object at 0x00000261905D6C10>\npage = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppDat...ght\\chromium-1091\\chrome-win\\chrome.exe> version=120.0.6099.28>>, 'page': <Page url='https://www.sayweee.com/en/cart'>}\npc_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...emtXWtgTL39sxIaXo2_gvVWR4VAbtYoUDyH0UYp8ss2X7j6iTkaSDTIHS4ZzF64G4BDWp8Y9TccyOwlQEWAzc8LZLU6wvu3SrfhWjafJvvHNxaQI', ...}\nlogin_trace = None\n\n    @allure.title(\"PC购物车-对满68换购列表加购验证\")\n    def test_107320_dWeb_trade_in_35_x_68_ui_ux_v2(self, page: dict, pc_autotest_header, login_trace):\n        \"\"\"\n        [107320][dWeb]-对满68换购列表加购验证\n        测试步骤：\n        1、访问https://www.sayweee.com/en/cart购物车页面，\n        2、清除购物车\n        3、滚动到购物车底部推荐模块 ele_cart_recommendations\n        4、加购推荐商品 ele_recommend_module_card\n        5、判断购物车商品金额ele_cart_normal_item_total\n        6、如果ele_cart_normal_item_total小于$35,会显示免运费banner 入口\n        7、如果ele_cart_normal_item_total加购大于35小于68，显示换购入口banner，\n        8、此时banner文案为$x away from unlocking more discounts!\n        9、点击换购入口ele_cart_trade_in_normal，右侧弹出换购列表ele_trade_in\n        10、此时换购商品ele_trade_in_products不可加购\n        11、关闭右侧的换购ele_trade_in_close\n        12、回到购物车滚动到购物车底部推荐模块 ele_cart_recommendations，\n        13、继续加购推荐商品ele_recommend_module_card\n        14、当购物车金额大于68时，换购入口banner 文案替换为：You have selected 0/5 special deals!\n        15、点击换购入口，右侧再次弹出换购列表ele_trade_in，\n        16、此时换购商品ele_trade_in_products可加购\n        17、加购换购商品ele_trade_in_products，商品会进入购物车\n        18、注意，换购商品最多可支持5件，加购5件之后不可再加购，\n        19、回到购物车，文案会更新为You have selected 5/5 special deals!\n        20、在购物车里删除换购，当金额不足68 时，换购文案会回到：$x away from unlocking more discounts!\n        \"\"\"\n        p: Page = page.get(\"page\")\n        c = page.get(\"context\")\n        cart_page = DWebCartPage(p, pc_autotest_header, browser_context=c, page_url=\"/cart\")\n        trade_in_page = DWebTradeInPage(p, pc_autotest_header)\n    \n        # 1-2. 访问购物车页面并清除购物车\n        try:\n            empty_cart(pc_autotest_header)\n            p.reload()\n            p.wait_for_timeout(2000)\n            log.info(\"清空购物车成功\")\n        except Exception as e:\n            log.info(f\"清空购物车发生异常: {str(e)}\")\n    \n        # 断言空购物车状态\n        assert cart_page.is_empty_cart(), \"购物车不为空\"\n        log.info(\"验证空购物车状态成功\")\n    \n        # 3-4. 滚动到购物车底部 推荐模块并加购推荐商品\n        cart_page.scroll_to_recommendations()\n        # 加购推荐商品\n        assert cart_page.add_recommendation_product(), \"加购推荐商品失败\"\n    \n        # 5. 判断购物车商品金额\n        cart_amount = cart_page.get_normal_cart_amount()\n        log.info(f\"当前购物车金额: ${cart_amount}\")\n    \n        # 6. 如果金额小于$35，验证免运费banner\n        if cart_amount < 35:\n            assert cart_page.is_free_shipping_banner_visible(), \"购物车金额小于$35时未显示免运费banner\"\n            banner_text = cart_page.get_free_shipping_banner_text()\n            assert \"$35\" in banner_text, \"免运费banner文案不包含$35\"\n            log.info(f\"验证购物车金额小于$35时显示免运费banner: {banner_text}\")\n    \n            # 继续加购商品直到金额大于$35\n            cart_amount = cart_page.add_to_cart_until_target_amount(35)\n    \n        # 7-8. 验证$35≤金额<$68时的换购入口banner\n        # 如果金额已经超过$68，需要减少购物车商品\n        while cart_amount >= 68:\n            cart_page.remove_cart_item(0)\n            cart_amount = cart_page.get_cart_amount()\n            log.info(f\"移除商品后，当前购物车金额: ${cart_amount}\")\n    \n        # 如果金额小于$35，继续加购\n        if cart_amount < 35:\n            cart_amount = cart_page.add_to_cart_until_target_amount(35)\n    \n        # 验证换购入口banner文案\n        assert trade_in_page.is_trade_in_banner_visible(), \"购物车金额在$35-$68之间时未显示换购入口banner\"\n        banner_text = trade_in_page.get_trade_in_banner_text()\n>       assert \"away from unlocking more discounts\" in banner_text, \"换购入口banner文案不正确\"\nE       AssertionError: 换购入口banner文案不正确\nE       assert 'away from unlocking more discounts' in 'Add $30.51 for FREE SHIPPINGShop more'\n\ntest_107320_dWeb_trade_in_35_x_68_ui_ux_v2.py:93: AssertionError"}, "description": "\n        [107320][dWeb]-对满68换购列表加购验证\n        测试步骤：\n        1、访问https://www.sayweee.com/en/cart购物车页面，\n        2、清除购物车\n        3、滚动到购物车底部推荐模块 ele_cart_recommendations\n        4、加购推荐商品 ele_recommend_module_card\n        5、判断购物车商品金额ele_cart_normal_item_total\n        6、如果ele_cart_normal_item_total小于$35,会显示免运费banner 入口\n        7、如果ele_cart_normal_item_total加购大于35小于68，显示换购入口banner，\n        8、此时banner文案为$x away from unlocking more discounts!\n        9、点击换购入口ele_cart_trade_in_normal，右侧弹出换购列表ele_trade_in\n        10、此时换购商品ele_trade_in_products不可加购\n        11、关闭右侧的换购ele_trade_in_close\n        12、回到购物车滚动到购物车底部推荐模块 ele_cart_recommendations，\n        13、继续加购推荐商品ele_recommend_module_card\n        14、当购物车金额大于68时，换购入口banner 文案替换为：You have selected 0/5 special deals!\n        15、点击换购入口，右侧再次弹出换购列表ele_trade_in，\n        16、此时换购商品ele_trade_in_products可加购\n        17、加购换购商品ele_trade_in_products，商品会进入购物车\n        18、注意，换购商品最多可支持5件，加购5件之后不可再加购，\n        19、回到购物车，文案会更新为You have selected 5/5 special deals!\n        20、在购物车里删除换购，当金额不足68 时，换购文案会回到：$x away from unlocking more discounts!\n        ", "start": 1749968681459, "stop": 1749968766897, "uuid": "dc1a112a-7c26-4b67-860d-f92eda296dea", "historyId": "8648a796470ce9e852b0a5f6690fc4f4", "testCaseId": "8648a796470ce9e852b0a5f6690fc4f4", "fullName": "src.Dweb.EC.testcases.dweb_cart.dweb_trade_in.test_107320_dWeb_trade_in_35_x_68_ui_ux_v2.TestDWebEmptyCartUIUX#test_107320_dWeb_trade_in_35_x_68_ui_ux_v2", "labels": [{"name": "story", "value": "PC购物车-对满68换购列表加购验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "pccart"}, {"name": "tag", "value": "toddo"}, {"name": "tag", "value": "transaction"}, {"name": "tag", "value": "suqin"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.dweb_cart.dweb_trade_in"}, {"name": "suite", "value": "test_107320_dWeb_trade_in_35_x_68_ui_ux_v2"}, {"name": "subSuite", "value": "TestDWebEmptyCartUIUX"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "27208-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.dweb_cart.dweb_trade_in.test_107320_dWeb_trade_in_35_x_68_ui_ux_v2"}]}
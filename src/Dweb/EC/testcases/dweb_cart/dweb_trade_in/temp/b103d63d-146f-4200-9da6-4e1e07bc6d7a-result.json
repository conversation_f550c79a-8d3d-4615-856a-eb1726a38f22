{"name": "PC购物车-对满68换购列表加购验证", "status": "broken", "statusDetails": {"message": "playwright._impl._errors.TimeoutError: Timeout 50000ms exceeded.", "trace": "self = <dweb_trade_in.test_107320_dWeb_trade_in_35_x_68_ui_ux_v2.TestDWebEmptyCartUIUX object at 0x000001E8EBE09850>\npage = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppDat...ght\\chromium-1091\\chrome-win\\chrome.exe> version=120.0.6099.28>>, 'page': <Page url='https://www.sayweee.com/en/cart'>}\npc_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...Uf9LLCQOgRDCH96Pijb1j7gTsUDjEW744doRFCYrnW-DY4Q-PwFNKg_jE46z9iVPwi2nClzvOGWNBIINnytlfL4lmPQQ5054-mtRdleI_5S2c_KI', ...}\nlogin_trace = None\n\n    @allure.title(\"PC购物车-对满68换购列表加购验证\")\n    def test_107320_dWeb_trade_in_35_x_68_ui_ux_v2(self, page: dict, pc_autotest_header, login_trace):\n        \"\"\"\n        [107320][dWeb]-对满68换购列表加购验证\n        测试步骤：\n        1、访问https://www.sayweee.com/en/cart购物车页面，\n        2、清除购物车\n        3、滚动到购物车底部推荐模块 ele_cart_recommendations\n        4、加购推荐商品 ele_recommend_module_card\n        5、判断购物车商品金额ele_cart_normal_item_total\n        6、如果ele_cart_normal_item_total小于$35,会显示免运费banner 入口\n        7、如果ele_cart_normal_item_total加购大于35小于68，显示换购入口banner，\n        8、此时banner文案为$x away from unlocking more discounts!\n        9、点击换购入口ele_cart_trade_in_normal，右侧弹出换购列表ele_trade_in\n        10、此时换购商品ele_trade_in_products不可加购\n        11、关闭右侧的换购ele_trade_in_close\n        12、回到购物车滚动到购物车底部推荐模块 ele_cart_recommendations，\n        13、继续加购推荐商品ele_recommend_module_card\n        14、当购物车金额大于68时，换购入口banner 文案替换为：You have selected 0/5 special deals!\n        15、点击换购入口，右侧再次弹出换购列表ele_trade_in，\n        16、此时换购商品ele_trade_in_products可加购\n        17、加购换购商品ele_trade_in_products，商品会进入购物车\n        18、注意，换购商品最多可支持5件，加购5件之后不可再加购，\n        19、回到购物车，文案会更新为You have selected 5/5 special deals!\n        20、在购物车里删除换购，当金额不足68 时，换购文案会回到：$x away from unlocking more discounts!\n        \"\"\"\n        p: Page = page.get(\"page\")\n        c = page.get(\"context\")\n        cart_page = DWebCartPage(p, pc_autotest_header, browser_context=c, page_url=\"/cart\")\n        trade_in_page = DWebTradeInPage(p, pc_autotest_header)\n    \n        # 1-2. 访问购物车页面并清除购物车\n        try:\n            empty_cart(pc_autotest_header)\n            p.reload()\n            p.wait_for_timeout(2000)\n            log.info(\"清空购物车成功\")\n        except Exception as e:\n            log.info(f\"清空购物车发生异常: {str(e)}\")\n    \n        # 断言空购物车状态\n        assert cart_page.is_empty_cart(), \"购物车不为空\"\n        log.info(\"验证空购物车状态成功\")\n    \n        # 3-4. 滚动到购物车底部 推荐模块并加购推荐商品\n        cart_page.scroll_to_recommendations()\n        # 加购推荐商品\n>       assert cart_page.add_recommendation_product(), \"加购推荐商品失败\"\n\ntest_107320_dWeb_trade_in_35_x_68_ui_ux_v2.py:63: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n..\\..\\..\\dweb_pages\\dweb_page_cart\\dweb_page_cart.py:169: in add_recommendation_product\n    products[index].locator(\"button[data-testid='btn-atc-plus']\").click()\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py:15784: in click\n    self._sync(\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_locator.py:159: in click\n    return await self._frame.click(self._selector, strict=True, **params)\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_frame.py:484: in click\n    await self._channel.send(\"click\", locals_to_params(locals()))\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:62: in send\n    return await self._connection.wrap_api_call(\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:492: in wrap_api_call\n    return await cb()\n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n\nself = <playwright._impl._connection.Channel object at 0x000001E8EBFDD250>\nmethod = 'click'\nparams = {'selector': '//div[contains(@class,\\'cart-v2_recommends\\')] >> internal:testid=[data-testid=\"wid-product-card-container\"s] >> nth=0 >> button[data-testid=\\'btn-atc-plus\\']', 'strict': True}\nreturn_as_dict = False\n\n    async def inner_send(\n        self, method: str, params: Optional[Dict], return_as_dict: bool\n    ) -> Any:\n        if params is None:\n            params = {}\n        callback = self._connection._send_message_to_server(\n            self._object, method, params\n        )\n        if self._connection._error:\n            error = self._connection._error\n            self._connection._error = None\n            raise error\n        done, _ = await asyncio.wait(\n            {\n                self._connection._transport.on_error_future,\n                callback.future,\n            },\n            return_when=asyncio.FIRST_COMPLETED,\n        )\n        if not callback.future.done():\n            callback.future.cancel()\n>       result = next(iter(done)).result()\nE       playwright._impl._errors.TimeoutError: Timeout 50000ms exceeded.\n\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:100: TimeoutError"}, "description": "\n        [107320][dWeb]-对满68换购列表加购验证\n        测试步骤：\n        1、访问https://www.sayweee.com/en/cart购物车页面，\n        2、清除购物车\n        3、滚动到购物车底部推荐模块 ele_cart_recommendations\n        4、加购推荐商品 ele_recommend_module_card\n        5、判断购物车商品金额ele_cart_normal_item_total\n        6、如果ele_cart_normal_item_total小于$35,会显示免运费banner 入口\n        7、如果ele_cart_normal_item_total加购大于35小于68，显示换购入口banner，\n        8、此时banner文案为$x away from unlocking more discounts!\n        9、点击换购入口ele_cart_trade_in_normal，右侧弹出换购列表ele_trade_in\n        10、此时换购商品ele_trade_in_products不可加购\n        11、关闭右侧的换购ele_trade_in_close\n        12、回到购物车滚动到购物车底部推荐模块 ele_cart_recommendations，\n        13、继续加购推荐商品ele_recommend_module_card\n        14、当购物车金额大于68时，换购入口banner 文案替换为：You have selected 0/5 special deals!\n        15、点击换购入口，右侧再次弹出换购列表ele_trade_in，\n        16、此时换购商品ele_trade_in_products可加购\n        17、加购换购商品ele_trade_in_products，商品会进入购物车\n        18、注意，换购商品最多可支持5件，加购5件之后不可再加购，\n        19、回到购物车，文案会更新为You have selected 5/5 special deals!\n        20、在购物车里删除换购，当金额不足68 时，换购文案会回到：$x away from unlocking more discounts!\n        ", "start": 1749964380472, "stop": 1749964478002, "uuid": "*************-4487-a7bb-408ae0d64ff0", "historyId": "8648a796470ce9e852b0a5f6690fc4f4", "testCaseId": "8648a796470ce9e852b0a5f6690fc4f4", "fullName": "src.Dweb.EC.testcases.dweb_cart.dweb_trade_in.test_107320_dWeb_trade_in_35_x_68_ui_ux_v2.TestDWebEmptyCartUIUX#test_107320_dWeb_trade_in_35_x_68_ui_ux_v2", "labels": [{"name": "story", "value": "PC购物车-对满68换购列表加购验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "pccart"}, {"name": "tag", "value": "regression"}, {"name": "tag", "value": "transaction"}, {"name": "tag", "value": "suqin"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.dweb_cart.dweb_trade_in"}, {"name": "suite", "value": "test_107320_dWeb_trade_in_35_x_68_ui_ux_v2"}, {"name": "subSuite", "value": "TestDWebEmptyCartUIUX"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "18820-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.dweb_cart.dweb_trade_in.test_107320_dWeb_trade_in_35_x_68_ui_ux_v2"}]}
{"name": "PC端-从首页新增地址UI/UX验证", "status": "broken", "statusDetails": {"message": "playwright._impl._errors.TimeoutError: Timeout 50000ms exceeded.", "trace": "self = <src.Dweb.EC.testcases.dweb_porder.dweb_address.test_110841_dWeb_add_address_ui_ux.TestDWebAddAddressUIUX object at 0x0000019A5DC02650>\npage = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppDat...6099.28>>, 'page': <Page url='https://www.sayweee.com/en/account/address/edit?redirect_url=%2Fen%3F&source=undefined'>}\npc_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...BWYYQeb-HrwKrEfd7CWG5SrQtaROgpvxml8ohdguTViPzdU9YurKxFOKVUFaQ271bGHcBlCX3glRMBhI7jMBE2sTMwWOVnV6QvJubzprNwRwpnc0', ...}\nlogin_trace = None\n\n    @allure.title(\"PC端-从首页新增地址UI/UX验证\")\n    def test_110841_dWeb_add_address_from_home_ui_ux(self, page: dict, pc_autotest_header, login_trace):\n        \"\"\"\n        PC端-从首页新增地址UI/UX验证\n        此用例的校验点有：\n        1. 在首页点击地址按钮（使用get_by_test_id定位），进入地址选择页面\n        2. 点击\"新增地址\"按钮（使用get_by_test_id定位），进入新增地址页面\n        3. 填写地址信息（街道地址、姓名、电话、备注）\n        4. 保存地址，验证地址簿中新增了一条地址\n        \"\"\"\n        p: Page = page.get(\"page\")\n        c = page.get(\"context\")\n    \n        # 创建地址页面对象\n        address_page = DWebAddressPage(p, pc_autotest_header, browser_context=c)\n    \n        # 从首页添加新地址\n>       address_page.add_new_address_from_home()\n\ntest_110841_dWeb_add_address_ui_ux.py:32: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n..\\..\\..\\dweb_pages\\dweb_page_address\\dweb_page_address.py:38: in add_new_address_from_home\n    self._fill_address_form()\n..\\..\\..\\dweb_pages\\dweb_page_address\\dweb_page_address.py:185: in _fill_address_form\n    self.page.locator(\"#rc_select_0\").fill(\"15006 104th Ave NE\")\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py:16154: in fill\n    self._sync(\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_locator.py:211: in fill\n    return await self._frame.fill(self._selector, strict=True, **params)\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_frame.py:523: in fill\n    await self._channel.send(\"fill\", locals_to_params(locals()))\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:62: in send\n    return await self._connection.wrap_api_call(\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:492: in wrap_api_call\n    return await cb()\n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n\nself = <playwright._impl._connection.Channel object at 0x0000019A6015F0D0>\nmethod = 'fill'\nparams = {'selector': '#rc_select_0', 'strict': True, 'value': '15006 104th Ave NE'}\nreturn_as_dict = False\n\n    async def inner_send(\n        self, method: str, params: Optional[Dict], return_as_dict: bool\n    ) -> Any:\n        if params is None:\n            params = {}\n        callback = self._connection._send_message_to_server(\n            self._object, method, params\n        )\n        if self._connection._error:\n            error = self._connection._error\n            self._connection._error = None\n            raise error\n        done, _ = await asyncio.wait(\n            {\n                self._connection._transport.on_error_future,\n                callback.future,\n            },\n            return_when=asyncio.FIRST_COMPLETED,\n        )\n        if not callback.future.done():\n            callback.future.cancel()\n>       result = next(iter(done)).result()\nE       playwright._impl._errors.TimeoutError: Timeout 50000ms exceeded.\n\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:100: TimeoutError"}, "description": "\n        PC端-从首页新增地址UI/UX验证\n        此用例的校验点有：\n        1. 在首页点击地址按钮（使用get_by_test_id定位），进入地址选择页面\n        2. 点击\"新增地址\"按钮（使用get_by_test_id定位），进入新增地址页面\n        3. 填写地址信息（街道地址、姓名、电话、备注）\n        4. 保存地址，验证地址簿中新增了一条地址\n        ", "start": 1751435232107, "stop": 1751435308843, "uuid": "a5ff9297-638d-48c9-918b-4b1bcda8a09e", "historyId": "c54c8c7f339aa5625211bafca5a3bf7b", "testCaseId": "c54c8c7f339aa5625211bafca5a3bf7b", "fullName": "src.Dweb.EC.testcases.dweb_porder.dweb_address.test_110841_dWeb_add_address_ui_ux.TestDWebAddAddressUIUX#test_110841_dWeb_add_address_from_home_ui_ux", "labels": [{"name": "story", "value": "PC端-新增地址UI/UX验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "dweb_regression"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.dweb_porder.dweb_address"}, {"name": "suite", "value": "test_110841_dWeb_add_address_ui_ux"}, {"name": "subSuite", "value": "TestDWebAddAddressUIUX"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "17796-Main<PERSON><PERSON>ead"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.dweb_porder.dweb_address.test_110841_dWeb_add_address_ui_ux"}]}
# -*- coding: utf-8 -*-
"""
<AUTHOR>  su<PERSON><PERSON> she
@Version        :  V1.0.0
------------------------------------
@Description    :  
@CreateTime     :  2024/3/27
@Software       :  PyCharm
------------------------------------
"""
import weeeTest
from weeeTest import log, weeeConfig
from datetime import datetime, timedelta


class CentralGrowth(weeeTest.TestCase):

    def banner_query_banner(self, headers, page: int = 1, size: int = 10,
                            type: str = "home", biz_type: str = "advertisement",
                            support_area: int = 1, support_language: str = "en",
                            isAd: bool = True,pos:int = None
                            ):
        """
         Ads Schedule管理
        """
        data = {
            "page": page,# 非必填，默认1
            "size": size,# 非必填，默认10
            "type": type,# 必填，"home","category"
            "biz_type": biz_type,# 必填， 默认"advertisement"
            "support_area": support_area,# 必填，默认1
            "support_language": support_language,# 必填，，默认"zh"
            "isAd": isAd,# 默认true
            "pos":pos # 非必填

        }
        self.get(url='/central/growth/banner/query_banner', headers=headers, params=data)
        return self.response

    def banner_query_type_info(self, headers,
                               is_ad: bool = True
                               ):
        """
         Ads Schedule管理filter 数据
        """
        data = {

            "is_ad": is_ad, # 默认true

        }
        self.get(url='/central/growth/banner/query_type_info', headers=headers, params=data)
        return self.response

    def banner_portal_list_normal(self, headers, sales_org: list,
                                  language_store: list, placement: list, sales_type: str = "sales_org",
                                  status: list = None,
                                  name: str = None, id: str = None, start_time: int = None,
                                  end_time: int = None,
                                  page_no: int = 1, page_size: int = 10,
                                  time_zone: str = "Asia/Shanghai"

                                  ):
        """
         Banner list 页面
        """
        data = {

            "sales_type": sales_type,# 必填，默认"sales_org"
            "sales_org": sales_org,# 必填，[1,2]
            "language_store": language_store,# 列表格式，必填，可多选，["en", "en_cn"]
            "placement": placement,# 列表格式，必填，可多选， ["home", "collect", "topx"]
            # "platform": platform,# 非必填，
            "status": status, # 列表格式，非必填，可多选，["C", "A", "E", "X"]
            "name": name, # 非必填，"test"
            "id": id, # 非必填，假设 "71379"
            "start_time": start_time,# 非必填，默认不传
            "end_time": end_time,# 非必填，默认不传
            "page_no": page_no,# 非必填，默认1
            "page_size": page_size,# 非必填，默认 10
            "time_zone": time_zone # 非必填  默认"Asia/Shanghai"
        }
        self.post(url='/central/growth/banner/portal/list/normal', headers=headers, json=data)
        return self.response

    def banner_portal_option(self, headers):
        """
         Banner list 页面filter 数据
        """
        data = None
        self.get(url='/central/growth/banner/portal/option', headers=headers, params=data)
        return self.response

    def banner_portal_modify_by_group_id(self, headers, status: str = None, group_id: int = 42336):
        """
         修改Banner list 页面数据状态
        """
        data = {
            "group_id": group_id,
            "status": status,  # A\C\X

        }
        self.post(url='/central/growth/banner/portal/modify/by_group_id', headers=headers, json=data)
        return self.response

    def banner_portal_list_calendar(self, headers, sales_type: str = "sales_org", sales_org: list = [1],
                                    language_store: list = ["en"], placement: list = ["home"],
                                    platform: list = None, status: str = "A",
                                    name: str = None, id: str = None, start_time: int = None,
                                    end_time: int = None,
                                    page_no: int = 1, page_size: int = 10,
                                    time_zone: str = "Asia/Shanghai"

                                    ):
        """
         Banner list 日历页面
        """
        data = {

            "sales_type": sales_type,# 必填，默认"sales_org"
            "sales_org": sales_org,# 必填，[1,2]
            "language_store": language_store,# 列表格式，必填，可多选，["en", "en_cn"]
            "placement": placement,# 列表格式，必填，可多选， ["home", "collect", "topx"]
            # "platform": platform,
            "status": status,
            "name": name,
            "id": id,
            "start_time": start_time,
            "end_time": end_time,
            "page_no": page_no,
            "page_size": page_size,
            "time_zone": time_zone
        }
        self.post(url='/central/growth/banner/portal/list/calendar', headers=headers, json=data)
        return self.response

    def asset_info_list(self, headers, page: int = 1, size: int = 20, menu_id: int = 20,
                        time_zone: str = "Asia/Shanghai"

                        ):
        """
         资源管理页面
        """
        data = {
            "page": page,
            "size": size,
            "param": {"menu_id": menu_id, "time_zone": time_zone}

        }
        self.post(url='/central/growth/asset/info/list', headers=headers, json=data)
        return self.response

    def feedback_detail_statistics(self, headers,
                                   start_time: int = int((datetime.now() - timedelta(weeks=1)).timestamp()),
                                   end_time: int = int(datetime.now().timestamp()),
                                   type: str = None

                                   ):
        """
         报表-用户反馈-
        """
        data = {
            "start_time": start_time,
            "end_time": end_time,
            "type": type
        }

        self.get(url='/central/growth/feedback/detail/statistics', headers=headers, params=data)
        return self.response

    def feedback_detail_items(self, headers,
                              start_time: int = int((datetime.now() - timedelta(weeks=1)).timestamp()),
                              end_time: int = int(datetime.now().timestamp()),
                              type: str = None,
                              page_no: int = 1, page_size: int = 10,
                              classify: str = None, grade: str = None,
                              order_name: str = "timing", order_ordinal: str = "desc"

                              ):
        """
         报表-用户反馈-
        """
        data = {
            "start_time": start_time,
            "end_time": end_time,
            "type": type,
            "page_no": page_no,
            "page_size": page_size,
            "classify": classify,
            "grade": grade,
            "order_name": order_name,
            "order_ordinal": order_ordinal
        }

        self.get(url='/central/growth/feedback/detail/items', headers=headers, params=data)
        return self.response

# -*- coding: utf-8 -*-
"""
<AUTHOR>  sufen xu
@Version        :  V1.0.0
------------------------------------
@Description    :  
@CreateTime     :  2023/7/19
@Software       :  PyCharm
------------------------------------
"""
import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath


class User(weeeTest.TestCase):
    def anon_auth(self, headers):
        self.get(url='/ec/customer/login/token/generate')
        auth = jmespath(self.response, "object.token")
        log.info(f'匿名auth:{auth}')
        headers["authorization"] = 'Bearer ' + auth
        return auth

    def email_register(self, headers, email: str = None, password: str = None):
        if email is None or password is None:
            raise Exception('注册的email,password不能为空')
        body = {
            "email": email,
            "password": password,
            "referral_id": 0
        }
        self.post(url='/ec/customer/signup', headers=headers, json=body)
        auth = jmespath(self.response, "object.token")
        if auth is not None and len(auth) > 0:
            log.info(f'注册成功auth:{auth}')
            headers["authorization"] = 'Bearer ' + auth
        else:
            log.info(f'注册失败,msg:{jmespath(self.response, "message")}')
        return self.response

    def email_user_info(self, headers):
        self.get(url='/ec/customer/user/info/simple', headers=headers)
        status = jmespath(self.response, "object.isNewUser")
        if status is True:
            log.info(f'新用户注册status:{status}')
        else:
            log.info(f'老用户登录，msg:{jmespath(self.response, "message")}')


    def refresh_token(self, headers):
        """# 刷新token """
        data = None
        self.post(url='/ec/customer/login/token/refresh', headers=headers, json=data)

        return self.response


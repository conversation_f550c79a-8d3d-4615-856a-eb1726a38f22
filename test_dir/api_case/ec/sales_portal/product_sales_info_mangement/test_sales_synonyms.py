# !/usr/bin/python3
# -*- coding: utf-8 -*-


import weeeTest

from test_dir.api.ec.central_portal.central_im import CentralIm


class TestSalesSynonyms(weeeTest.TestCase):

    @weeeTest.mark.list('sales', 'Transaction', 'test_sales_synonyms')
    def test_sales_synonyms(self, sales_header):
        """ # 商品销售信息管理-商品内容信息管理-同义词管理-查询同义词管理页面数据"""
        synonym_list = CentralIm().search_synonym_list(headers=sales_header)

        assert len(synonym_list["object"]["data"]) > 0, f'查询同义词管理页面数据异常{synonym_list}'


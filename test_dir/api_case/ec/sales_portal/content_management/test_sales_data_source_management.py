# !/usr/bin/python3
# -*- coding: utf-8 -*-


import weeeTest

from test_dir.api.ec.central_portal.central_content import CentralContent
from test_dir.api.ec.central_portal.central_growth import CentralGrowth


class TestSalesDataSourceMgmt(weeeTest.TestCase):
    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal')
    def test_sales_data_source(self, sales_header):
        """ # 页面管理-数据源管理-查询数据源管理页面数据 """
        data_object_list = CentralContent().content_data_object_list(headers=sales_header)
        assert data_object_list.get("result") is True, f'数据源管理查询失败{data_object_list}'
        assert len(data_object_list.get("object", {}).get("data", [])) > 0, f'查询数据源管理页面数据为空{data_object_list}'

        # 验证返回数据的基本结构
        if data_object_list.get("object", {}).get("data"):
            for data_source in data_object_list["object"]["data"][:3]:  # 只检查前3个
                assert data_source.get("id") is not None, f'数据源ID为空'
                assert data_source.get("name") is not None, f'数据源名称为空'
                assert data_source.get("data_type") is not None, f'数据源类型为空'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal')
    def test_sales_data_source_with_keyword_filter(self, sales_header):
        """ # 页面管理-数据源管理-查询数据源管理页面数据(按关键词筛选) """
        # 测试关键词搜索
        keywords = ["banner", "item", "ds", "test"]

        for keyword in keywords:
            data_object_list = CentralContent().content_data_object_list(
                headers=sales_header,
                keyword=keyword
            )
            assert data_object_list.get("result") is True, f'关键词{keyword}数据源查询失败{data_object_list}'
            # 如果有结果，验证关键词是否在数据源信息中
            if data_object_list.get("object", {}).get("data"):
                for data_source in data_object_list["object"]["data"][:2]:  # 只检查前2个
                    data_source_info = str(data_source.get("name", "")) + str(data_source.get("description", ""))
                    # 注意：这里不强制要求关键词匹配，因为搜索可能是模糊匹配

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal')
    def test_sales_data_source_with_seller_filter(self, sales_header):
        """ # 页面管理-数据源管理-查询数据源管理页面数据(按商家筛选) """
        # 测试不同商家ID的数据源
        seller_ids = [0, 1, 2, 3]

        for seller_id in seller_ids:
            data_object_list = CentralContent().content_data_object_list(
                headers=sales_header,
                seller_id=seller_id
            )
            assert data_object_list.get("result") is True, f'商家ID{seller_id}数据源查询失败{data_object_list}'
            # 验证返回数据的商家ID信息
            if data_object_list.get("object", {}).get("data"):
                for data_source in data_object_list["object"]["data"][:2]:  # 只检查前2个
                    assert data_source.get("seller_id") is not None, f'数据源商家ID为空，筛选商家ID:{seller_id}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal')
    def test_sales_data_source_with_data_type_filter(self, sales_header):
        """ # 页面管理-数据源管理-查询数据源管理页面数据(按数据类型筛选) """
        # 测试不同数据类型
        data_types = [1, 2, 3]

        for data_type in data_types:
            data_object_list = CentralContent().content_data_object_list(
                headers=sales_header,
                data_type=data_type
            )
            assert data_object_list.get("result") is True, f'数据类型{data_type}数据源查询失败{data_object_list}'
            # 验证返回数据的数据类型
            if data_object_list.get("object", {}).get("data"):
                for data_source in data_object_list["object"]["data"][:2]:  # 只检查前2个
                    assert data_source.get("data_type") == data_type, f'数据源类型不匹配，期望:{data_type}，实际:{data_source.get("data_type")}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal')
    def test_sales_data_source_with_user_filter(self, sales_header):
        """ # 页面管理-数据源管理-查询数据源管理页面数据(按创建用户筛选) """
        # 测试不同创建用户ID
        user_ids = [1, 2, 3, 4]

        for user_id in user_ids:
            data_object_list = CentralContent().content_data_object_list(
                headers=sales_header,
                in_user=user_id
            )
            assert data_object_list.get("result") is True, f'创建用户ID{user_id}数据源查询失败{data_object_list}'
            # 验证返回数据的创建用户信息
            if data_object_list.get("object", {}).get("data"):
                for data_source in data_object_list["object"]["data"][:2]:  # 只检查前2个
                    assert data_source.get("in_user") is not None, f'数据源创建用户为空，筛选用户ID:{user_id}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal')
    def test_sales_data_source_with_pagination(self, sales_header):
        """ # 页面管理-数据源管理-查询数据源管理页面数据(分页测试) """
        # 测试第一页
        data_object_first = CentralContent().content_data_object_list(
            headers=sales_header,
            pageSize=5,
            startColumn=0
        )
        assert data_object_first.get("result") is True, f'第一页数据源查询失败{data_object_first}'

        # 测试第二页
        data_object_second = CentralContent().content_data_object_list(
            headers=sales_header,
            pageSize=5,
            startColumn=5
        )
        assert data_object_second.get("result") is True, f'第二页数据源查询失败{data_object_second}'

        # 验证分页数据不重复（如果两页都有数据的话）
        first_page_ids = []
        second_page_ids = []

        if data_object_first.get("object", {}).get("data"):
            first_page_ids = [ds.get("id") for ds in data_object_first["object"]["data"]]

        if data_object_second.get("object", {}).get("data"):
            second_page_ids = [ds.get("id") for ds in data_object_second["object"]["data"]]

        # 如果两页都有数据，验证没有重复
        if first_page_ids and second_page_ids:
            common_ids = set(first_page_ids) & set(second_page_ids)
            assert len(common_ids) == 0, f'分页数据重复: {common_ids}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal')
    def test_sales_data_source_combined_filters(self, sales_header):
        """ # 页面管理-数据源管理-查询数据源管理页面数据(组合筛选条件) """
        # 测试组合筛选条件
        data_object_list = CentralContent().content_data_object_list(
            headers=sales_header,
            keyword="banner",
            seller_id=0,
            data_type=1,
            pageSize=10,
            startColumn=0
        )
        assert data_object_list.get("result") is True, f'组合筛选条件数据源查询失败{data_object_list}'

        # 验证返回数据的基本结构
        if data_object_list.get("object", {}).get("data"):
            for data_source in data_object_list["object"]["data"][:3]:  # 只检查前3个
                assert data_source.get("id") is not None, f'数据源ID为空'
                assert data_source.get("name") is not None, f'数据源名称为空'
                assert data_source.get("data_type") == 1, f'数据源类型不符合筛选条件: {data_source.get("data_type")}'
                assert data_source.get("seller_id") == 0, f'数据源商家ID不符合筛选条件: {data_source.get("seller_id")}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal')
    def test_sales_data_source_edge_cases(self, sales_header):
        """ # 页面管理-数据源管理-查询数据源管理页面数据(边界情况测试) """
        # 测试边界情况：很小的分页大小
        data_object_small = CentralContent().content_data_object_list(
            headers=sales_header,
            pageSize=1,
            startColumn=0
        )
        assert data_object_small.get("result") is True, f'小分页数据源查询失败{data_object_small}'

        # 测试边界情况：很大的分页大小
        data_object_large = CentralContent().content_data_object_list(
            headers=sales_header,
            pageSize=100,
            startColumn=0
        )
        assert data_object_large.get("result") is True, f'大分页数据源查询失败{data_object_large}'

        # 测试边界情况：空关键词
        data_object_empty_keyword = CentralContent().content_data_object_list(
            headers=sales_header,
            keyword=""
        )
        assert data_object_empty_keyword.get("result") is True, f'空关键词数据源查询失败{data_object_empty_keyword}'

        # 测试边界情况：不存在的关键词
        data_object_nonexistent = CentralContent().content_data_object_list(
            headers=sales_header,
            keyword="nonexistent_keyword_12345"
        )
        assert data_object_nonexistent.get("result") is True, f'不存在关键词数据源查询失败{data_object_nonexistent}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal')
    def test_sales_data_source_data_structure_validation(self, sales_header):
        """ # 页面管理-数据源管理-查询数据源管理页面数据(数据结构验证) """
        data_object_list = CentralContent().content_data_object_list(headers=sales_header)
        assert data_object_list.get("result") is True, f'数据源查询失败{data_object_list}'

        # 验证返回数据的完整结构
        if data_object_list.get("object", {}).get("data"):
            for data_source in data_object_list["object"]["data"][:5]:  # 检查前5个
                # 验证必要字段
                assert data_source.get("id") is not None, f'数据源ID为空'
                assert data_source.get("name") is not None, f'数据源名称为空'
                assert data_source.get("data_type") is not None, f'数据源类型为空'
                assert data_source.get("seller_id") is not None, f'数据源商家ID为空'

                # 验证数据类型
                assert isinstance(data_source.get("id"), (int, str)), f'数据源ID类型错误'
                assert isinstance(data_source.get("name"), str), f'数据源名称类型错误'
                assert isinstance(data_source.get("data_type"), int), f'数据源类型字段类型错误'
                assert isinstance(data_source.get("seller_id"), int), f'数据源商家ID类型错误'


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net')

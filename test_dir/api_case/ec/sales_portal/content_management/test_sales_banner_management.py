# !/usr/bin/python3
# -*- coding: utf-8 -*-


import weeeTest
from test_dir.api.ec.central_portal.central_growth import CentralGrowth


class TestSalesBannerMgmt(weeeTest.TestCase):
    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_banner_query_banner(self, sales_header):
        """ # 页面管理-Banner管理-ADS schedule管理-查询ADS schedule页面数据 """
        query_banner = CentralGrowth().banner_query_banner(headers=sales_header)
        assert len(query_banner.get("object", {}).get("data", [])) > 0, f'查询ADS schedule页面数据为空{query_banner}'

        # 验证返回数据的基本结构
        if query_banner.get("object", {}).get("data"):
            for banner in query_banner["object"]["data"][:3]:  # 只检查前3个
                assert banner.get("id") is not None, f'Banner ID为空'
                assert banner.get("type") is not None, f'Banner类型为空'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_banner_query_banner_with_type_filter(self, sales_header):
        """ # 页面管理-Banner管理-ADS schedule管理-查询ADS schedule页面数据(按类型筛选) """
        # 测试home类型的广告
        query_banner_home = CentralGrowth().banner_query_banner(headers=sales_header, type="home")
        assert query_banner_home.get("result") is True, f'查询home类型广告失败{query_banner_home}'

        # 测试category类型的广告
        query_banner_category = CentralGrowth().banner_query_banner(headers=sales_header, type="category")
        assert query_banner_category.get("result") is True, f'查询category类型广告失败{query_banner_category}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_banner_query_banner_with_language_filter(self, sales_header):
        """ # 页面管理-Banner管理-ADS schedule管理-查询ADS schedule页面数据(按语言筛选) """
        languages = ["en", "zh", "ja", "vi", "zh-Hant", "ko"]

        for lang in languages:
            query_banner = CentralGrowth().banner_query_banner(headers=sales_header, support_language=lang)
            assert query_banner.get("result") is True, f'查询语言为{lang}的广告失败{query_banner}'
            # 验证返回的数据结构
            if query_banner.get("object", {}).get("data"):
                for banner in query_banner["object"]["data"][:2]:  # 只检查前2个
                    assert banner.get("id") is not None, f'Banner ID为空，语言:{lang}'
                    assert banner.get("support_language") is not None, f'支持语言为空，语言:{lang}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_banner_query_banner_with_area_filter(self, sales_header):
        """ # 页面管理-Banner管理-ADS schedule管理-查询ADS schedule页面数据(按区域筛选) """
        # 测试不同区域的广告
        areas = [1, 2, 4]

        for area in areas:
            query_banner = CentralGrowth().banner_query_banner(headers=sales_header, support_area=area)
            assert query_banner.get("result") is True, f'查询区域{area}的广告失败{query_banner}'
            # 验证返回的数据结构
            if query_banner.get("object", {}).get("data"):
                for banner in query_banner["object"]["data"][:2]:  # 只检查前2个
                    assert banner.get("id") is not None, f'Banner ID为空，区域:{area}'
                    assert banner.get("support_area") is not None, f'支持区域为空，区域:{area}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_banner_query_banner_with_position_filter(self, sales_header):
        """ # 页面管理-Banner管理-ADS schedule管理-查询ADS schedule页面数据(按位置筛选) """
        # 测试不同位置的广告
        positions = [3, 5]

        for pos in positions:
            query_banner = CentralGrowth().banner_query_banner(headers=sales_header, pos=pos)
            assert query_banner.get("result") is True, f'查询位置{pos}的广告失败{query_banner}'
            # 验证返回的数据结构
            if query_banner.get("object", {}).get("data"):
                for banner in query_banner["object"]["data"][:2]:  # 只检查前2个
                    assert banner.get("id") is not None, f'Banner ID为空，位置:{pos}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_banner_query_type_info(self, sales_header):
        """ # 页面管理-Banner管理-ADS schedule管理-ADS schedule页面过滤器返回数据 """
        query_type_info = CentralGrowth().banner_query_type_info(headers=sales_header)
        biz_type = query_type_info.get("object", {}).get("biz_type", [])
        support_area = query_type_info.get("object", {}).get("support_area", [])
        type_info = query_type_info.get("object", {}).get("type", [])
        vendor_list = query_type_info.get("object", {}).get("vendor_list", [])
        assert len(biz_type) > 0, f'查询ADS schedule页面biz_type数据为空{query_type_info}'
        assert len(support_area) > 0, f'查询ADS schedule页面support_area数据为空{query_type_info}'
        assert len(type_info) > 0, f'查询ADS schedule页面type数据为空{query_type_info}'
        assert len(vendor_list) > 0, f'查询ADS schedule页面vendor_list数据为空{query_type_info}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_banner_portal_list_normal(self, sales_header):
        """ # 页面管理-Banner管理-Banner列表-查询Banner列表页面数据 """
        banner_portal_list_normal = CentralGrowth().banner_portal_list_normal(headers=sales_header,
                                                                              sales_type="sales_org",
                                                                              sales_org=[1],
                                                                              language_store=["en"],
                                                                              placement=["home"])
        assert len(
            banner_portal_list_normal.get("object", {}).get("result", [])) > 0, f'查询Banner列表页面数据为空{banner_portal_list_normal}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_banner_portal_option(self, sales_header):
        """ # 页面管理-Banner管理-Banner列表-查询Banner列表页面过滤器返回数据 """
        banner_portal_option = CentralGrowth().banner_portal_option(headers=sales_header)
        frequency = banner_portal_option.get("object", {}).get("frequency", {}).get("enums", [])
        languageStore = banner_portal_option.get("object", {}).get("languageStore", {}).get("enums", [])
        placement = banner_portal_option.get("object", {}).get("placement", {}).get("enums", [])
        platform = banner_portal_option.get("object", {}).get("platform", {}).get("enums", [])
        play_mode = banner_portal_option.get("object", {}).get("play_mode", {}).get("enums", [])
        salesRegion = banner_portal_option.get("object", {}).get("salesRegion", {}).get("enums", [])
        status = banner_portal_option.get("object", {}).get("status", {}).get("enums", [])
        type_enums = banner_portal_option.get("object", {}).get("type", {}).get("enums", [])
        assert len(frequency) > 0, f'查询Banner列表页面frequency过滤器返回数据为空{banner_portal_option}'
        assert len(languageStore) > 0, f'查询Banner列表页面languageStore过滤器返回数据为空{banner_portal_option}'
        assert len(placement) > 0, f'查询Banner列表页面placement过滤器返回数据为空{banner_portal_option}'
        assert len(platform) > 0, f'查询Banner列表页面platform过滤器返回数据为空{banner_portal_option}'
        assert len(play_mode) > 0, f'查询Banner列表页面play_mode过滤器返回数据为空{banner_portal_option}'
        assert len(salesRegion) > 0, f'查询Banner列表页面salesRegion过滤器返回数据为空{banner_portal_option}'
        assert len(status) > 0, f'查询Banner列表页面status过滤器返回数据为空{banner_portal_option}'
        assert len(type_enums) > 0, f'查询Banner列表页面type过滤器返回数据为空{banner_portal_option}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_banner_portal_modify_by_group_id(self, sales_header):
        """ # 页面管理-Banner管理-Banner列表-修改Banner列表页面数据 """
        banner_portal_list_normal = CentralGrowth().banner_portal_list_normal(headers=sales_header,
                                                                              sales_type="sales_org",
                                                                              sales_org=[1],
                                                                              language_store=["en"],
                                                                              placement=["home"]
                                                                              )
        assert len(banner_portal_list_normal.get("object", {}).get("result", [])) > 0, f'查询Banner列表页面数据为空{banner_portal_list_normal}'

        result_list = banner_portal_list_normal.get("object", {}).get("result", [])
        if result_list:
            group_id = result_list[0].get("id")
            status = result_list[0].get("status")
            update_status = None
            if status == "A":
                update_status = "C"
            elif status == "C":
                update_status = "A"

            if group_id and update_status:
                banner_portal_modify = CentralGrowth().banner_portal_modify_by_group_id(headers=sales_header,
                                                                                        group_id=group_id,
                                                                                        status=update_status)
                assert banner_portal_modify.get("result") is True, f'修改Banner列表页面数据失败{banner_portal_modify}'


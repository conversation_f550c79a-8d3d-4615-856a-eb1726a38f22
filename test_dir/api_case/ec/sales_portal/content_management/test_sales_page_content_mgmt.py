# !/usr/bin/python3
# -*- coding: utf-8 -*-


import weeeTest

from test_dir.api.ec.central_portal.central_content import CentralContent


class TestSalesPageContentMgmt(weeeTest.TestCase):
    def _sales_activity_page(self, sales_header):
        """ # 面管理-内容管理-查询单活动页面 """
        content_page_list = CentralContent().content_page_list(headers=sales_header, page_type=7)
        assert len(content_page_list["object"]["data"]) > 0, f'内容管理-查询单活动页面为空{content_page_list}'
        return content_page_list

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal')
    def test_search_sales_activity_page(self,sales_header):
        """ # 页面管理-内容管理-查询单活动页面 """
        activity_page = CentralContent().content_page_list(headers=sales_header, page_type=7)
        assert len(activity_page["object"]["list"]) > 0, f'内容管理-查询单活动页面为空{activity_page}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal')
    def test_search_sales_activity_page_with_status_filter(self, sales_header):
        """ # 页面管理-内容管理-查询单活动页面(按状态筛选) """
        # 测试状态为A的活动页面
        activity_page_active = CentralContent().content_page_list(headers=sales_header, page_type=7, status="A")
        assert activity_page_active.get("result") is True, f'查询状态为A的活动页面失败{activity_page_active}'

        # 测试状态为R的活动页面
        activity_page_rejected = CentralContent().content_page_list(headers=sales_header, page_type=7, status="R")
        assert activity_page_rejected.get("result") is True, f'查询状态为R的活动页面失败{activity_page_rejected}'

        # 测试状态为G的活动页面
        activity_page_gray = CentralContent().content_page_list(headers=sales_header, page_type=7, status="G")
        assert activity_page_gray.get("result") is True, f'查询状态为G的活动页面失败{activity_page_gray}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal')
    def test_search_sales_activity_page_with_language_filter(self, sales_header):
        """ # 页面管理-内容管理-查询单活动页面(按语言筛选) """
        languages = ["en", "zh", "ja", "vi", "zh-Hant", "ko"]

        for lang in languages:
            activity_page = CentralContent().content_page_list(headers=sales_header, page_type=7, lang=lang)
            assert activity_page.get("result") is True, f'查询语言为{lang}的活动页面失败{activity_page}'
            # 验证返回的数据结构
            if activity_page.get("object", {}).get("list"):
                for page in activity_page["object"]["list"][:3]:  # 只检查前3个
                    assert page.get("page_key") is not None, f'页面key为空，语言:{lang}'
                    assert page.get("page_url") is not None, f'页面URL为空，语言:{lang}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal')
    def test_search_sales_activity_page_with_homepage_filter(self, sales_header):
        """ # 页面管理-内容管理-查询单活动页面(按首页显示筛选) """
        # 测试显示在首页的活动页面
        activity_page_homepage = CentralContent().content_page_list(headers=sales_header, page_type=7, is_show_homepage=1)
        assert activity_page_homepage.get("result") is True, f'查询首页显示的活动页面失败{activity_page_homepage}'

        # 测试不显示在首页的活动页面
        activity_page_not_homepage = CentralContent().content_page_list(headers=sales_header, page_type=7, is_show_homepage=0)
        assert activity_page_not_homepage.get("result") is True, f'查询非首页显示的活动页面失败{activity_page_not_homepage}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal')
    def test_search_sales_activity_page_with_keyword_filter(self, sales_header):
        """ # 页面管理-内容管理-查询单活动页面(按关键词筛选) """
        # 测试关键词搜索
        keywords = ["test", "activity", "page"]

        for keyword in keywords:
            activity_page = CentralContent().content_page_list(headers=sales_header, page_type=7, keyword=keyword)
            assert activity_page.get("result") is True, f'关键词{keyword}搜索活动页面失败{activity_page}'
            # 如果有结果，验证关键词是否在页面信息中
            if activity_page.get("object", {}).get("list"):
                for page in activity_page["object"]["list"][:2]:  # 只检查前2个
                    page_info = str(page.get("page_key", "")) + str(page.get("page_url", "")) + str(page.get("page_title", ""))
                    # 注意：这里不强制要求关键词匹配，因为搜索可能是模糊匹配

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal')
    def test_search_sales_activity_page_with_pagination(self, sales_header):
        """ # 页面管理-内容管理-查询单活动页面(分页测试) """
        # 测试第一页
        activity_page_first = CentralContent().content_page_list(headers=sales_header, page_type=7, limit=20, offset=0)
        assert activity_page_first.get("result") is True, f'查询第一页活动页面失败{activity_page_first}'

        # 测试第二页
        activity_page_second = CentralContent().content_page_list(headers=sales_header, page_type=7, limit=20, offset=20)
        assert activity_page_second.get("result") is True, f'查询第二页活动页面失败{activity_page_second}'

        # 验证分页数据不重复（如果两页都有数据的话）
        first_page_keys = []
        second_page_keys = []

        if activity_page_first.get("object", {}).get("list"):
            first_page_keys = [page.get("page_key") for page in activity_page_first["object"]["list"]]

        if activity_page_second.get("object", {}).get("list"):
            second_page_keys = [page.get("page_key") for page in activity_page_second["object"]["list"]]

        # 如果两页都有数据，验证没有重复
        if first_page_keys and second_page_keys:
            common_keys = set(first_page_keys) & set(second_page_keys)
            assert len(common_keys) == 0, f'分页数据重复: {common_keys}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal')
    def test_search_sales_routing_list(self, sales_header):
        """ # 页面管理-内容管理-查询复合活动页面 """
        activity_page = CentralContent().content_routing_list(headers=sales_header)
        assert len(activity_page["object"]["data"]) > 0, f'内容管理-查询复合活动页面为空{activity_page}'
        for index, data in enumerate(activity_page["object"]["data"]):
            assert data["routing_key"] is not None
            assert data["routing_url"] is not None
            # 更新复合页面数据状态
            if data["status"] == "A":
                outing_operate = CentralContent().content_routing_operate(headers=sales_header,
                                                                          routing_id=data["rec_id"],
                                                                          status="R")
                assert outing_operate["object"] == "success", f'更新复合页面数据状态{activity_page}'

            elif data["status"] == "R":
                outing_operate = CentralContent().content_routing_operate(headers=sales_header,
                                                                          routing_id=data["rec_id"],
                                                                          status="R")
                assert outing_operate["object"] == "success", f'更新复合页面数据状态{activity_page}'

            if index == 5:
                break

    @weeeTest.mark.list('sales', 'Transaction', 'test_search_sales_brand_page')
    def test_search_sales_brand_page(self, sales_header):
        """ # 页面管理-内容管理-查询品牌动页面 """
        content_page_list = CentralContent().content_page_list(headers=sales_header, page_type=9)
        assert len(content_page_list["object"]["list"]) > 0, f'内容管理-查询品牌动页面为空{content_page_list}'
        for index, data in enumerate(content_page_list["object"]["list"]):
            assert data["page_url"] is not None, f'内容管理-查询品牌动页面为空{content_page_list}'
            assert data["page_key"] is not None, f'内容管理-查询品牌动页面为空{content_page_list}'

            # # offline 品牌页面数据---暂时不执行
            # if data["status"] == "A":
            #     page_trash = CentralContent().content_page_trash(headers=sales_header,
            #                                                      page_key=data["page_key"],
            #                                                      page_type=9)
            #     assert page_trash["object"] == data["page_key"]
            #
            # elif data["status"] == "R":
            #     page_trash = CentralContent().content_page_trash(headers=sales_header,
            #                                                      page_key=data["page_key"],
            #                                                      page_type=9)
            #     assert page_trash["object"] == data["page_key"]

            if index == 5:
                break

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal')
    def test_search_sales_activity_page_with_status_filter(self, sales_header):
        """ # 页面管理-内容管理-查询单活动页面(按状态筛选) """
        # 测试状态为A的活动页面
        activity_page_active = CentralContent().content_page_list(headers=sales_header, page_type=7, status="A")
        assert activity_page_active.get("result") is True, f'查询状态为A的活动页面失败{activity_page_active}'

        # 测试状态为R的活动页面
        activity_page_rejected = CentralContent().content_page_list(headers=sales_header, page_type=7, status="R")
        assert activity_page_rejected.get("result") is True, f'查询状态为R的活动页面失败{activity_page_rejected}'

        # 测试状态为G的活动页面
        activity_page_gray = CentralContent().content_page_list(headers=sales_header, page_type=7, status="G")
        assert activity_page_gray.get("result") is True, f'查询状态为G的活动页面失败{activity_page_gray}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal')
    def test_search_sales_activity_page_with_language_filter(self, sales_header):
        """ # 页面管理-内容管理-查询单活动页面(按语言筛选) """
        languages = ["en", "zh", "ja", "vi", "zh-Hant", "ko"]

        for lang in languages:
            activity_page = CentralContent().content_page_list(headers=sales_header, page_type=7, lang=lang)
            assert activity_page.get("result") is True, f'查询语言为{lang}的活动页面失败{activity_page}'
            # 验证返回的数据结构
            if activity_page.get("object", {}).get("list"):
                for page in activity_page["object"]["list"][:3]:  # 只检查前3个
                    assert page.get("page_key") is not None, f'页面key为空，语言:{lang}'
                    assert page.get("page_url") is not None, f'页面URL为空，语言:{lang}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal')
    def test_search_sales_activity_page_with_homepage_filter(self, sales_header):
        """ # 页面管理-内容管理-查询单活动页面(按首页显示筛选) """
        # 测试显示在首页的活动页面
        activity_page_homepage = CentralContent().content_page_list(headers=sales_header, page_type=7, is_show_homepage=1)
        assert activity_page_homepage.get("result") is True, f'查询首页显示的活动页面失败{activity_page_homepage}'

        # 测试不显示在首页的活动页面
        activity_page_not_homepage = CentralContent().content_page_list(headers=sales_header, page_type=7, is_show_homepage=0)
        assert activity_page_not_homepage.get("result") is True, f'查询非首页显示的活动页面失败{activity_page_not_homepage}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal')
    def test_search_sales_activity_page_with_keyword_filter(self, sales_header):
        """ # 页面管理-内容管理-查询单活动页面(按关键词筛选) """
        # 测试关键词搜索
        keywords = ["test", "activity", "page"]

        for keyword in keywords:
            activity_page = CentralContent().content_page_list(headers=sales_header, page_type=7, keyword=keyword)
            assert activity_page.get("result") is True, f'关键词{keyword}搜索活动页面失败{activity_page}'
            # 如果有结果，验证关键词是否在页面信息中
            if activity_page.get("object", {}).get("list"):
                for page in activity_page["object"]["list"][:2]:  # 只检查前2个
                    page_info = str(page.get("page_key", "")) + str(page.get("page_url", "")) + str(page.get("page_title", ""))
                    # 注意：这里不强制要求关键词匹配，因为搜索可能是模糊匹配

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal')
    def test_search_sales_activity_page_with_pagination(self, sales_header):
        """ # 页面管理-内容管理-查询单活动页面(分页测试) """
        # 测试第一页
        activity_page_first = CentralContent().content_page_list(headers=sales_header, page_type=7, limit=5, offset=0)
        assert activity_page_first.get("result") is True, f'查询第一页活动页面失败{activity_page_first}'

        # 测试第二页
        activity_page_second = CentralContent().content_page_list(headers=sales_header, page_type=7, limit=5, offset=5)
        assert activity_page_second.get("result") is True, f'查询第二页活动页面失败{activity_page_second}'

        # 验证分页数据不重复（如果两页都有数据的话）
        first_page_keys = []
        second_page_keys = []

        if activity_page_first.get("object", {}).get("list"):
            first_page_keys = [page.get("page_key") for page in activity_page_first["object"]["list"]]

        if activity_page_second.get("object", {}).get("list"):
            second_page_keys = [page.get("page_key") for page in activity_page_second["object"]["list"]]

        # 如果两页都有数据，验证没有重复
        if first_page_keys and second_page_keys:
            common_keys = set(first_page_keys) & set(second_page_keys)
            assert len(common_keys) == 0, f'分页数据重复: {common_keys}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal')
    def test_search_sales_activity_page_combined_filters(self, sales_header):
        """ # 页面管理-内容管理-查询单活动页面(组合筛选条件) """
        # 测试组合筛选条件：状态+语言+首页显示
        activity_page = CentralContent().content_page_list(
            headers=sales_header,
            page_type=7,
            status="A",
            lang="en",
            is_show_homepage=0,
            limit=10
        )
        assert activity_page.get("result") is True, f'组合筛选条件查询活动页面失败{activity_page}'

        # 验证返回数据的基本结构
        if activity_page.get("object", {}).get("list"):
            for page in activity_page["object"]["list"][:3]:  # 只检查前3个
                assert page.get("page_key") is not None, f'页面key为空'
                assert page.get("status") is not None, f'页面状态为空'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal')
    def test_search_sales_page_type_list(self, sales_header):
        """ # 页面管理-内容管理-查询页面类型筛选数据 """
        # 测试CMS单个页面类型筛选数据
        page_type_list_cms = CentralContent().content_page_type_list(headers=sales_header, page_type=7)
        assert page_type_list_cms.get("result") is True, f'查询CMS页面类型筛选数据失败{page_type_list_cms}'

        # 测试品牌页面类型筛选数据
        page_type_list_brand = CentralContent().content_page_type_list(headers=sales_header, page_type=9)
        assert page_type_list_brand.get("result") is True, f'查询品牌页面类型筛选数据失败{page_type_list_brand}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal')
    def test_search_sales_data_object_list(self, sales_header):
        """ # 页面管理-内容管理-查询数据源列表 """
        # 测试数据源列表查询
        data_object_list = CentralContent().content_data_object_list(headers=sales_header)
        assert data_object_list.get("result") is True, f'查询数据源列表失败{data_object_list}'

        # 测试带关键词的数据源列表查询
        data_object_list_with_keyword = CentralContent().content_data_object_list(
            headers=sales_header,
            keyword="test",
            pageSize=10
        )
        assert data_object_list_with_keyword.get("result") is True, f'带关键词查询数据源列表失败{data_object_list_with_keyword}'


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net')

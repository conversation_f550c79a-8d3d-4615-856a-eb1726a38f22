# !/usr/bin/python3
# -*- coding: utf-8 -*-


import weeeTest

from test_dir.api.ec.central_portal.central_activity import CentralActivity


class TestSalesPopup(weeeTest.TestCase):
    def _sales_popup_list(self, sales_header):
        """ # 页面管理-弹出管理-查询popup list页面数据 """
        popup_list = CentralActivity().popup_list(headers=sales_header)
        assert len(popup_list["object"]["data"]) > 0, f'查询popup list页面数据为空{popup_list}'
        return popup_list

    @weeeTest.mark.list('sales', 'Transaction', 'test_search_sales_popup_list')
    def test_search_sales_popup_list(self, sales_header):
        """ # 页面管理-弹出管理-查询popup list页面数据 """
        popup_list = self._sales_popup_list(sales_header)
        assert len(popup_list["object"]["data"]) > 0, f'查询popup list页面数据为空{popup_list}'

    @weeeTest.mark.list('sales', 'Transaction', 'test_update_popup_status_popup')
    def test_update_popup_status_popup(self, sales_header):
        """ # 页面管理-弹出管理-更新popup状态 """
        popup_list = self._sales_popup_list(sales_header)
        # 随机更新一个popup
        popup_id = popup_list["object"]["data"][-1]["popup_id"]
        status = popup_list["object"]["data"][-1]["status"]
        if status == "X":
            update_status = "A"

        else:
            update_status = "X"
        # 更新状态
        popup_status = CentralActivity().popup_status(headers=sales_header,
                                                      popup_id=popup_id, status=update_status)

        assert popup_status["object"] == 1, f'更新popup状态失败{popup_status}'
        # 断言更新成功
        popup_list = self._sales_popup_list(sales_header)
        assert popup_list["object"]["data"][-1]["status"] == update_status, f'更新popup状态失败{popup_status}'

    @weeeTest.mark.list('sales', 'Transaction', 'test_popup_page_list')
    def test_popup_page_list(self, sales_header):
        """ # 页面管理-弹出管理-popup 详情页面-page列表 """
        popup_page_list = CentralActivity().popup_page_list(headers=sales_header)
        assert popup_page_list["object"] is not None, f'popup 详情页面-page列表{popup_page_list}'

    @weeeTest.mark.list('sales', 'Transaction')
    def test_popup_popup_option(self, sales_header):
        """ # 页面管理-弹出管理-popup 详情页面-popup_option """
        popup_option = CentralActivity().popup_option(headers=sales_header)
        assert popup_option["object"] is not None, f'popup 详情页面-popup_option{popup_option}'


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net')

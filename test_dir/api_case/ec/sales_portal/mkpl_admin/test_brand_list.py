# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
sales portal MKPL 品牌列表模块
"""
import weeeTest
from weeeTest import weeeConfig
from . import sales_header
from test_dir.api.ec.central_portal.central_mkpl import BrandList


class TestBrandList(weeeTest.TestCase):
    """
    品牌列表
    """

    @weeeTest.mark.list('sales', 'Transaction', 'MKPL', 'test_brand_list')
    def test_mkpl_brand_list(self):
        """
        Sales MKPL 品牌列表
        """
        brand = BrandList().brand_list(headers=sales_header)
        for item in brand['object']['data']:
            assert item['brand_key'] and isinstance(item['brand_key'], str), "brand_key is empty or not a string"
            assert item['brand_name_en'] and isinstance(item['brand_name_en'],
                                                        str), "brand_name_en is empty or not a string"

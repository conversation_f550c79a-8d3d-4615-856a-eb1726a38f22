"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :  test_grocery_to_cart.py
@Description    :  
@CreateTime     :  2023/11/20 16:52
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/11/20 16:52
"""
import json
import csv
import time

import pytest
import weeeTest

from test_dir.api.ec.ec_inventory.api_inventory import ApiInventory
from test_dir.api.ec.ec_item.product.pdp_detail import PdpDetail
from test_dir.api.ec.ec_item.search_v3.search_by_catalogue_content import SearchByCatalogueContent
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder
from test_dir.api.ec.ec_so.preorder.update_preorderline import UpdatePreOrderLine
from test_dir.api_case.ec.common.comm_check_products_with_cart import CommCheckProductsWithCart
from test_dir.api_case.ec.common.remove_all_products_in_cart import RemoveAllProductsInCart
from test_dir.api_case.ec.common.set_user_porder import SetUserPorder

sales_data = [
    (1, "32751", "Orlando"),
    (2, "33714", "Tampa"),
    (3, "33468", "Miami"),
    (4, "30014", "Atlanta"),
    (5, "94110", "BA-R1a"),
    (6, "95196", "BA-R1b"),
    (7, "95813", "BA-SAC"),
    (8, "95131", "BA-R2"),
    (9, "85066", "LA-PHX"),
    (10, "92103", "LA-SD"),
    (11, "91307", "LA-SB"),
    (12, "91403", "LA-LAX"),
    (13, "89146", "LA-LAS"),
    (14, "11420", "NewYork"),
    (15, "23292", "WashingtonDC"),
    (16, "02919", "Boston"),
    (17, "06824", "CT-Connecticut"),
    (18, "98034", "Seattle"),
    (19, "97202", "Portland"),
    (20, "77501", "Houston"),
    (21, "78716", "Austin"),
    (22, "75181", "Dallas"),
    (23, "78220", "TX-SanAntonio"),
    (24, "60143", "Chicago"),
    (25, "53717", "WI-Milwaukee"),
    (26, "47405", "IN-Indianapolis"),
    (27, "48823", "MI-Detroit"),
    (28, "63146", "MO-StLouis"),
    (38, "10016", "Manhattan"),
    (41, "92844", "LA-OC"),
    (42, "92331", "LA-East"),
    (56, "43082", "OH-Ohio"),
    (404, "32080", "JAX1"),
    (849, "95234", "BA-R1c"),
    (955, "15221", "PIT1"),
    (1047, "07946", "New-Jersey"),
    (1395, "73008", "OK-OKC"),
    (1446, "18938", "PA-Philadelphia"),
    (29, "57266", "MO-WEST"),
    (30, "04291", "MO-EAST"),
    (31, "17967", "MOF-NJ"),
    (32, "60401", "MOF-Midwest"),
    (39, "27242", "MOF-NE-2-days"),
    (1291, "94555", "B2B-SFBayArea"),
    (1513, "91008", "B2B-LA"),
    (1514, "92187", "B2B-SD")
]


class TestAllSalesRegionsCheckOut(weeeTest.TestCase):
    @pytest.fixture(scope='class', autouse=True)
    def setup_class(self, region_monitor_header):
        # 清空购物车
        # 1.2 清空购物车
        RemoveAllProductsInCart().remove_all_products_in_grocery_cart(headers=region_monitor_header)

    @pytest.mark.parametrize('region_id, zipcode,title', sales_data)
    @pytest.mark.region_monitor
    def test_all_sales_regions_data_check(self, region_id, zipcode, title, region_monitor_header):
        """
        不同销售组织下不同region下单数据验证
        """
        # 切换地址
        SetUserPorder.set_user_header_porder(headers=region_monitor_header, zipcode=zipcode, language="en")
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=region_monitor_header)["object"]
        deal_date = porder.get("delivery_pickup_date")
        deal_id = porder.get("deal_id")
        sales_org_id = porder.get("sales_org_id")
        zipcode = porder.get("zipcode")

        # 如果您需要对这些变量进行断言，确保它们不是 None 或者提供默认值
        assert deal_date is not None, "delivery_pickup_date is missing in the response"
        assert deal_id is not None, "deal_id is missing in the response"
        assert sales_org_id is not None, "sales_org_id is missing in the response"
        assert zipcode is not None, "zipcode is missing in the response"

        # 获取sale特殊分类本地可售商品
        normal = SearchByCatalogueContent().search_by_catalogue_content(
            headers=region_monitor_header,
            zipcode=zipcode, date=deal_date,
            filter_sub_category="sale",
            filters=json.dumps({"delivery_type": "delivery_type_local"})
        )
        try:
            assert normal["object"]["contents"], f"当前sale分类下没有本地售卖商品，请检查，为{normal['object']}"

        except Exception as e:
            time.sleep(60)
            normal = SearchByCatalogueContent().search_by_catalogue_content(
                headers=region_monitor_header,
                zipcode=zipcode, date=deal_date,
                filter_sub_category="sale",
                filters=json.dumps({"delivery_type": "delivery_type_local"})
            )
            assert normal["object"]["contents"], f"当前sale分类下没有本地售卖商品，为{normal['object']}，exception={str(e)}"

        available_grocery_products = [[item['data']['id'], item['data']['sales_min_order_quantity']] for item in
                                      normal["object"]["contents"] if
                                      item['type'] != 'carousel' and item["data"]["sold_status"] == "available"]

        assert available_grocery_products, f"当前{zipcode}sale分类下没有本地售卖商品，请检查，为{normal['object']}"
        # 加购生鲜商品
        for index, item in enumerate(available_grocery_products):
            porder_items = UpdatePreOrderLine().porder_items_v3(
                headers=region_monitor_header,
                product_id=item[0],
                quantity=item[1]
            )
            assert porder_items["result"] is True and len(
                porder_items["object"]["updateItems"]) > 0, f"product id is {item[0]}, response is {porder_items}"
            CommCheckProductsWithCart().check_product_exists_in_cart(
                headers=region_monitor_header,
                cart_domain="grocery",
                product_id=item[0]
            )
            if index == 1:
                break

        # 特殊地区履约商品检查
        if region_id in (14, 15, 16, 17, 38, 955, 1047, 1446):
            if region_id == 14:
                product_id = 70952
            elif region_id == 15:
                product_id = 43576
            elif region_id == 16:
                product_id = 44895
            elif region_id == 17:
                product_id = 23971
            elif region_id == 38:
                product_id = 41142
            elif region_id == 955:
                product_id = 90553
            elif region_id == 1047:
                product_id = 10895
            elif region_id == 1446:
                product_id = 100333

            pdp_detail = PdpDetail().pdp_detail(headers=region_monitor_header,
                                                product_id=product_id,
                                                zipcode=zipcode, sales_org_id=sales_org_id)

            inventory_v5 = ApiInventory().ec_inventory_query_v5(headers=region_monitor_header, product_id=product_id,
                                                               zipcode=zipcode, sales_org_id=sales_org_id,date=deal_date)
            assert pdp_detail["object"]["product"] is not None, f'该商品{product_id}未返回商品信息，请确认'
            assert inventory_v5["object"]["inventory"] is not None, f'该商品{product_id}未返回库存信息，请确认'
            if inventory_v5["object"]["inventory"]["qty"]> 0:
                assert pdp_detail["object"]["product"]["sold_status"] == "available", f'该商品{product_id}未返回商品信息，请确认'
                assert inventory_v5["object"]["is_sold_out"] is False, f'该商品{product_id}未返回库存信息，请确认'
                # 加购履约商品
                porder_items = UpdatePreOrderLine().porder_items_v3(
                    headers=region_monitor_header,
                    product_id=product_id, quantity=pdp_detail["object"]["product"]["min_order_quantity"]
                )
                assert porder_items["result"] is True and len(
                    porder_items["object"]["updateItems"]) > 0, f"product id is {product_id}, response is {porder_items}"
                CommCheckProductsWithCart().check_product_exists_in_cart(
                    headers=region_monitor_header, cart_domain="grocery", product_id=product_id
                )

                # RemoveAllProductsInCart().remove_all_products_in_grocery_cart(headers=region_monitor_header)

            else:
                assert pdp_detail["object"]["product"]["sold_status"] == "sold_out" or pdp_detail["object"]["product"][
                    "sold_status"] == "change_other_day", f'该商品{product_id}未返回商品信息，请确认'










        # # 加购Upsell商品
        # CommonBuy.buy_upsell_product(ec_login_header)
        #
        # # 使用参数化支付方式，产生一个待支付的订单
        # order_info = CommonPayment.pay_with_all_method(headers=region_monitor_header,
        #                                                payment_category="P", is_point=False)
        # order_ids = order_info["order_ids"]
        # assert order_ids, f"使用paypal支付，未产生订单号，接口返回结果为：{order_ids}"
        # # 取消待支付订单
        # cancel_order = CancelOrder().cancel_unpaid_order_new(headers=region_monitor_header, order_ids=order_ids)
        # assert cancel_order['object'] == 'success'

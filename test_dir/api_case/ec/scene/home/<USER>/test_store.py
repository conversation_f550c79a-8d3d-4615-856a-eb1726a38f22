# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_user.py
@Description    :
@CreateTime     :  2023/5/16 15:23
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 15:23
"""
import copy
import time

import weeeTest

from test_dir.api.ec.ec_item.store.api_store import ApiStore
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder
from test_dir.api_case.ec.common.set_user_porder import SetUserPorder


class TestStore(weeeTest.TestCase):
    @weeeTest.mark.list('test_store', 'Regression', 'Smoke',  'Transaction')
    def test_store(self, ec_login_header):
        """ 验证不同入口切换Store """
        # 获取用户的porder
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]
        # 查询所有store列表
        store_list_all = ApiStore().store_list_all(headers=ec_login_header)
        assert store_list_all["object"] is not None, f'store列表:{store_list_all}'

        # 查询sales_org =3下的store列表
        _header = copy.deepcopy(ec_login_header)
        SetUserPorder().set_user_zipcode(_header, zipcode="99348")
        store_list = ApiStore().store_list(headers=_header, zipcode=99348)
        assert len(store_list["object"]) == 0, f'store列表:{store_list["object"]}'
        SetUserPorder().set_user_zipcode(_header, zipcode="98011")
        # 查询sales_org =4下的store列表
        store_list = ApiStore().store_list(headers=ec_login_header, zipcode="98011")
        assert len(store_list["object"]) > 0, f'store列表:{store_list["object"]}'

        # 用户切换store
        for store in store_list["object"]:
            # 用户切换store
            store_select = ApiStore().store_select(headers=ec_login_header, store_id=store["store_id"])
            # 切换store接口可能比较慢，需要等待
            time.sleep(5)
            assert store_select["object"] is True, f'用户切换store失败:{store_select}'
            # 查询用户选择了哪个store信息
            is_select = ApiStore().store_is_select(headers=ec_login_header)
            assert is_select["object"] is not None
            assert is_select["object"]["select_store_id"] == store["store_id"]

        # 这里还有问题
        # 用户订阅store，添加提醒 ---这个接口还有问题
        # ApiStore().store_remind(headers=headers)
        # assert self.response["result"] is True
        # # 添加store mapping ---这个接口还有问题
        # ApiStore().store_mapping_batch_add(headers=headers)
        # assert self.response["result"] is True
        # # 更新store mapping ---这个接口还有问题
        # ApiStore().store_mapping_batch_update(headers=headers)
        # assert self.response["result"] is True


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.sayweee.net', env='tb1')

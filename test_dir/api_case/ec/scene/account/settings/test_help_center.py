# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_item.py
@Description    :
@CreateTime     :  2023/5/16 16:53
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 16:53
"""
import pytest
import weeeTest
from weeeTest import jmespath

from test_dir.api.ec.ec_cs.api_cs import ApiCs
from test_dir.api_case.ec.common.comm_cms import CommCheckFunction


class TestHelpCenter(weeeTest.TestCase):

    @weeeTest.mark.list('Regression', 'Smoke', 'Transaction','product')
    def test_check_help_center_page(self, ec_login_header):
        """ 帮助中心页面验证流程 """
        self_service_list = ApiCs().self_service_list(headers=ec_login_header)
        assert self_service_list["result"] is True, f'用户help_center信息返回异常，请确认{self_service_list}'
        assert self_service_list["object"] is not None, f'用户help_center信息返回异常，请确认{self_service_list}'
        assert len(self_service_list["object"]) == 6, f'用户help_center信息返回异常，请确认{self_service_list}'
        self.help_center_page_assert(self_service_list["object"], headers=ec_login_header)

    @weeeTest.mark.list('Regression', 'Transaction','product')
    def test_check_help_center_article_list(self, ec_login_header):
        """ 帮助中心页面-文章中心验证流程 """
        category = ApiCs().category_first_list(headers=ec_login_header)
        assert category["result"] is True, f'用户文章中心信息返回异常，请确认{category}'
        assert category["object"] is not None, f'用户文章中心信息返回异常，请确认{category}'
        assert len(category["object"]) > 0, f'用户文章中心信息返回异常，请确认{category}'
        for item in category["object"]:
            assert item["categoryId"] is not None, f'用户文章中心信息返回异常，请确认{category}'
            assert item["categoryName"] is not None
            article_list = ApiCs().article_list(headers=ec_login_header, categoryId=item["categoryId"])
            assert article_list["result"] is True
            assert len(article_list["object"]) > 0, f'用户文章中心信息返回异常，请确认{article_list}'
            for item2 in article_list["object"]:
                assert item2["deskArticleId"] is not None, f'用户文章中心信息返回异常，请确认{article_list}'
                assert item2["title"] is not None, f'用户文章中心信息返回异常，请确认{article_list}'
                assert item2["url"] is not None, f'用户文章中心信息返回异常，请确认{article_list}'
                assert "/help/detail/" + str(item2["deskArticleId"]) in item2[
                    "url"], f'用户文章中心信息返回异常，请确认{article_list}'
                CommCheckFunction().comm_check_link(item2["url"], headers=ec_login_header)

    @weeeTest.mark.list('Regression', 'Transaction','product')
    @pytest.mark.parametrize("key_word", ["地址"])
    def test_check_help_center_search_article(self, key_word, ec_login_header):
        """ 帮助中心页面-文章中心搜证验证流程 """
        search_article = ApiCs().search_article(headers=ec_login_header, keywords=key_word)
        assert search_article["result"] is True, f'用户文章中心搜索信息返回异常，请确认{search_article}'
        assert search_article["object"] is not None, f'用户文章中心搜索信息返回异常，请确认{search_article}'
        assert len(search_article["object"]) > 0, f'用户文章中心搜索信息返回异常，请确认{search_article}'
        for item in search_article["object"]:
            assert item["deskArticleId"] is not None, f'用户文章中心信息返回异常，请确认{search_article}'
            assert item["title"] is not None, f'用户文章中心信息返回异常，请确认{search_article}'
            assert item["url"] is not None, f'用户文章中心信息返回异常，请确认{search_article}'

    @weeeTest.mark.list('Regression', 'product')
    def test_article_detail(self, ec_login_header):
        """ 测试获取文章详情 """
        response = ApiCs().article_detail(headers=ec_login_header, desk_article_id="842")
        assert response["result"] , f"Expected True, got {response['result']}"
        assert jmespath(response, "object.articleId") == 842
        assert "How can I check order information" in jmespath(response, "object.title")

    def help_center_page_assert(self, self_service_list, headers):
        for item in self_service_list:
            assert item["icon"] is not None, f'用户help_center信息返回异常，请确认{self_service_list}'
            assert item["id"] is not None, f'用户help_center信息返回异常，请确认{self_service_list}'
            assert item["title"] is not None, f'用户help_center信息返回异常，请确认{self_service_list}'
            assert item["url"] is not None, f'用户help_center信息返回异常，请确认{self_service_list}'
            if item["id"] == 107:
                # "退货&退款"
                assert "/order_case" in item["url"], f'用户help_center信息返回异常，请确认{self_service_list}'
                assert item["priority"] == 1, f'用户help_center信息返回异常，请确认{self_service_list}'
            elif item["id"] == 108:
                # "联系客服"
                assert "/account/case" in item["url"], f'用户help_center信息返回异常，请确认{self_service_list}'
                assert item["priority"] == 2, f'用户help_center信息返回异常，请确认{self_service_list}'

            elif item["id"] == 109:
                # "订单查询"
                assert "/account/my_orders" in item["url"], f'用户help_center信息返回异常，请确认{self_service_list}'
                assert item["priority"] == 3, f'用户help_center信息返回异常，请确认{self_service_list}'

            elif item["id"] == 111:
                # "账户与安全"
                assert "/account/settings" in item["url"], f'用户help_center信息返回异常，请确认{self_service_list}'
                assert item["priority"] == 4, f'用户help_center信息返回异常，请确认{self_service_list}'

            elif item["id"] == 112:
                # "新手指南"
                assert "/help/detail" in item["url"], f'用户help_center信息返回异常，请确认{self_service_list}'
                assert item["priority"] == 5, f'用户help_center信息返回异常，请确认{self_service_list}'

            elif item["id"] == 110:
                # ""合作伙伴""
                assert "/account/contact-us" in item["url"], f'用户help_center信息返回异常，请确认{self_service_list}'
                assert item["priority"] == 6, f'用户help_center信息返回异常，请确认{self_service_list}'

            CommCheckFunction().comm_check_link(item["url"], headers=headers)

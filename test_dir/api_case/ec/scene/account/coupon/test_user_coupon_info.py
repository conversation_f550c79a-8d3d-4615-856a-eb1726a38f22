"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@Description    :
@CreateTime     :  2024/3/27
@Software       :  PyCharm
------------------------------------
"""
import pytest
import weeeTest
from test_dir.api.ec.ec_promotion.coupon.get_coupon_info import Couponinfo
from test_dir.api_case.ec.common.comm_cms import CommCheckFunction


class TestGetUserCouponInfo(weeeTest.TestCase):
    @pytest.mark.parametrize("status", ['A', 'U', 'E'])
    @weeeTest.mark.list('B2B', 'Social', 'Smoke', 'Transaction')
    def test_get_user_coupon_info(self, status, ec_login_header):
        """获取用户优惠券信息"""
        my_coupon_info = Couponinfo().get_coupon_info(headers=ec_login_header, status=status)
        assert my_coupon_info['result'] is True, f"my_coupon_info为：{my_coupon_info}"
        assert isinstance(my_coupon_info['object']['coupon_list'], list)
        coupon_list = my_coupon_info['object']['coupon_list']
        if status == "A":
            if len(coupon_list) > 0:
                for index, item in enumerate(coupon_list):
                    self.check_coupon_assert(item, status, headers=ec_login_header)
                    if index == 5:
                        break
        elif status == "U":
            if len(coupon_list) > 0:
                self.check_coupon_assert(coupon_list, status, headers=ec_login_header)

        elif status == "E":
            if len(coupon_list) > 0:
                for index, item in enumerate(coupon_list):
                    self.check_coupon_assert(item, status, headers=ec_login_header)
                    if index == 5:
                        break

    def check_coupon_assert(self, coupon_list, status, headers):
        assert coupon_list["code"] is not None, f'优惠券信息异常{coupon_list}'
        assert coupon_list["coupon_plan_id"] is not None, f'优惠券信息异常{coupon_list}'
        assert coupon_list["discount"] is not None, f'优惠券信息异常{coupon_list}'
        assert coupon_list["expiration_timestamp"] is not None, f'优惠券信息异常{coupon_list}'
        assert coupon_list["group_id"] is not None, f'优惠券信息异常{coupon_list}'
        assert coupon_list["id"] is not None, f'优惠券信息异常{coupon_list}'
        assert coupon_list["rule_info"] is not None, f'优惠券信息异常{coupon_list}'
        # if coupon_list["type"] == "D":
        #     # 点击立即使用
        #     CommCheckFunction().comm_check_link(coupon_list["rule_info"]["link"], headers=headers), f'优惠券{coupon_list["id"]}信息异常{coupon_list}'
        assert coupon_list["start_timestamp"] is not None, f'优惠券信息异常{coupon_list}'
        assert coupon_list["title"] is not None, f'优惠券信息异常{coupon_list}'
        assert coupon_list["type"] is not None, f'优惠券信息异常{coupon_list}'
        if status == "E":
            assert coupon_list["invalid_message"] is not None, f'优惠券信息异常{coupon_list}'

import json

import allure
import pytest
import weeeTest
from weeeTest import log

from test_dir.api_case.ec.datatrack.base_case import BaseCase
from test_dir.api_case.ec.datatrack.collection_page_track_check_api.track_verification_collection_page_v2 import \
    TrackVerificationCollectionPageV2 as TVCPV2
from test_dir.api_case.ec.datatrack.datatrack_common_function import TrackCommonVerify


@allure.story("cart页面埋点操作与校验V2")
class TestCartPageTrackV2(weeeTest.TestCase, BaseCase):
    # cart页面track
    pytestmark = [pytest.mark.firstpagelevel, pytest.mark.clickhouse, pytest.mark.carttrack]
    platform_and_page_key = [("MWeb", "mweb_cart"), ("Android", "android_cart"), ("iOS", "ios_cart")]

    @allure.step(
        "cart页面浏览mkpl拼单（group order）的banner ,t2_banner_imp埋点校验: platform={platform},page_key={page_key},banner_type=seller_group_order")
    @pytest.mark.parametrize("platform, page_key", platform_and_page_key)
    def test_0110008_cart_track_t2_banner_imp_group_order(self, platform, page_key, clickhouse_client,
                                                          time_range):
        sql = f"""
                  select JSONExtractRaw(params, 'mod_nm') as a, JSONExtractRaw(JSONExtractRaw(params, 'co'), 'banner_type') as b, JSONExtractRaw(JSONExtractRaw(params, 'co'), 'url') as c  from weee_data.data_tracking_all  where __time>='{time_range.get("begin")}' and __time<='{time_range.get("end")}' and l0_platform='{platform}' and l0_page_key='{page_key}' and a='null' and b='"seller_group_order"' and c!='""' and l0_event_type='t2_banner_imp' limit 100
               """
        log.info("sql===> " + sql)
        result_rows = self.query_track(client=clickhouse_client, sql=sql).result_rows
        log.info("length===> " + str(len(result_rows)))

        assert result_rows, f"cart页面浏览mkpl拼单 banner上报t2_banner_imp,t2_banner_imp埋点校验: platform={platform},page_key={page_key},banner_type=seller_group_order的埋点"

    @allure.step(
        "cart页面点击购物车顶部group order,t2_click_action埋点校验: platform={platform},page_key={page_key},target_type=banner_line,target_nm=seller_group_order")
    @pytest.mark.parametrize("platform, page_key", platform_and_page_key)
    def test_0110000_cart_track_t2_click_action_group_order(self, platform, page_key, clickhouse_client, time_range):
        sql = f"""
                  select JSONExtractRaw(JSONExtractRaw(params, 'co'), 'target_type') as a, JSONExtractRaw(JSONExtractRaw(params, 'co'), 'target_nm') as b ,JSONExtractRaw(JSONExtractRaw(params, 'ctx'),'global_vendor') as c from weee_data.data_tracking_all where   __time>='{time_range.get("begin")}' and __time<='{time_range.get("end")}' and l0_platform='{platform}' and l0_page_key='{page_key}'and  a='"banner_line"' and b='"seller_group_order"' and c!='""'  and l0_event_type='t2_click_action'  limit 100
               """
        log.info("sql===> " + sql)
        result_rows = self.query_track(client=clickhouse_client, sql=sql).result_rows
        log.info("length===> " + str(len(result_rows)))

        assert result_rows, f"未找到-cart页面点击购物车顶部group order,t2_click_action埋点: platform={platform},page_key={page_key},target_type=banner_line,target_nm=seller_group_order"

    @allure.step(
        "cart页面浏览loytal升级的banner ,t2_banner_imp埋点校验: platform={platform},page_key={page_key},banner_type=message,mod_nm=rewards_anncmnt")
    @pytest.mark.parametrize("platform, page_key", platform_and_page_key)
    def test_0110009_cart_track_t2_banner_imp_loytal(self, platform, page_key, clickhouse_client,
                                                     time_range):
        sql = f"""
                     select JSONExtractRaw(params, 'mod_nm') as a, JSONExtractRaw(JSONExtractRaw(params, 'co'), 'banner_type') as b, JSONExtractRaw(JSONExtractRaw(params, 'co'), 'url') as c  from weee_data.data_tracking_all  where __time>='{time_range.get("begin")}' and __time<='{time_range.get("end")}' and l0_platform='{platform}' and l0_page_key='{page_key}' and a='"rewards_anncmnt"' and b='"message"' and c!='""' and l0_event_type='t2_banner_imp' limit 100
                  """
        log.info("sql===> " + sql)
        result_rows = self.query_track(client=clickhouse_client, sql=sql).result_rows
        log.info("length===> " + str(len(result_rows)))

        assert result_rows, f"cart页面浏览loytal会员升级banner上报t2_banner_imp,t2_banner_imp埋点校验: platform={platform},page_key={page_key},banner_type=message的埋点"

    @allure.step(
        "cart点击loytal升级的banner上报t2_click_action ,t2_click_action埋点校验: platform={platform},page_key={page_key},target_type=message,mod_nm=rewards_anncmnt")
    @pytest.mark.parametrize("platform, page_key", platform_and_page_key)
    def test_0110010_cart_track_t2_click_action_loytal(self, platform, page_key, clickhouse_client,
                                                       time_range):
        # 这个埋点安卓很少，用9.3号数据查询，只有1条，用9.2数据，安卓0条
        sql = f"""
                 select JSONExtractRaw(params, 'mod_nm') as a, JSONExtractRaw(JSONExtractRaw(params, 'co'), 'target_type') as b, JSONExtractRaw(JSONExtractRaw(params, 'co'), 'click_type') as c  from weee_data.data_tracking_all  where __time>='{time_range.get("begin")}' and __time<='{time_range.get("end")}' and l0_platform='{platform}' and l0_page_key='{page_key}' and a='"rewards_anncmnt"' and b='"message"' and c='"view"' and l0_event_type='t2_click_action' limit 100
              """
        log.info("sql===> " + sql)
        result_rows = self.query_track(client=clickhouse_client, sql=sql).result_rows
        log.info("length===> " + str(len(result_rows)))

        assert result_rows, f"cart页面点击loytal会员升级banner上报t2_click_action,t2_click_action埋点校验: platform={platform},page_key={page_key},target_type=message,click_type=view的埋点"

    @allure.step(
        "cart页面浏览add on的banner ,t2_banner_imp埋点校验: platform={platform},page_key={page_key},banner_type=add_on")
    @pytest.mark.parametrize("platform, page_key", platform_and_page_key)
    def test_0110007_cart_track_t2_banner_imp_addon(self, platform, page_key, clickhouse_client,
                                                    time_range):
        sql = f"""
                    select JSONExtractRaw(params, 'mod_nm') as a, JSONExtractRaw(JSONExtractRaw(params, 'co'), 'banner_type') as b, JSONExtractRaw(JSONExtractRaw(params, 'co'), 'url') as c  from weee_data.data_tracking_all  where __time>='{time_range.get("begin")}' and __time<='{time_range.get("end")}' and l0_platform='{platform}' and l0_page_key='{page_key}' and a='null' and b='"add_on"' and c!='""' and l0_event_type='t2_banner_imp' limit 100
                 """
        log.info("sql===> " + sql)
        result_rows = self.query_track(client=clickhouse_client, sql=sql).result_rows
        log.info("length===> " + str(len(result_rows)))

        assert result_rows, f"cart页面浏览add on banner上报t2_banner_imp,t2_banner_imp埋点校验: platform={platform},page_key={page_key},banner_type=add_on的埋点"

    @allure.step(
        "cart浏览免运费的banner上报t2_banner_imp ,t2_banner_imp埋点校验: platform={platform},page_key={page_key},banner_type=banner_line,mod_nm=cart,banner_key=free_delivery_info")
    @pytest.mark.parametrize("platform, page_key", platform_and_page_key)
    def test_0110011_cart_track_t2_banner_imp_freefee(self, platform, page_key, clickhouse_client,
                                                      time_range):
        sql = f"""
                               select JSONExtractRaw(params, 'mod_nm') as a, JSONExtractRaw(JSONExtractRaw(params, 'co'), 'banner_type') as b, JSONExtractRaw(JSONExtractRaw(params, 'co'), 'banner_key') as c  from weee_data.data_tracking_all  where __time>='{time_range.get("begin")}' and __time<='{time_range.get("end")}' and l0_platform='{platform}' and l0_page_key='{page_key}' and a='"cart"' and b='"banner_line"' and c='"free_delivery_info"' and l0_event_type='t2_banner_imp' limit 100
                            """
        log.info("sql===> " + sql)
        result_rows = self.query_track(client=clickhouse_client, sql=sql).result_rows
        log.info("length===> " + str(len(result_rows)))

        assert result_rows, f"cart页面浏览免运费banner上报t2_banner_imp,t2_banner_impn埋点校验: platform={platform},page_key={page_key},banner_type=banner_line,banner_key=free_delivery_info的埋点"

    @allure.step(
        "cart点击免运费的banner右侧的i上报t2_click_action  ,t2_click_action 埋点校验: platform={platform},page_key={page_key},target_nm=free_delivery_info,target_type=banner_line,click_type=view")
    @pytest.mark.parametrize("platform, page_key", platform_and_page_key)
    def test_0110012_cart_track_t2_click_action_i(self, platform, page_key, clickhouse_client,
                                                  time_range):
        sql = f"""
                    select JSONExtractRaw(params, 'mod_nm') as a, JSONExtractRaw(JSONExtractRaw(params, 'co'), 'target_nm') as b, JSONExtractRaw(JSONExtractRaw(params, 'co'), 'target_type') as c ,JSONExtractRaw(JSONExtractRaw(params, 'co'), 'click_type') as d ,JSONExtractRaw(JSONExtractRaw(params), 'sec_nm') as e from weee_data.data_tracking_all  where __time>='{time_range.get("begin")}' and __time<='{time_range.get("end")}' and l0_platform='{platform}' and l0_page_key='{page_key}' and a='"cart"' and b='"free_delivery_info"' and c='"banner_line"' and d='"view"' and e ='"normal"'and l0_event_type='t2_click_action' limit 100
                 """
        log.info("sql===> " + sql)
        result_rows = self.query_track(client=clickhouse_client, sql=sql).result_rows
        log.info("length===> " + str(len(result_rows)))

        assert result_rows, f"cart页面点击免运费banner右侧的i上报t2_click_action,t2_click_action埋点校验: platform={platform},page_key={page_key},target_nm=free_delivery_info,target_type=banner_line,click_type=view的埋点"

    @allure.step(
        "cart点击免运费的banner右侧的i弹窗上报t2_popup  ,t2_popup 埋点校验: platform={platform},page_key={page_key},mod_nm=rewards_program_shipping_tip_popup,name=free_shipping_upsell,action=view")
    @pytest.mark.parametrize("platform, page_key", platform_and_page_key)
    def test_0110013_cart_track_t2_popup_i(self, platform, page_key, clickhouse_client,
                                           time_range):
        sql = f"""
                    select JSONExtractRaw(params, 'mod_nm') as a, JSONExtractRaw(JSONExtractRaw(params, 'co'), 'name') as b, JSONExtractRaw(JSONExtractRaw(params, 'co'), 'action') as c from weee_data.data_tracking_all  where __time>='{time_range.get("begin")}' and __time<='{time_range.get("end")}' and l0_platform='{platform}' and l0_page_key='{page_key}' and a='"rewards_program_shipping_tip_popup"' and b='"free_shipping_upsell"' and c='"view"' and l0_event_type='t2_popup' limit 100
                 """
        log.info("sql===> " + sql)
        result_rows = self.query_track(client=clickhouse_client, sql=sql).result_rows
        log.info("length===> " + str(len(result_rows)))

        assert result_rows, f"cart页面点击免运费banner右侧的i弹出弹窗上报t2_popup,t2_popup埋点校验: platform={platform},page_key={page_key},mod_nm=rewards_program_shipping_tip_popup,name=free_shipping_upsell,action=view的埋点"

    @allure.step(
        "cart金银用户不满足运费点击划线运费右侧的i上报t2_click_action  ,t2_click_action 埋点校验: platform={platform},page_key={page_key},sec_nm=normal,target_nm=shipping_fee,click_type=view,target_type=normal_button")
    @pytest.mark.parametrize("platform, page_key", platform_and_page_key)
    def test_0110014_cart_track_t2_click_action_delivery_fee_i(self, platform, page_key, clickhouse_client,
                                                               time_range):
        sql = f"""
                    select JSONExtractRaw(params, 'mod_nm') as a, JSONExtractRaw(JSONExtractRaw(params), 'sec_nm') as b, JSONExtractRaw(JSONExtractRaw(params, 'co'), 'target_nm') as c, JSONExtractRaw(JSONExtractRaw(params, 'co'), 'click_type') as d,JSONExtractRaw(JSONExtractRaw(params, 'co'), 'target_type') as e from weee_data.data_tracking_all  where __time>='{time_range.get("begin")}' and __time<='{time_range.get("end")}' and l0_platform='{platform}' and l0_page_key='{page_key}' and a='"cart"' and b='"normal"' and c='"shipping_fee"'and d='"view"'and e='"normal_button"' and l0_event_type='t2_click_action' limit 100
                 """
        log.info("sql===> " + sql)
        result_rows = self.query_track(client=clickhouse_client, sql=sql).result_rows
        log.info("length===> " + str(len(result_rows)))

        assert result_rows, f"cart金银用户不满足运费点击划线运费右侧的i上报t2_click_action,t2_click_action埋点校验: platform={platform},page_key={page_key},sec_nm=normal,target_nm=shipping_fee,click_type=view,target_type=normal_button的埋点"

    # @allure.step(
    #     "cart页面点击购物车顶部rewards banner,t2_cart_action埋点校验: platform={platform},page_key={page_key},target_type=message,mod_nm=rewards_anncmnt")
    # 替换成10的case
    # @pytest.mark.clickhouse
    # @pytest.mark.parametrize("platform, page_key", platform_and_page_key)
    # def test_0110001_cart_track_t2_cart_action_rewards(self, platform, page_key, clickhouse_client, time_range):
    #     # todo 目前未搜索到数据，埋点数据可能有误
    #     sql = f"""
    #               select JSONExtractRaw(params, 'mod_nm') as a, JSONExtractRaw(JSONExtractRaw(params, 'co'), 'target_type') as b  from weee_data.data_tracking_all  where __time>='{time_range.get("begin")}' and __time<='{time_range.get("end")}' and l0_platform='{platform}' and l0_page_key='{page_key}' and l0_event_type='t2_click_action' and a='"rewards_anncmnt"' and b='"message"' limit 100
    #            """
    #     log.info("sql===> " + sql)
    #     result_rows = self.query_track(client=clickhouse_client, sql=sql).result_rows
    #     log.info("length===> " + str(len(result_rows)))
    #
    #     assert result_rows, f"未找到-cart页面点击购物车顶部rewards banner,t2_cart_action埋点校验: platform={platform},page_key={page_key},target_type=message,mod_nm=rewards_anncmnt"

    # @allure.step(
    #     "cart页面点击购物车免运费banner,t2_cart_action埋点校验: platform={platform},page_key={page_key},target_type=banner_line,mod_nm=cart")
    # @pytest.mark.clickhouse
    # @pytest.mark.parametrize("platform, page_key", platform_and_page_key)
    # def test_0110002_cart_track_t2_cart_action_rewards(self, platform, page_key, clickhouse_client, time_range):
    #     sql = f"""
    #               select JSONExtractRaw(params, 'mod_nm') as a, JSONExtractRaw(JSONExtractRaw(params, 'co'), 'target_type') as b, JSONExtractRaw(JSONExtractRaw(params, 'co'), 'target_nm') as c  from weee_data.data_tracking_all  where __time>='{time_range.get("begin")}' and __time<='{time_range.get("end")}' and l0_platform='{platform}' and l0_page_key='{page_key}' and l0_event_type='t2_click_action' and a='"cart"' and b='"banner_line"' and c='"free_delivery_info"' limit 100
    #            """
    #     log.info("sql===> " + sql)
    #     result_rows = self.query_track(client=clickhouse_client, sql=sql).result_rows
    #     log.info("length===> " + str(len(result_rows)))
    #
    #     assert result_rows, f"未找到-cart页面点击购物车免运费banner,t2_cart_action埋点校验: platform={platform},page_key={page_key},target_type=banner_line,mod_nm=cart的埋点"

    @allure.step(
        "cart页面点击购物车商品+or-,t2_cart_action埋点校验: platform={platform},page_key={page_key},sec_nm={sec_nm}")
    @pytest.mark.parametrize("platform, page_key", platform_and_page_key)
    @pytest.mark.parametrize("sec_nm", ["normal", "pantry", "seller"])
    # @pytest.mark.parametrize("source", ["cart"])
    def test_0110003_cart_track_t2_cart_action_add(self, platform, page_key, sec_nm, clickhouse_client,
                                                   time_range):
        sql = f"""
                      select JSONExtractRaw(params, 'mod_nm') as a, JSONExtractRaw(params, 'sec_nm') as b, JSONExtractRaw(JSONExtractRaw(params, 'co'), 'prod_id') as c, JSONExtractRaw(JSONExtractRaw(params, 'co'), 'source') as d  from weee_data.data_tracking_all  where __time>='{time_range.get("begin")}' and __time<='{time_range.get("end")}' and l0_platform='{platform}' and l0_page_key='{page_key}' and a='"cart"' and b='"{sec_nm}"' and c!='""' and d!='""' limit 100
                   """
        log.info("sql===> " + sql)
        result_rows = self.query_track(client=clickhouse_client, sql=sql).result_rows
        log.info("length===> " + str(len(result_rows)))

        assert result_rows, f"未找到-cart页面点击购物车商品+or-,t2_cart_action埋点校验: platform={platform},page_key={page_key},sec_nm={sec_nm}的埋点"

    @allure.step(
        "cart页面点击购物车里普通商品的删除按钮,t2_click_action埋点校验: platform={platform},page_key={page_key},target_nm=remove,sec_nm={sec_nm}")
    @pytest.mark.parametrize("platform, page_key", platform_and_page_key)
    @pytest.mark.parametrize("sec_nm", ["normal", "pantry", "seller"])
    # @pytest.mark.parametrize("click_type", ["remove"])
    def test_0110004_cart_track_t2_click_action_normal_remove(self, platform, page_key, sec_nm, clickhouse_client,
                                                              time_range):
        sql = f"""
                  select JSONExtractRaw(params, 'mod_nm') as a, JSONExtractRaw(params, 'sec_nm') as b, JSONExtractRaw(JSONExtractRaw(params, 'co'), 'target_nm') as c, JSONExtractRaw(JSONExtractRaw(params, 'co'), 'target_type') as d , JSONExtractRaw(JSONExtractRaw(params, 'co'), 'click_type') as e from weee_data.data_tracking_all  where __time>='{time_range.get("begin")}' and __time<='{time_range.get("end")}' and l0_platform='{platform}' and l0_page_key='{page_key}' and a='"cart"' and b='"{sec_nm}"' and c!='""' and d='"product"' and e='"remove"' and l0_event_type='t2_click_action' limit 100
               """
        log.info("sql===> " + sql)
        result_rows = self.query_track(client=clickhouse_client, sql=sql).result_rows
        log.info("length===> " + str(len(result_rows)))

    @allure.step(
        "cart页面点击购物车里普通商品的删除按钮,t2_cart_action埋点校验: platform={platform},page_key={page_key},biz_type={biz_type},source!='',sec_nm={sec_nm}")
    @pytest.mark.parametrize("platform, page_key", platform_and_page_key)
    @pytest.mark.parametrize("sec_nm,biz_type", [("normal", "normal"), ("pantry", "normal"), ("seller", "seller")])
    @pytest.mark.parametrize("source", ["cart", "trade_in", "app_cart-bought"])
    def test_0110006_cart_track_t2_cart_action_normal_remove(self, platform, page_key, biz_type, source, sec_nm,
                                                             clickhouse_client,
                                                             time_range):
        sql = f"""
                  select JSONExtractRaw(params, 'mod_nm') as a, JSONExtractRaw(params, 'sec_nm') as b, JSONExtractRaw(JSONExtractRaw(params, 'co'), 'biz_type') as c, JSONExtractRaw(JSONExtractRaw(params, 'co'), 'source') as d  from weee_data.data_tracking_all  where __time>='{time_range.get("begin")}' and __time<='{time_range.get("end")}' and l0_platform='{platform}' and l0_page_key='{page_key}' and a='"cart"' and b='"{sec_nm}"' and c='"{biz_type}"' and d !='' and l0_event_type='t2_cart_action'  limit 100
               """
        print("sql", sql)
        log.info("sql===> " + sql)
        result_rows = self.query_track(client=clickhouse_client, sql=sql).result_rows
        log.info("length===> " + str(len(result_rows)))

        assert result_rows, f"未找到-cart页面点击购物车里普通商品的删除按钮,t2_cart_action埋点校验: platform={platform},page_key={page_key},target_nm=remove,sec_nm={sec_nm}的埋点"

    @allure.step(
        "cart页面点击购物车里换购商品的删除按钮,t2_cart_action埋点校验: platform={platform},page_key={page_key},source=trade_in,sec_nm={sec_nm}")
    @pytest.mark.parametrize("platform, page_key", platform_and_page_key)
    @pytest.mark.parametrize("sec_nm", ["normal", "pantry"])
    def test_0110005_cart_track_t2_cart_action_trade_in_remove(self, platform, page_key, sec_nm, clickhouse_client,
                                                               time_range):
        if platform == 'iOS':
            sql = f"""
                      select JSONExtractRaw(params, 'mod_nm') as a, JSONExtractRaw(params, 'sec_nm') as b, JSONExtractRaw(JSONExtractRaw(params, 'co'), 'prod_id') as c, JSONExtractRaw(JSONExtractRaw(params, 'co'), 'source') as d  from weee_data.data_tracking_all  where __time>='{time_range.get("begin")}' and __time<='{time_range.get("end")}' and l0_platform='{platform}' and l0_page_key='{page_key}' and a='"cart"' and b='"{sec_nm}"' and c!='' and d='"trade_in"' and l0_event_type='t2_cart_action'    limit 100
                   """
            log.info("sql===> " + sql)
            result_rows = self.query_track(client=clickhouse_client, sql=sql).result_rows
            log.info("length===> " + str(len(result_rows)))

            assert result_rows, f"cart页面点击购物车里换购商品的删除按钮,t2_cart_action埋点校验: platform={platform},page_key={page_key},source=trade_in,sec_nm={sec_nm}的埋点"
        else:
            pytest.skip("0110005 mweb, android无数据，此用例跳过")
    @allure.step(
        "cart页面点击购物车里换购商品的删除按钮,t2_click_action埋点校验: platform={platform},page_key={page_key},click_type=remove,target_type=product,sec_nm={sec_nm}")
    @pytest.mark.parametrize("platform, page_key", platform_and_page_key)
    @pytest.mark.parametrize("sec_nm", ["normal", "pantry"])
    def test_0110016_cart_t2_click_action_trade_in_remove(self, platform, page_key, sec_nm, clickhouse_client,
                                                          time_range):
        sql = f"""
                  select JSONExtractRaw(params, 'mod_nm') as a, JSONExtractRaw(params, 'sec_nm') as b, JSONExtractRaw(JSONExtractRaw(params, 'co'), 'target_nm') as c, JSONExtractRaw(JSONExtractRaw(params, 'co'), 'target_type') as d , JSONExtractRaw(JSONExtractRaw(params, 'co'), 'click_type') as e from weee_data.data_tracking_all  where __time>='{time_range.get("begin")}' and __time<='{time_range.get("end")}' and l0_platform='{platform}' and l0_page_key='{page_key}' and a='"cart"' and b='"{sec_nm}"' and c!='""' and d='"product"' and e='"remove"' and l0_event_type='t2_click_action' limit 100
               """
        log.info("sql===> " + sql)
        result_rows = self.query_track(client=clickhouse_client, sql=sql).result_rows
        log.info("length===> " + str(len(result_rows)))

        assert result_rows, f"cart页面点击购物车里换购商品的删除按钮,t2_click_action埋点校验: platform={platform},page_key={page_key},click_type=remove,target_type=product,sec_nm={sec_nm}的埋点"

    @allure.step(
        "cart页面点击mkpl购物车的i弹出mkpl介绍弹窗,t2_popup埋点校验: platform={platform},page_key={page_key},name=mkpl_introduction,action=view")
    @pytest.mark.parametrize("platform, page_key", platform_and_page_key)
    def test_0110015_cart_track_t2_popup_mkpl_i(self, platform, page_key, clickhouse_client,
                                                time_range):
        sql = f"""
                 select JSONExtractRaw(params, 'mod_nm') as a, JSONExtractRaw(params, 'sec_nm') as b,  JSONExtractRaw(JSONExtractRaw(params, 'co'), 'action') as d,JSONExtractRaw(JSONExtractRaw(params, 'co'), 'name') as e , JSONExtractRaw(JSONExtractRaw(JSONExtractRaw(params, 'co'), 'other_parameter'), 'vendor_id') as f from weee_data.data_tracking_all   where __time>='{time_range.get("begin")}' and __time<='{time_range.get("end")}' and l0_platform='{platform}' and l0_page_key='{page_key}' and a='null' and b='null'  and d='"view"'and e='"mkpl_introduction"' and f!=''  and l0_event_type='t2_popup'  limit 100
               """
        log.info("sql===> " + sql)
        result_rows = self.query_track(client=clickhouse_client, sql=sql).result_rows
        log.info("length===> " + str(len(result_rows)))

        assert result_rows, f"cart页面点击mkpl购物车的i弹出mkpl介绍弹窗,t2_popup埋点校验: platform={platform},page_key={page_key},name=mkpl_introduction,action=view的埋点"

    # JSONExtractRaw(JSONExtractRaw(params, 'co'), 'vendor_id') as c,
    @allure.step(
        "cart页面点击商品加入到稍后再买,t2_click_action埋点校验: platform={platform},page_key={page_key},sec_nm={sec_nm},click_type=save,target_type=product")
    @pytest.mark.parametrize("platform, page_key", platform_and_page_key)
    @pytest.mark.parametrize("sec_nm", ["normal", "pantry", "seller"])
    def test_0110017_cart_track_t2_click_action_cart_to_saveforlater(self, platform, page_key, sec_nm,
                                                                     clickhouse_client,
                                                                     time_range):
        sql = f"""
                 select JSONExtractRaw(params, 'mod_nm') as a, JSONExtractRaw(params, 'sec_nm') as b,  JSONExtractRaw(JSONExtractRaw(params, 'co'), 'click_type') as c,JSONExtractRaw(JSONExtractRaw(params, 'co'), 'target_type') as d , JSONExtractRaw(JSONExtractRaw(params, 'co'), 'target_nm') as e from weee_data.data_tracking_all   where __time>='{time_range.get("begin")}' and __time<='{time_range.get("end")}' and l0_platform='{platform}' and l0_page_key='{page_key}' and a='"cart"' and b='"{sec_nm}"'  and c='"save"'and d='"product"' and e!='""'  and l0_event_type='t2_click_action'  limit 100
               """
        log.info("sql===> " + sql)
        result_rows = self.query_track(client=clickhouse_client, sql=sql).result_rows
        log.info("length===> " + str(len(result_rows)))

        assert result_rows, f"cart页面点击商品加入到稍后再买,t2_click_action埋点校验: platform={platform},page_key={page_key},sec_nm={sec_nm},click_type=save,target_type=product的埋点"

    @allure.step(
        "cart页面点击稍后再买商品的删除按钮,t2_cart_action埋点校验: platform={platform},page_key={page_key},refer_type={refer_type}")
    @pytest.mark.parametrize("platform, page_key", platform_and_page_key)
    @pytest.mark.parametrize("refer_type", ["normal", "pantry", "seller"])
    def test_0110018_cart_track_t2_cart_action_save4later_remove(self, platform, page_key, refer_type,
                                                                 clickhouse_client,
                                                                 time_range):
        sql = f"""
                  select JSONExtractRaw(params, 'mod_nm') as a, JSONExtractRaw(params, 'sec_nm') as b, JSONExtractRaw(JSONExtractRaw(params, 'co'), 'refer_type') as c from weee_data.data_tracking_all  where __time>='{time_range.get("begin")}' and __time<='{time_range.get("end")}' and l0_platform='{platform}' and l0_page_key='{page_key}' and a='"cart"' and b!='null' and c='"{refer_type}"' and l0_event_type='t2_cart_action'  limit 100
               """
        log.info("sql===> " + sql)
        result_rows = self.query_track(client=clickhouse_client, sql=sql).result_rows
        log.info("length===> " + str(len(result_rows)))

        assert result_rows, f"cart页面点击稍后再买商品的删除按钮,t2_cart_action埋点校验: platform={platform},page_key={page_key}的埋点"

    @allure.step(
        "cart页面点击稍后再买商品的删除按钮,t2_click_action埋点校验: platform={platform},page_key={page_key},click_type=remove,target_type=product")
    @pytest.mark.parametrize("platform, page_key", platform_and_page_key)
    # @pytest.mark.parametrize("sec_nm", ["normal", "pantry"])
    def test_0110019_cart_t2_click_action_save4later_remove(self, platform, page_key, clickhouse_client,
                                                            time_range):
        # 9.3测试 安卓没有数据, h5和ios有数据
        sql = f"""
                  select JSONExtractRaw(params, 'mod_nm') as a, JSONExtractRaw(params, 'sec_nm') as b, JSONExtractRaw(JSONExtractRaw(params, 'co'), 'target_nm') as c, JSONExtractRaw(JSONExtractRaw(params, 'co'), 'target_type') as d , JSONExtractRaw(JSONExtractRaw(params, 'co'), 'click_type') as e from weee_data.data_tracking_all  where __time>='{time_range.get("begin")}' and __time<='{time_range.get("end")}' and l0_platform='{platform}' and l0_page_key='{page_key}' and a='"cart"' and b='"save_for_later"' and c!='""' and d='"product"' and e='"remove"' and l0_event_type='t2_click_action' limit 100
               """
        log.info("sql===> " + sql)
        result_rows = self.query_track(client=clickhouse_client, sql=sql).result_rows
        log.info("length===> " + str(len(result_rows)))

        assert result_rows, f"cart页面点击稍后再买商品的删除按钮,t2_click_action埋点校验: platform={platform},page_key={page_key},click_type=remove,target_type=product的埋点"

    @allure.step(
        "稍后再买模块点击稍后再买商品加到购物车,t2_click_action埋点校验: platform={platform},page_key={page_key},sec_nm=save_for_later,click_type=unsave,target_type=product")
    @pytest.mark.parametrize("platform, page_key", platform_and_page_key)
    # @pytest.mark.parametrize("sec_nm", ["normal", "pantry"])
    def test_0110020_cart_t2_click_action_save4later_to_cart(self, platform, page_key, clickhouse_client,
                                                             time_range):
        sql = f"""
                  select JSONExtractRaw(params, 'mod_nm') as a, JSONExtractRaw(params, 'sec_nm') as b, JSONExtractRaw(JSONExtractRaw(params, 'co'), 'target_nm') as c, JSONExtractRaw(JSONExtractRaw(params, 'co'), 'target_type') as d , JSONExtractRaw(JSONExtractRaw(params, 'co'), 'click_type') as e from weee_data.data_tracking_all  where __time>='{time_range.get("begin")}' and __time<='{time_range.get("end")}' and l0_platform='{platform}' and l0_page_key='{page_key}' and a='"cart"' and b='"save_for_later"' and c!='""' and d='"product"' and e='"unsave"' and l0_event_type='t2_click_action' limit 100
               """
        log.info("sql===> " + sql)
        result_rows = self.query_track(client=clickhouse_client, sql=sql).result_rows
        log.info("length===> " + str(len(result_rows)))

        assert result_rows, f"稍后再买模块点击稍后再买商品加到购物车,t2_click_action埋点校验: platform={platform},page_key={page_key},sec_nm=save_for_later,click_type=unsave,target_type=product的埋点"

    @allure.step(
        "稍后再买模块点击稍后再买商品加到购物车,t2_click_action埋点校验: platform={platform},page_key={page_key},sec_nm=save_for_later,click_type=unsave,target_type=product")
    @pytest.mark.parametrize("platform, page_key", platform_and_page_key)
    # @pytest.mark.parametrize("sec_nm", ["normal", "pantry"])
    def test_0110020_cart_t2_click_action_save4later_to_cart(self, platform, page_key, clickhouse_client,
                                                             time_range):
        sql = f"""
                      select JSONExtractRaw(params, 'mod_nm') as a, JSONExtractRaw(params, 'sec_nm') as b, JSONExtractRaw(JSONExtractRaw(params, 'co'), 'target_nm') as c, JSONExtractRaw(JSONExtractRaw(params, 'co'), 'target_type') as d , JSONExtractRaw(JSONExtractRaw(params, 'co'), 'click_type') as e from weee_data.data_tracking_all  where __time>='{time_range.get("begin")}' and __time<='{time_range.get("end")}' and l0_platform='{platform}' and l0_page_key='{page_key}' and a='"cart"' and b='"save_for_later"' and c!='""' and d='"product"' and e='"unsave"' and l0_event_type='t2_click_action' limit 100
                   """
        log.info("sql===> " + sql)
        result_rows = self.query_track(client=clickhouse_client, sql=sql).result_rows
        log.info("length===> " + str(len(result_rows)))

        assert result_rows, f"稍后再买模块点击稍后再买商品加到购物车,t2_click_action埋点校验: platform={platform},page_key={page_key},sec_nm=save_for_later,click_type=unsave,target_type=product的埋点"

    # @allure.step(
    #     "购物车失效商品点击设置上架提醒按钮,t2_click_action埋点校验: platform={platform},page_key={page_key},target_type=product_notify_me")
    # @pytest.mark.parametrize("platform, page_key", platform_and_page_key)
    # @pytest.mark.parametrize("click_type", ["normal"])
    # # 有问题
    # def test_0110021_cart_t2_click_action_cart_product_notify_me(self, platform, page_key, click_type,clickhouse_client,
    #                                                          time_range):
    #     sql = f"""
    #                   select JSONExtractRaw(params, 'mod_nm') as a, JSONExtractRaw(params, 'sec_nm') as b, JSONExtractRaw(JSONExtractRaw(params, 'co'), 'target_nm') as c, JSONExtractRaw(JSONExtractRaw(params, 'co'), 'target_type') as d , JSONExtractRaw(JSONExtractRaw(params, 'co'), 'click_type') as e from weee_data.data_tracking_all  where __time>='{time_range.get("begin")}' and __time<='{time_range.get("end")}' and l0_platform='{platform}' and l0_page_key='{page_key}' and a='"cart"' and b='"save_for_later"' and c!='""' and d='"product_notify_me"' and e='{click_type}' and l0_event_type='t2_click_action' limit 100
    #                """
    #     log.info("sql===> " + sql)
    #     result_rows = self.query_track(client=clickhouse_client, sql=sql).result_rows
    #     log.info("length===> " + str(len(result_rows)))
    #
    #     assert result_rows, f"购物车失效商品点击设置上架提醒按钮,t2_click_action埋点校验: platform={platform},page_key={page_key},sec_nm=save_for_later,click_type=unsave,target_type=product的埋点"
    #
    @allure.step(
        "点击购物车为你推荐商品进入pdp,t2_click_action埋点校验: platform={platform},page_key={page_key},sec_nm=preference,click_type=view,target_type=product")
    @pytest.mark.parametrize("platform, page_key", platform_and_page_key)
    def test_0110022_cart_t2_click_action_preference_to_pdp(self, platform, page_key, clickhouse_client,
                                                            time_range):
        sql = f"""
                      select JSONExtractRaw(params, 'mod_nm') as a, JSONExtractRaw(params, 'sec_nm') as b, JSONExtractRaw(JSONExtractRaw(params, 'co'), 'target_nm') as c, JSONExtractRaw(JSONExtractRaw(params, 'co'), 'target_type') as d , JSONExtractRaw(JSONExtractRaw(params, 'co'), 'click_type') as e from weee_data.data_tracking_all  where __time>='{time_range.get("begin")}' and __time<='{time_range.get("end")}' and l0_platform='{platform}' and l0_page_key='{page_key}' and a='"recommend_item_list"' and b='"preference"' and c!='""' and d='"product"' and e='"view"' and l0_event_type='t2_click_action' limit 100
                   """
        log.info("sql===> " + sql)
        result_rows = self.query_track(client=clickhouse_client, sql=sql).result_rows
        log.info("length===> " + str(len(result_rows)))

        assert result_rows, f"点击购物车为你推荐商品进入pdp,t2_click_action埋点校验: platform={platform},page_key={page_key},sec_nm=preference,click_type=view,target_type=product的埋点"

    @allure.step(
        "点击购物车下方猜你喜欢的搜藏按钮,t2_click_action埋点校验: platform={platform},page_key={page_key},sec_nm={sec_nm},click_type={click_type}")
    @pytest.mark.parametrize("platform, page_key", platform_and_page_key)
    @pytest.mark.parametrize("click_type", ["save", "unsave"])
    @pytest.mark.parametrize("sec_nm", ["preference", "bought_before"])
    def test_0110023_cart_t2_click_action_preference_to_save(self, platform, page_key, click_type, sec_nm,
                                                             clickhouse_client,
                                                             time_range):
        if platform == 'iOS':
            sql = f"""
                          select JSONExtractRaw(params, 'mod_nm') as a, JSONExtractRaw(params, 'sec_nm') as b, JSONExtractRaw(JSONExtractRaw(params, 'co'), 'target_type') as c, JSONExtractRaw(JSONExtractRaw(params, 'co'), 'click_type') as d from weee_data.data_tracking_all  where __time>='{time_range.get("begin")}' and __time<='{time_range.get("end")}' and l0_platform='{platform}' and l0_page_key='{page_key}' and a='"recommend_item_list"' and b='"{sec_nm}"' and c='"product"' and d='"{click_type}"'and l0_event_type='t2_click_action' limit 100
                       """
            log.info("sql===> " + sql)
            result_rows = self.query_track(client=clickhouse_client, sql=sql).result_rows
            log.info("length===> " + str(len(result_rows)))

            assert result_rows, f"点击购物车下方猜你喜欢的搜藏按钮,t2_click_action埋点校验: platform={platform},page_key={page_key},sec_nm={sec_nm},click_type={click_type}的埋点"
        else:
            pytest.skip("0110023用例在android和mweb上没数据")
    @allure.step(
        "浏览多种类型购物车上报t2_cart_imp,t2_cart_imp埋点校验: platform={platform},page_key={page_key},sec_nm={sec_nm}")
    @pytest.mark.parametrize("platform, page_key", platform_and_page_key)
    @pytest.mark.parametrize("sec_nm", ["normal", "pantry", "seller"])
    def test_0110024_cart_t2_cart_imp(self, platform, page_key, sec_nm, clickhouse_client,
                                      time_range):
        sql = f"""
                      select JSONExtractRaw(params, 'mod_nm') as a, JSONExtractRaw(params, 'sec_nm') as b, JSONExtractRaw(JSONExtractRaw(params, 'co'), 'items') as c from weee_data.data_tracking_all  where __time>='{time_range.get("begin")}' and __time<='{time_range.get("end")}' and l0_platform='{platform}' and l0_page_key='{page_key}' and a='"cart"' and b='"{sec_nm}"' and c!='""' and l0_event_type='t2_cart_imp' limit 100
                   """
        log.info("sql===> " + sql)
        result_rows = self.query_track(client=clickhouse_client, sql=sql).result_rows
        log.info("length===> " + str(len(result_rows)))

        assert result_rows, f"浏览多种类型购物车上报t2_cart_imp,t2_cart_imp埋点校验: platform={platform},page_key={page_key},sec_nm={sec_nm}的埋点"

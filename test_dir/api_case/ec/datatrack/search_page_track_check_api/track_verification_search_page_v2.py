import json
from test_dir.api_case.ec.datatrack.datatrack_common_function import TrackCommonVerify


class TrackVerificationSearchPageV2:
    @staticmethod
    def search_t2_click_action(track: [], mod_nm, page_key, platform) -> bool:
        flag = False
        for item in track:
            if item.get('l0_page_key') and item.get('l0_page_key') == page_key:
                if item.get("l0_platform") and item.get("l0_platform") == platform:
                    if item.get('params'):
                        track_params = json.loads(item.get('params'))
                        if track_params.get('mod_nm') == mod_nm:
                            if track_params.get('co') and track_params.get('co').get('target_type'):
                                if track_params.get('co').get('target_type') == 'search_target':
                                    flag = True
        return flag

    @staticmethod
    def search_track_t2_click_action_product_save_unsave(track, mod_nm, page_key, platform,click_type):
        flag = False
        for item in track:
            if item.get('l0_page_key') and item.get('l0_page_key') == page_key:
                if item.get("l0_platform") and item.get("l0_platform") == platform:
                    if item.get('params'):
                        track_params = json.loads(item.get('params'))
                        if track_params.get('mod_nm') == mod_nm:
                            if track_params.get('co'):
                                if track_params.get('co').get('target_type') == 'product' and track_params.get('co').get('click_type') == click_type:
                                    if track_params.get('ctx'):
                                        if track_params.get('ctx').get('page_target') and track_params.get('ctx').get('trace_id'):
                                            flag = True
        return flag

    @staticmethod
    def search_t2_click_action_navigate_to_pdp(track, mod_nm, page_key, platform):
        flag = False
        for item in track:
            if item.get('l0_page_key') and item.get('l0_page_key') == page_key:
                if item.get("l0_platform") and item.get("l0_platform") == platform:
                    if item.get('params'):
                        track_params = json.loads(item.get('params'))
                        if track_params.get('mod_nm') == mod_nm:
                            if track_params.get('co') and track_params.get('co').get('target_type') == 'product':
                                if track_params.get('ctx'):
                                    if track_params.get('ctx').get('page_target') and track_params.get('ctx').get('trace_id'):
                                        flag = True
        return flag

    @staticmethod
    def search_t2_click_action_topx(track, mod_nm, page_key, platform):
        flag = False
        for item in track:
            if item.get('l0_page_key') and item.get('l0_page_key') == page_key:
                if item.get("l0_platform") and item.get("l0_platform") == platform:
                    if item.get('params'):
                        track_params = json.loads(item.get('params'))
                        if track_params.get('mod_nm') == mod_nm:
                            if track_params.get('co') and track_params.get('co').get('target_type') == 'chart':
                                if track_params.get('ctx'):
                                    if track_params.get('ctx').get('page_target') \
                                            and track_params.get('ctx').get('trace_id') \
                                            and track_params.get('ctx').get('tag_key') \
                                            and track_params.get('ctx').get('tag_name'):
                                        flag = True
        return flag

    @staticmethod
    def search_t2_prod_imp(track, mod_nm, page_key, platform):
        flag = False
        for item in track:
            if item.get('l0_page_key') and item.get('l0_page_key') == page_key:
                if item.get("l0_platform") and item.get("l0_platform") == platform:
                    if item.get('params'):
                        track_params = json.loads(item.get('params'))
                        if track_params.get('mod_nm') == mod_nm:
                            if track_params.get('co'):
                                if TrackCommonVerify.is_null_in_dict(track_params.get('co')):
                                    if track_params.get('ctx'):
                                        if (track_params.get('ctx').get('page_target')
                                                and track_params.get('ctx').get('sort')
                                                and track_params.get('ctx').get('page_tab')
                                                and track_params.get('ctx').get('trace_id')):
                                            flag = True
        return flag

    @staticmethod
    def search_t2_cart_action(track, mod_nm, page_key, platform):
        flag = False
        for item in track:
            if item.get('l0_page_key') and item.get('l0_page_key') == page_key:
                if item.get("l0_platform") and item.get("l0_platform") == platform:
                    if item.get('params'):
                        track_params = json.loads(item.get('params'))
                        if track_params.get('mod_nm') == mod_nm:
                            if track_params.get('co') and track_params.get('co').get('source'):
                                if track_params.get('ctx'):
                                    if (track_params.get('ctx').get('page_target')
                                            and track_params.get('ctx').get('sort')
                                            and track_params.get('ctx').get('page_tab')
                                            and track_params.get('ctx').get('trace_id')):
                                        flag = True
        return flag

    @staticmethod
    def search_t2_filter_button_imp(track, mod_nm, page_key, platform):
        flag = False
        for item in track:
            if item.get('l0_page_key') and item.get('l0_page_key') == page_key:
                if item.get("l0_platform") and item.get("l0_platform") == platform:
                    if item.get('params'):
                        track_params = json.loads(item.get('params'))
                        if track_params.get('mod_nm') == mod_nm:
                            if track_params.get('ctx'):
                                if track_params.get('ctx').get('page_target') and track_params.get('ctx').get(
                                        'trace_id'):
                                    flag = True
        return flag





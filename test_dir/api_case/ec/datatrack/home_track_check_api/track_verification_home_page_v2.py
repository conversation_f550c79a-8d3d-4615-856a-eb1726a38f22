import json

from test_dir.api_case.ec.datatrack.datatrack_common_function import TrackCommonVerify


class TrackVerificationHomePageV2:
    @staticmethod
    def home_t2_click_product(track: [], mod_nm, page_key, platform) -> bool:
        flag = False
        for item in track:
            if item.get('l0_page_key') and item.get('l0_page_key') == page_key:
                if item.get("l0_platform") and item.get("l0_platform") == platform:
                    if item.get('params'):
                        track_params = json.loads(item.get('params'))
                        if track_params.get('co') and track_params.get('co').get('target_type'):
                            if track_params.get('co').get('target_type') == 'product':
                                if track_params.get('mod_nm') == mod_nm:
                                    if 'cm_collection_v2_manual_rank' in track_params.get(
                                            'mod_nm') or track_params.get(
                                        'mod_nm') == 'cm_item_ads_collection':
                                        if track_params.get('sec_nm'):
                                            return True
                                        else:
                                            return False
                                    flag = True
        return flag

    @staticmethod
    def home_t2_click_banner(track: [], mod_nm, page_key, platform) -> bool:
        flag = False
        for item in track:
            if item.get('l0_page_key') and item.get('l0_page_key') == page_key:
                if item.get("l0_platform") and item.get("l0_platform") == platform:
                    if item.get('params'):
                        track_params = json.loads(item.get('params'))
                        if track_params.get("mod_nm") == mod_nm:
                            if track_params.get('co') and track_params.get('co').get('target_type'):
                                if track_params.get('co').get('target_type') == 'home_top_banner':
                                    if TrackCommonVerify.is_null_in_dict(track_params.get('co')):
                                        flag = True
        return flag

    @staticmethod
    def home_t2_click_banner_line(track: [], mod_nm, page_key, platform) -> bool:
        flag = False
        for item in track:
            if item.get('l0_page_key') and item.get('l0_page_key') == page_key:
                if item.get("l0_platform") and item.get("l0_platform") == platform:
                    if item.get('params'):
                        track_params = json.loads(item.get('params'))
                        if track_params.get("mod_nm") == mod_nm:
                            if track_params.get('co') and track_params.get('co').get('target_type'):
                                if track_params.get('co').get('target_type') == 'banner_line':
                                    if TrackCommonVerify.is_null_in_dict(track_params.get('co')):
                                        flag = True
        return flag

    @staticmethod
    def home_t2_click_normal_button(track: list, mod_nm, page_key, platform) -> bool:
        flag = False
        for item in track:
            if item.get('l0_page_key') and item.get('l0_page_key') == page_key:
                if item.get("l0_platform") and item.get("l0_platform") == platform:
                    if item.get('params'):
                        track_params = json.loads(item.get('params'))
                        if track_params.get("mod_nm") == mod_nm:
                            if track_params.get('co') and track_params.get('co').get('target_nm'):
                                if track_params.get('co').get('target_nm') == 'explore_more':
                                    flag = True
        return flag

    @staticmethod
    def home_t2_click_category_icon(track: list, mod_nm, page_key, platform) -> bool:
        flag = False
        for item in track:
            if item.get('l0_page_key') and item.get('l0_page_key') == page_key:
                if item.get("l0_platform") and item.get("l0_platform") == platform:
                    if item.get('params'):
                        track_params = json.loads(item.get('params'))
                        if track_params.get("mod_nm") == mod_nm:
                            if track_params.get('co') and track_params.get('co').get('target_type'):
                                if track_params.get('co').get('target_type') == 'category':
                                    if TrackCommonVerify.is_null_in_dict(track_params.get('co')):
                                        flag = True
        return flag

    @staticmethod
    def home_t2_banner_imp_banner(track, mod_nm, page_key, platform) -> bool:
        flag = False
        for item in track:
            if item.get('l0_page_key') and item.get('l0_page_key') == page_key:
                if item.get("l0_platform") and item.get("l0_platform") == platform:
                    if item.get('params'):
                        track_params = json.loads(item.get('params'))
                        if track_params.get("mod_nm") == mod_nm:
                            if track_params.get('co') and track_params.get('co').get('banner_type'):
                                if track_params.get('co').get('banner_type') == 'home_top_banner':
                                    if TrackCommonVerify.is_null_in_dict(track_params.get('co')):
                                        flag = True
        return flag

    @staticmethod
    def home_t2_cart_action_add_to_cart(track, mod_nm, page_key, platform) -> bool:
        flag = False
        for item in track:
            if item.get('l0_page_key') and item.get('l0_page_key') == page_key:
                if item.get("l0_platform") and item.get("l0_platform") == platform:
                    if item.get('params'):
                        track_params = json.loads(item.get('params'))
                        if track_params.get('mod_nm') and mod_nm == track_params.get("mod_nm"):
                            if mod_nm not in ["cm_collection_v2_manual_rank2", "cm_collection_v2_manual_rank1", "cm_item_ads_collection"]:
                                if track_params.get('co') and track_params.get('co').get('source'):
                                    if track_params.get('co').get('source') == f'{page_key}-{mod_nm}-null':
                                        flag = True
                            else:
                                if track_params.get('co') and track_params.get('co').get('source'):
                                    if track_params.get('co').get('source') == f'{page_key}-{mod_nm}-null':
                                        if track_params.get("sec_nm"):
                                            flag = True
        return flag

    @staticmethod
    def home_t2_product_impose(track, mod_nm, page_key, platform) -> bool:
        flag = False
        for item in track:
            if item.get('l0_page_key') and item.get('l0_page_key') == page_key:
                if item.get("l0_platform") and item.get("l0_platform") == platform:
                    if item.get('params'):
                        track_params = json.loads(item.get('params'))
                        if track_params.get("mod_nm") == mod_nm:
                            if mod_nm not in ["cm_collection_v2_manual_rank2", "cm_collection_v2_manual_rank1", "cm_item_ads_collection"]:
                                if track_params.get('co') and TrackCommonVerify.is_null_in_dict(
                                        track_params.get('co')):
                                    flag = True
                            else:
                                if track_params.get('co') and TrackCommonVerify.is_null_in_dict(
                                        track_params.get('co')):
                                    if track_params.get("sec_nm"):
                                        flag = True
        return flag

    @staticmethod
    def home_track_t2_ellipse(track, mod_nm, page_key, platform) -> bool:
        flag = False
        for item in track:
            if item.get('l0_page_key') and item.get('l0_page_key') == page_key:
                if item.get("l0_platform") and item.get("l0_platform") == platform:
                    if item.get('params'):
                        track_params = json.loads(item.get('params'))
                        if track_params.get("mod_nm") == mod_nm:
                            if track_params.get('co') and track_params.get('co').get('ellipse_type'):
                                if track_params.get('co').get('ellipse_type') == "category":
                                    flag = True
        return flag





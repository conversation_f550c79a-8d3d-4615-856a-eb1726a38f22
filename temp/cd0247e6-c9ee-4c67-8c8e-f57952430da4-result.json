{"name": "H5-社区搜索Accounts栏验证", "status": "passed", "description": "\n        H5-社区搜索Accounts栏验证\n        ", "start": *************, "stop": *************, "uuid": "d5af4e70-09f6-4200-bc12-1804441f7fa0", "historyId": "4327abefa3e079114d9ab3cd88ebb12c", "testCaseId": "4327abefa3e079114d9ab3cd88ebb12c", "fullName": "src.Mweb.EC.testcases.mweb_social.test_112741_h5_social_search_accounts_verify.TestH5SocialSearchAccountsVerify#test_112741_h5_social_search_accounts_verify", "labels": [{"name": "story", "value": "H5-社区搜索Accounts栏验证"}, {"name": "tag", "value": "social"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "mweb_regression"}, {"name": "parentSuite", "value": "src.Mweb.EC.testcases.mweb_social"}, {"name": "suite", "value": "test_112741_h5_social_search_accounts_verify"}, {"name": "subSuite", "value": "TestH5SocialSearchAccountsVerify"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "23528-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Mweb.EC.testcases.mweb_social.test_112741_h5_social_search_accounts_verify"}]}
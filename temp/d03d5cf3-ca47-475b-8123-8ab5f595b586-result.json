{"name": "进入pdp页面检查元素并加购", "status": "passed", "description": "\n        training\n        ", "parameters": [{"name": "pdp_url", "value": "'https://www.sayweee.com/en/product/Calbee-Takoyaki-Ball/18362?category=snack02&parent_category=snack'"}], "start": 1745317051693, "stop": 1745317063135, "uuid": "32a8d88a-3b23-4c92-abfe-55492b54b88c", "historyId": "0e00b8b9b7917b2c321be7d9bb2eb9de", "testCaseId": "1b9c0f1ed3ead231909e6efc4bb73b27", "fullName": "src.Dweb.EC.testcases.dweb_pdp.test_100_pdp_page_check.TestAtPDP#test_check_pdp_and_add_to_cart", "labels": [{"name": "story", "value": "产品PDP页面校验"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "pdp"}, {"name": "tag", "value": "smoke"}, {"name": "tag", "value": "dweb_regression"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.dweb_pdp"}, {"name": "suite", "value": "test_100_pdp_page_check"}, {"name": "subSuite", "value": "TestAtPDP"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "23072-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.dweb_pdp.test_100_pdp_page_check"}]}
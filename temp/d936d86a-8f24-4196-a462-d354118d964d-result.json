{"name": "Mobile-订单成功页样式验证", "status": "failed", "statusDetails": {"message": "AssertionError: Locator expected to be visible\nActual value: None \nCall log:\nLocatorAssertions.to_be_visible with timeout 5000ms\n  - waiting for locator(\"//button[contains(text(), 'Share order and earn')]\")", "trace": "self = <src.Mweb.EC.testcases.mweb_order_confimation.test_112230_mweb_order_confirmation_ui_ux.TestMWebOrderConfirmationUIUX object at 0x00000143FC263110>\nphone_page = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppDat...aywright\\chromium-1091\\chrome-win\\chrome.exe> version=120.0.6099.28>>, 'page': <Page url='https://www.sayweee.com/en'>}\nh5_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...uZBFvf9xCE7fdAfz0j0_peXZ2AmEY5oTjm3kGArdKc_-Yr8hE1AQywfTOCOeXcqcQvjW03ZcQ5ApcUVdOJgLZUVza2jic-6FwlXX08RYiCKroMT4', ...}\nh5_open_and_close_trace = None\n\n    @allure.title(\"Mobile-订单成功页样式验证\")\n    @pytest.mark.present\n    def test_112230_mweb_order_confirmation_ui_ux(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):\n        \"\"\"\n        【112230】 Mobile-订单成功页样式验证\n    \n        测试步骤:\n        1. 打开订单成功页\n        2. 验证页面标题和订单号\n        3. 验证订单状态信息\n        4. 点击开始赚取积分按钮并验证分享功能\n        5. 验证订单详情按钮并点击跳转\n        6. 验证推荐商品区域\n        这个账号暂时没有订单号没办法测试\n        \"\"\"\n        p: Page = phone_page.get(\"page\")\n        c = phone_page.get(\"context\")\n        # 1. 创建订单成功页面对象\n        order_confirmation_page = MWebOrderComfirmationPage(p,h5_autotest_header,browser_context=c,page_url=\"/order/success/v2/69581331\")\n        p.wait_for_load_state(\"networkidle\", timeout=30000)\n        log.info(\"成功加载订单确认页面\")\n        p.wait_for_timeout(3000)\n    \n        with allure.step(\"点击开始赚取积分按钮并验证分享功能\"):\n            # 验证开始赚取积分按钮\n            start_button = p.locator(mweb_order_confirmation_ele.ele_start_earning)\n>           expect(start_button).to_be_visible()\nE           AssertionError: Locator expected to be visible\nE           Actual value: None \nE           Call log:\nE           LocatorAssertions.to_be_visible with timeout 5000ms\nE             - waiting for locator(\"//button[contains(text(), 'Share order and earn')]\")\n\nsrc\\Mweb\\EC\\testcases\\mweb_order_confimation\\test_112230_mweb_order_confirmation_ui_ux.py:50: AssertionError"}, "description": "\n        【112230】 Mobile-订单成功页样式验证\n        \n        测试步骤:\n        1. 打开订单成功页\n        2. 验证页面标题和订单号\n        3. 验证订单状态信息\n        4. 点击开始赚取积分按钮并验证分享功能\n        5. 验证订单详情按钮并点击跳转\n        6. 验证推荐商品区域\n        这个账号暂时没有订单号没办法测试\n        ", "steps": [{"name": "点击开始赚取积分按钮并验证分享功能", "status": "failed", "statusDetails": {"message": "AssertionError: Locator expected to be visible\nActual value: None \nCall log:\nLocatorAssertions.to_be_visible with timeout 5000ms\n  - waiting for locator(\"//button[contains(text(), 'Share order and earn')]\")\n\n", "trace": "  File \"D:\\MyWork\\Python\\EC-demo\\qa-ui-dmweb\\src\\Mweb\\EC\\testcases\\mweb_order_confimation\\test_112230_mweb_order_confirmation_ui_ux.py\", line 50, in test_112230_mweb_order_confirmation_ui_ux\n    expect(start_button).to_be_visible()\n  File \"D:\\MyWork\\Python\\EC-demo\\qa-ui-dmweb\\venv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py\", line 20808, in to_be_visible\n    self._sync(self._impl_obj.to_be_visible(visible=visible, timeout=timeout))\n  File \"D:\\MyWork\\Python\\EC-demo\\qa-ui-dmweb\\venv\\Lib\\site-packages\\playwright\\_impl\\_sync_base.py\", line 115, in _sync\n    return task.result()\n           ^^^^^^^^^^^^^\n  File \"D:\\MyWork\\Python\\EC-demo\\qa-ui-dmweb\\venv\\Lib\\site-packages\\playwright\\_impl\\_assertions.py\", line 642, in to_be_visible\n    await self._expect_impl(\n  File \"D:\\MyWork\\Python\\EC-demo\\qa-ui-dmweb\\venv\\Lib\\site-packages\\playwright\\_impl\\_assertions.py\", line 68, in _expect_impl\n    raise AssertionError(\n"}, "start": 1750920219939, "stop": 1750920224981}], "start": 1750920210585, "stop": 1750920224982, "uuid": "ef8a4c93-3a2e-4df1-b8cb-99224d636159", "historyId": "3eff565b6913224b5baa26fd29e0b331", "testCaseId": "3eff565b6913224b5baa26fd29e0b331", "fullName": "src.Mweb.EC.testcases.mweb_order_confimation.test_112230_mweb_order_confirmation_ui_ux.TestMWebOrderConfirmationUIUX#test_112230_mweb_order_confirmation_ui_ux", "labels": [{"name": "story", "value": "Mobile-订单成功页样式验证"}, {"name": "tag", "value": "present"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "mweb_regression"}, {"name": "tag", "value": "<PERSON><PERSON><PERSON>"}, {"name": "parentSuite", "value": "src.Mweb.EC.testcases.mweb_order_confimation"}, {"name": "suite", "value": "test_112230_mweb_order_confirmation_ui_ux"}, {"name": "subSuite", "value": "TestMWebOrderConfirmationUIUX"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "23528-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Mweb.EC.testcases.mweb_order_confimation.test_112230_mweb_order_confirmation_ui_ux"}]}
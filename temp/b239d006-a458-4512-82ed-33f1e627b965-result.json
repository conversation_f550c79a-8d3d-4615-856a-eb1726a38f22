{"name": "从首页进入category", "status": "passed", "description": "\n        111095: MS用例号\n        ", "parameters": [{"name": "cl", "value": "'a'"}], "start": 1745316550731, "stop": 1745316739628, "uuid": "e340ef12-c4a2-42f4-95a0-a62a23c1a2ed", "historyId": "2fb9624dd69dfabd4be187aedba85ec8", "testCaseId": "269389fefd41abc328ddd6bbcb24a2df", "fullName": "src.Dweb.EC.testcases.dweb_home.test_003_page_home.TestAtHome#test_search_by_category", "labels": [{"name": "story", "value": "首页搜索by category"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "dweb_regression"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.dweb_home"}, {"name": "suite", "value": "test_003_page_home"}, {"name": "subSuite", "value": "TestAtHome"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "23072-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.dweb_home.test_003_page_home"}]}
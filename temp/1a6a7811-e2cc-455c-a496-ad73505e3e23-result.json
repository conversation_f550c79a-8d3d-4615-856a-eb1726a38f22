{"name": "购物车-验证从首页进入Deals筛选Global+加购商品后点击结算", "status": "passed", "description": "\n        购物车-验证从首页进入Deals筛选Global+加购商品后点击结算\n        测试步骤：\n        1. 清空购物车\n        2. 从首页进入Deals分类\n        3. 点击分类的筛选条件选择Global+选项并应用\n        4. 加购筛选Global+筛选条件后的商品\n        5. 进入购物车页面，验证商品已添加成功\n        6. 点击结算按钮，验证跳转到结算页面\n        ", "steps": [{"name": "切换zipcode到98011", "status": "passed", "start": 1750919854815, "stop": 1750919854826}, {"name": "清空购物车", "status": "passed", "start": 1750919854826, "stop": 1750919859308}, {"name": "从首页进入Deals分类", "status": "passed", "start": 1750919859308, "stop": 1750919875060}, {"name": "点击分类的筛选条件选择Global+选项并应用，加购商品", "status": "passed", "start": 1750919875060, "stop": 1750919891512}, {"name": "进入购物车页面，验证商品已添加成功", "status": "passed", "start": 1750919891512, "stop": 1750919900370}, {"name": "点击结算按钮，验证跳转到结算页面", "status": "passed", "start": 1750919900370, "stop": 1750919905421}], "start": 1750919854815, "stop": 1750919905423, "uuid": "29236c0d-71f0-4f70-b2e0-319bffd6d266", "historyId": "3e1f838383f68b87fe8d290b7e71565f", "testCaseId": "3e1f838383f68b87fe8d290b7e71565f", "fullName": "src.Mweb.EC.testcases.mweb_cart.test_112719_mweb_cart_global_checkout.TestMwebCartGlobalCheckout#test_mweb_global_cart_ui_ux", "labels": [{"name": "story", "value": "购物车-验证从首页进入Deals筛选Global+加购商品后点击结算"}, {"name": "tag", "value": "present"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "mweb_regression"}, {"name": "parentSuite", "value": "src.Mweb.EC.testcases.mweb_cart"}, {"name": "suite", "value": "test_112719_mweb_cart_global_checkout"}, {"name": "subSuite", "value": "TestMwebCartGlobalCheckout"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "23528-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Mweb.EC.testcases.mweb_cart.test_112719_mweb_cart_global_checkout"}]}
{"name": "PC端-从账户页面新增地址UI/UX验证", "status": "broken", "statusDetails": {"message": "playwright._impl._errors.TimeoutError: Timeout 50000ms exceeded.", "trace": "self = <src.Dweb.EC.testcases.dweb_porder.dweb_address.test_110841_dWeb_add_address_ui_ux.TestDWebAddAddressUIUX object at 0x000001FDD716A590>\npage = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppDat...ge url='https://www.sayweee.com/en/account/address/edit?redirect_url=%2Fen%2Faccount%2Fsettings%3F&source=me_setting'>}\npc_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...sQTAcnJR8A7JAOh1ZSD-Pa4ZLso29CFKKRkuhCy5rGmgdHxwcT2I5ZgjWzyOj35XTNm6dqAXzkKCpOvX2w-xkB_i5g-cBs2cJEMKdLXqajR1j72s', ...}\nlogin_trace = None\n\n    @allure.title(\"PC端-从账户页面新增地址UI/UX验证\")\n    def test_110841_dWeb_add_address_from_account_ui_ux(self, page: dict, pc_autotest_header, login_trace):\n        \"\"\"\n        PC端-从账户页面新增地址UI/UX验证\n        此用例的校验点有：\n        1. 进入账户页面，点击地址管理（使用get_by_test_id定位）\n        2. 点击\"新增地址\"按钮（使用get_by_test_id定位），进入新增地址页面\n        3. 填写地址信息（街道地址、姓名、电话、备注）\n        4. 保存地址，验证地址簿中新增了一条地址\n        \"\"\"\n        p: Page = page.get(\"page\")\n        c = page.get(\"context\")\n    \n        # 创建地址页面对象\n        address_page = DWebAddressPage(p, pc_autotest_header, browser_context=c)\n    \n        # 从账户页面添加新地址\n>       address_page.add_new_address_from_account()\n\nsrc\\Dweb\\EC\\testcases\\dweb_porder\\dweb_address\\test_110841_dWeb_add_address_ui_ux.py:51: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\nsrc\\Dweb\\EC\\dweb_pages\\dweb_page_address\\dweb_page_address.py:72: in add_new_address_from_account\n    self._fill_address_form()\nsrc\\Dweb\\EC\\dweb_pages\\dweb_page_address\\dweb_page_address.py:185: in _fill_address_form\n    self.page.locator(\"#rc_select_0\").fill(\"15006 104th Ave NE\")\nvenv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py:16154: in fill\n    self._sync(\nvenv\\Lib\\site-packages\\playwright\\_impl\\_locator.py:211: in fill\n    return await self._frame.fill(self._selector, strict=True, **params)\nvenv\\Lib\\site-packages\\playwright\\_impl\\_frame.py:523: in fill\n    await self._channel.send(\"fill\", locals_to_params(locals()))\nvenv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:62: in send\n    return await self._connection.wrap_api_call(\nvenv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:492: in wrap_api_call\n    return await cb()\n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n\nself = <playwright._impl._connection.Channel object at 0x000001FDD792DE90>\nmethod = 'fill'\nparams = {'selector': '#rc_select_0', 'strict': True, 'value': '15006 104th Ave NE'}\nreturn_as_dict = False\n\n    async def inner_send(\n        self, method: str, params: Optional[Dict], return_as_dict: bool\n    ) -> Any:\n        if params is None:\n            params = {}\n        callback = self._connection._send_message_to_server(\n            self._object, method, params\n        )\n        if self._connection._error:\n            error = self._connection._error\n            self._connection._error = None\n            raise error\n        done, _ = await asyncio.wait(\n            {\n                self._connection._transport.on_error_future,\n                callback.future,\n            },\n            return_when=asyncio.FIRST_COMPLETED,\n        )\n        if not callback.future.done():\n            callback.future.cancel()\n>       result = next(iter(done)).result()\nE       playwright._impl._errors.TimeoutError: Timeout 50000ms exceeded.\n\nvenv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:100: TimeoutError"}, "description": "\n        PC端-从账户页面新增地址UI/UX验证\n        此用例的校验点有：\n        1. 进入账户页面，点击地址管理（使用get_by_test_id定位）\n        2. 点击\"新增地址\"按钮（使用get_by_test_id定位），进入新增地址页面\n        3. 填写地址信息（街道地址、姓名、电话、备注）\n        4. 保存地址，验证地址簿中新增了一条地址\n        ", "start": *************, "stop": *************, "uuid": "f0f6a54e-8f8c-41fc-bf31-41f707b4d95b", "historyId": "69ce40bc893ee895c2eda63ce7bded3f", "testCaseId": "69ce40bc893ee895c2eda63ce7bded3f", "fullName": "src.Dweb.EC.testcases.dweb_porder.dweb_address.test_110841_dWeb_add_address_ui_ux.TestDWebAddAddressUIUX#test_110841_dWeb_add_address_from_account_ui_ux", "labels": [{"name": "story", "value": "PC端-新增地址UI/UX验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "dweb_regression"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.dweb_porder.dweb_address"}, {"name": "suite", "value": "test_110841_dWeb_add_address_ui_ux"}, {"name": "subSuite", "value": "TestDWebAddAddressUIUX"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "15096-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.dweb_porder.dweb_address.test_110841_dWeb_add_address_ui_ux"}]}
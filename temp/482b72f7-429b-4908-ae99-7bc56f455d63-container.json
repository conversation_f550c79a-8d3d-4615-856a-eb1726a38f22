{"uuid": "7941fe05-56d9-4579-b684-4d3c225b8484", "children": ["15cc4f0f-89ca-4498-89b9-1a309b535b9c"], "befores": [{"name": "setup", "status": "broken", "statusDetails": {"message": "playwright._impl._errors.Error: net::ERR_ABORTED at https://www.sayweee.com/en\n", "trace": "  File \"D:\\MyWork\\Python\\EC-demo\\qa-ui-dmweb\\venv\\Lib\\site-packages\\pluggy\\_callers.py\", line 77, in _multicall\n    res = hook_impl.function(*args)\n          ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\MyWork\\Python\\EC-demo\\qa-ui-dmweb\\venv\\Lib\\site-packages\\_pytest\\fixtures.py\", line 1123, in pytest_fixture_setup\n    result = call_fixture_func(fixturefunc, request, kwargs)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\MyWork\\Python\\EC-demo\\qa-ui-dmweb\\venv\\Lib\\site-packages\\_pytest\\fixtures.py\", line 895, in call_fixture_func\n    fixture_result = next(generator)\n                     ^^^^^^^^^^^^^^^\n  File \"D:\\MyWork\\Python\\EC-demo\\qa-ui-dmweb\\src\\Dweb\\EC\\testcases\\dweb_home\\test_001_login_and_signin.py\", line 13, in setup\n    lp = LoginAndSignin(not_login_page[\"page\"], pc_autotest_header)\n         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\MyWork\\Python\\EC-demo\\qa-ui-dmweb\\src\\Dweb\\EC\\dweb_pages\\dweb_page_home\\dweb_page_login_and_signin.py\", line 27, in __init__\n    self.page.goto(TEST_URL)\n  File \"D:\\MyWork\\Python\\EC-demo\\qa-ui-dmweb\\venv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py\", line 9312, in goto\n    self._sync(\n  File \"D:\\MyWork\\Python\\EC-demo\\qa-ui-dmweb\\venv\\Lib\\site-packages\\playwright\\_impl\\_sync_base.py\", line 115, in _sync\n    return task.result()\n           ^^^^^^^^^^^^^\n  File \"D:\\MyWork\\Python\\EC-demo\\qa-ui-dmweb\\venv\\Lib\\site-packages\\playwright\\_impl\\_page.py\", line 475, in goto\n    return await self._main_frame.goto(**locals_to_params(locals()))\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\MyWork\\Python\\EC-demo\\qa-ui-dmweb\\venv\\Lib\\site-packages\\playwright\\_impl\\_frame.py\", line 139, in goto\n    await self._channel.send(\"goto\", locals_to_params(locals()))\n  File \"D:\\MyWork\\Python\\EC-demo\\qa-ui-dmweb\\venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 62, in send\n    return await self._connection.wrap_api_call(\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\MyWork\\Python\\EC-demo\\qa-ui-dmweb\\venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 492, in wrap_api_call\n    return await cb()\n           ^^^^^^^^^^\n  File \"D:\\MyWork\\Python\\EC-demo\\qa-ui-dmweb\\venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 100, in inner_send\n    result = next(iter(done)).result()\n             ^^^^^^^^^^^^^^^^^^^^^^^^^\n"}, "start": 1745315809249, "stop": 1745315811202}], "start": 1745315809249, "stop": 1745315811391}
{"name": "H5购物车-MO购物车UI/UX验证", "status": "passed", "description": "\n        【112719】 H5购物车-MO购物车UI/UX验证\n        ", "start": 1745320082119, "stop": 1745320119458, "uuid": "5a25ffc0-17f2-4e09-b34a-ad68bab8ecbf", "historyId": "d6dfb11cf9db1f5284d63669a3daccc5", "testCaseId": "d6dfb11cf9db1f5284d63669a3daccc5", "fullName": "src.Mweb.EC.testcases.mweb_cart.test_112719_mweb_mo_cart_ui_ux.TestMwebMoCartUIUX#test_112719_MWeb_mo_cart_ui_ux", "labels": [{"name": "story", "value": "H5购物车-MO购物车UI/UX验证"}, {"name": "tag", "value": "present"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "mweb_regression"}, {"name": "tag", "value": "<PERSON><PERSON><PERSON>"}, {"name": "parentSuite", "value": "src.Mweb.EC.testcases.mweb_cart"}, {"name": "suite", "value": "test_112719_mweb_mo_cart_ui_ux"}, {"name": "subSuite", "value": "TestMwebMoCartUIUX"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "11500-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Mweb.EC.testcases.mweb_cart.test_112719_mweb_mo_cart_ui_ux"}]}
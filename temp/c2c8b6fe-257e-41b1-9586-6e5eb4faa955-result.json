{"name": "test_home_place_order_then_login", "status": "broken", "statusDetails": {"message": "playwright._impl._errors.TimeoutError: Timeout 50000ms exceeded.", "trace": "self = <src.Dweb.EC.testcases.dweb_signup_signout.test_001_login_and_signin.TestLoginAndSignin object at 0x000001FDD7193C90>\nsetup = <src.Dweb.EC.dweb_pages.dweb_page_home.dweb_page_login_and_signin.LoginAndSignin object at 0x000001FDD7D07210>\nnot_login_trace = None\n\n    def test_home_place_order_then_login(self, setup, not_login_trace):\n>       setup.place_order_then_login()\n\nsrc\\Dweb\\EC\\testcases\\dweb_signup_signout\\test_001_login_and_signin.py:21: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\nsrc\\Dweb\\EC\\dweb_pages\\dweb_page_home\\dweb_page_login_and_signin.py:52: in place_order_then_login\n    self.page.locator(\"//div[@class='ant-modal-body']/i\").click()\nvenv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py:15784: in click\n    self._sync(\nvenv\\Lib\\site-packages\\playwright\\_impl\\_locator.py:159: in click\n    return await self._frame.click(self._selector, strict=True, **params)\nvenv\\Lib\\site-packages\\playwright\\_impl\\_frame.py:484: in click\n    await self._channel.send(\"click\", locals_to_params(locals()))\nvenv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:62: in send\n    return await self._connection.wrap_api_call(\nvenv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:492: in wrap_api_call\n    return await cb()\n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n\nself = <playwright._impl._connection.Channel object at 0x000001FDD7F84150>\nmethod = 'click'\nparams = {'selector': \"//div[@class='ant-modal-body']/i\", 'strict': True}\nreturn_as_dict = False\n\n    async def inner_send(\n        self, method: str, params: Optional[Dict], return_as_dict: bool\n    ) -> Any:\n        if params is None:\n            params = {}\n        callback = self._connection._send_message_to_server(\n            self._object, method, params\n        )\n        if self._connection._error:\n            error = self._connection._error\n            self._connection._error = None\n            raise error\n        done, _ = await asyncio.wait(\n            {\n                self._connection._transport.on_error_future,\n                callback.future,\n            },\n            return_when=asyncio.FIRST_COMPLETED,\n        )\n        if not callback.future.done():\n            callback.future.cancel()\n>       result = next(iter(done)).result()\nE       playwright._impl._errors.TimeoutError: Timeout 50000ms exceeded.\n\nvenv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:100: TimeoutError"}, "start": 1750652593121, "stop": 1750652643605, "uuid": "ae78d6d3-f699-455f-8d40-c2aa35a91af2", "historyId": "6449ab75841da1e1691902aefcad990e", "testCaseId": "6449ab75841da1e1691902aefcad990e", "fullName": "src.Dweb.EC.testcases.dweb_signup_signout.test_001_login_and_signin.TestLoginAndSignin#test_home_place_order_then_login", "labels": [{"name": "story", "value": "首页登陆加购或先加购再登陆"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "coreflow"}, {"name": "tag", "value": "dweb_regression"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.dweb_signup_signout"}, {"name": "suite", "value": "test_001_login_and_signin"}, {"name": "subSuite", "value": "TestLoginAndSignin"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "15096-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.dweb_signup_signout.test_001_login_and_signin"}]}
{"name": "PC端-从首页新增地址UI/UX验证", "status": "passed", "description": "\n        PC端-从首页新增地址UI/UX验证\n        此用例的校验点有：\n        1. 在首页点击地址按钮（使用get_by_test_id定位），进入地址选择页面\n        2. 点击\"新增地址\"按钮（使用get_by_test_id定位），进入新增地址页面\n        3. 填写地址信息（街道地址、姓名、电话、备注）\n        4. 保存地址，验证地址簿中新增了一条地址\n        ", "start": 1745315205710, "stop": 1745315255320, "uuid": "6bcc263b-9519-4d12-a2ff-d39ed74aff4f", "historyId": "f71273822ecea2381b126fccbfb610b1", "testCaseId": "f71273822ecea2381b126fccbfb610b1", "fullName": "src.Dweb.EC.testcases.dweb_address.test_110841_dWeb_add_address_ui_ux.TestDWebAddAddressUIUX#test_110841_dWeb_add_address_from_home_ui_ux", "labels": [{"name": "story", "value": "PC端-新增地址UI/UX验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "dweb_regression"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.dweb_address"}, {"name": "suite", "value": "test_110841_dWeb_add_address_ui_ux"}, {"name": "subSuite", "value": "TestDWebAddAddressUIUX"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "23072-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.dweb_address.test_110841_dWeb_add_address_ui_ux"}]}
{"name": "【113360】 H5购物车-空购物车UI/UX验证", "status": "broken", "statusDetails": {"message": "AttributeError: 'NoneType' object has no attribute 'text_content'", "trace": "self = <src.Mweb.EC.testcases.mweb_cart.test_113360_mWeb_empty_cart_ui_ux.TestMWebEmptyCartUIUX object at 0x00000232891A2710>\nphone_page = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppDat...091\\chrome-win\\chrome.exe> version=120.0.6099.28>>, 'page': <Page url='https://www.sayweee.com/en/cart?joinEnki=true'>}\nh5_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer..._SJ7diRvnCocHMmF5hZ0tlNKGfXaDzYKUphn4N20EySuVCfzK94PC6IciJ0ehJwPcBV0k_AHg-ygZ2X2U8Xmqu8HR-tY3Tb1uM-gGcrmECwgQmyA', ...}\nh5_open_and_close_trace = None\n\n    @allure.title(\"【113360】 H5购物车-空购物车UI/UX验证\")\n    def test_113360_mWeb_empty_cart_ui_ux(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):\n        \"\"\"\n        【113360】 H5购物车-空购物车UI/UX验证\n        \"\"\"\n        p: Page = phone_page.get(\"page\")\n        c = phone_page.get(\"context\")\n        try:\n            empty_cart(h5_autotest_header)\n        except Exception as e:\n            log.info(\"清空购物车发生异常\" + str(e))\n        cart_page = MWebCartPage(p, h5_autotest_header, browser_context=c, page_url=\"/cart\")\n        p.reload()\n        p.wait_for_timeout(5000)\n        # 断言购物车顶部文案\n>       cart_title = cart_page.FE.ele(mweb_cart_ele.ele_cart_title).text_content()\nE       AttributeError: 'NoneType' object has no attribute 'text_content'\n\nsrc\\Mweb\\EC\\testcases\\mweb_cart\\test_113360_mWeb_empty_cart_ui_ux.py:31: AttributeError"}, "description": "\n        【113360】 H5购物车-空购物车UI/UX验证\n        ", "start": 1745320120528, "stop": 1745320138630, "uuid": "08d24fcc-cb50-48d3-bd5e-920a658eda59", "historyId": "520c33e81d4b419cf08d0ddb7c3c5785", "testCaseId": "520c33e81d4b419cf08d0ddb7c3c5785", "fullName": "src.Mweb.EC.testcases.mweb_cart.test_113360_mWeb_empty_cart_ui_ux.TestMWebEmptyCartUIUX#test_113360_mWeb_empty_cart_ui_ux", "labels": [{"name": "story", "value": "【113360】 H5购物车-空购物车UI/UX验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "h5cart"}, {"name": "tag", "value": "mweb_regression"}, {"name": "tag", "value": "transaction"}, {"name": "tag", "value": "suqin"}, {"name": "parentSuite", "value": "src.Mweb.EC.testcases.mweb_cart"}, {"name": "suite", "value": "test_113360_mWeb_empty_cart_ui_ux"}, {"name": "subSuite", "value": "TestMWebEmptyCartUIUX"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "11500-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Mweb.EC.testcases.mweb_cart.test_113360_mWeb_empty_cart_ui_ux"}]}
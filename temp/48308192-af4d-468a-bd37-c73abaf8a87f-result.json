{"name": "【106444】 topx分享流程验证", "status": "passed", "description": "\n        【106444】 topx分享流程验证\n        ", "start": 1750920386709, "stop": 1750920413237, "uuid": "470590df-499e-4e08-b60f-9b8b3789a8d3", "historyId": "1d2952ff3a78fb7de2b170f69a664af4", "testCaseId": "1d2952ff3a78fb7de2b170f69a664af4", "fullName": "src.Mweb.EC.testcases.mweb_topx.test_106444_mWeb_topx_share_ui_ux.TestMWebTopxShareUIUX#test_106444_mWeb_topx_share_ui_ux", "labels": [{"name": "story", "value": "【106444】 topx分享流程验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "h5topx"}, {"name": "tag", "value": "mweb_regression"}, {"name": "tag", "value": "transaction"}, {"name": "tag", "value": "suqin"}, {"name": "parentSuite", "value": "src.Mweb.EC.testcases.mweb_topx"}, {"name": "suite", "value": "test_106444_mWeb_topx_share_ui_ux"}, {"name": "subSuite", "value": "TestMWebTopxShareUIUX"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "23528-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Mweb.EC.testcases.mweb_topx.test_106444_mWeb_topx_share_ui_ux"}]}
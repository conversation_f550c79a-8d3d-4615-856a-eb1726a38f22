{"name": "【100829】 H5-Marketplace Banner Array 点击验证", "status": "passed", "description": "\n        【100829】 H5-Marketplace Banner Array 点击验证\n        测试步骤：\n        1. 直接访问 https://tb1.sayweee.net/zh/mkpl/waterfall 页面\n        2. 等待页面加载完成后等待5秒\n        3. 检查是否出现全球购介绍弹窗，如果有则关闭\n        4. 在页面中找到 Marketplace Banner Array 3 元素\n        5. 点击该元素\n        6. 验证跳转到包含 \"mkpl/global\" 的页面\n        7. 验证URL包含必要的参数 mode=sub_page 和 hide_activity_pop=1\n        ", "start": 1750919991353, "stop": 1750920005489, "uuid": "6b8cb098-dece-4210-a4d7-2eb88dbbedc4", "historyId": "fa887a3ff29ebb1366b7fe4e41a7f5b0", "testCaseId": "fa887a3ff29ebb1366b7fe4e41a7f5b0", "fullName": "src.Mweb.EC.testcases.mweb_global+.mweb_mkpl_home.test_100829_waterfall_banner_array_3.TestWaterfallBannerArray#test_100829_click_mkpl_banner_array_3", "labels": [{"name": "story", "value": "H5-Marketplace Banner Array 验证"}, {"name": "tag", "value": "h5home"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "mweb_regression"}, {"name": "parentSuite", "value": "src.Mweb.EC.testcases.mweb_global+.mweb_mkpl_home"}, {"name": "suite", "value": "test_100829_waterfall_banner_array_3"}, {"name": "subSuite", "value": "TestWaterfallBannerArray"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "23528-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Mweb.EC.testcases.mweb_global+.mweb_mkpl_home.test_100829_waterfall_banner_array_3"}]}
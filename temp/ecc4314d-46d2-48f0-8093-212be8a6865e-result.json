{"name": "【110171】 PC/mobile购物车-匿名用户操作稍后再买", "status": "broken", "statusDetails": {"message": "playwright._impl._errors.TimeoutError: Timeout 50000ms exceeded.", "trace": "self = <test_110171_dWeb_anny_cart_save_for_later_ui_ux.TestDWebAnnyCartSaveForLaterUIUX object at 0x000001FDD7108450>\nnot_login_page = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppDat...ght\\chromium-1091\\chrome-win\\chrome.exe> version=120.0.6099.28>>, 'page': <Page url='https://www.sayweee.com/en/cart'>}\npc_anony_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...i6OH_PHdKLUR2HRUrRq8ULbWeRx_hNzmtVFREKBDqmdw_GTBX4g40eVPx-mLvvyin_KOKs9K33xSSmG3gIT18npaLnWqGqBpbzS1B_5UWA6cdhaI', ...}\n\n    @allure.title(\"【110171】 PC/mobile购物车-匿名用户操作稍后再买\")\n    def test_110171_dWeb_anny_cart_save_for_later_ui_ux(self, not_login_page: dict, pc_anony_header):\n        \"\"\"\n        【110171】 PC/mobile购物车-匿名用户操作稍后再买\n        此用例的校验点有：\n        1. 匿名用户，点击猜你喜欢合集的商品，并加购\n        2. 进入购物车，点击稍后再买按钮\n        3. 断言拉起注册登录页面\n        \"\"\"\n        p: Page = not_login_page.get(\"page\")\n        c = not_login_page.get(\"context\")\n    \n        # 直接进入指定页面\n>       cart_page = DWebCartPage(p, pc_anony_header, browser_context=c, page_url=\"/cart\")\n\nsrc\\Dweb\\EC\\testcases\\dweb_cart\\test_110171_dWeb_anny_cart_save_for_later_ui_ux.py:28: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\nsrc\\Dweb\\EC\\dweb_pages\\dweb_page_cart\\dweb_page_cart.py:29: in __init__\n    self.home_page_switch_lang(lang=\"English\")\nsrc\\Dweb\\EC\\dweb_pages\\dweb_common_page.py:35: in home_page_switch_lang\n    self.page.get_by_test_id(\"wid-language\").click()\nvenv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py:15784: in click\n    self._sync(\nvenv\\Lib\\site-packages\\playwright\\_impl\\_locator.py:159: in click\n    return await self._frame.click(self._selector, strict=True, **params)\nvenv\\Lib\\site-packages\\playwright\\_impl\\_frame.py:484: in click\n    await self._channel.send(\"click\", locals_to_params(locals()))\nvenv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:62: in send\n    return await self._connection.wrap_api_call(\nvenv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:492: in wrap_api_call\n    return await cb()\n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n\nself = <playwright._impl._connection.Channel object at 0x000001FDD7DA83D0>\nmethod = 'click'\nparams = {'selector': 'internal:testid=[data-testid=\"wid-language\"s]', 'strict': True}\nreturn_as_dict = False\n\n    async def inner_send(\n        self, method: str, params: Optional[Dict], return_as_dict: bool\n    ) -> Any:\n        if params is None:\n            params = {}\n        callback = self._connection._send_message_to_server(\n            self._object, method, params\n        )\n        if self._connection._error:\n            error = self._connection._error\n            self._connection._error = None\n            raise error\n        done, _ = await asyncio.wait(\n            {\n                self._connection._transport.on_error_future,\n                callback.future,\n            },\n            return_when=asyncio.FIRST_COMPLETED,\n        )\n        if not callback.future.done():\n            callback.future.cancel()\n>       result = next(iter(done)).result()\nE       playwright._impl._errors.TimeoutError: Timeout 50000ms exceeded.\n\nvenv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:100: TimeoutError"}, "description": "\n        【110171】 PC/mobile购物车-匿名用户操作稍后再买\n        此用例的校验点有：\n        1. 匿名用户，点击猜你喜欢合集的商品，并加购\n        2. 进入购物车，点击稍后再买按钮\n        3. 断言拉起注册登录页面\n        ", "start": 1750650695174, "stop": 1750650747843, "uuid": "28495ce1-f63e-4651-b518-0c8da85cd8cd", "historyId": "543af7cfc8da0f2860cb6658ed4ba843", "testCaseId": "543af7cfc8da0f2860cb6658ed4ba843", "fullName": "src.Dweb.EC.testcases.dweb_cart.test_110171_dWeb_anny_cart_save_for_later_ui_ux.TestDWebAnnyCartSaveForLaterUIUX#test_110171_dWeb_anny_cart_save_for_later_ui_ux", "labels": [{"name": "story", "value": "【110171】 PC/mobile购物车-匿名用户操作稍后再买"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "pccart"}, {"name": "tag", "value": "dweb_regression"}, {"name": "tag", "value": "transaction"}, {"name": "tag", "value": "suqin"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.dweb_cart"}, {"name": "suite", "value": "test_110171_dWeb_anny_cart_save_for_later_ui_ux"}, {"name": "subSuite", "value": "TestDWebAnnyCartSaveForLaterUIUX"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "15096-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.dweb_cart.test_110171_dWeb_anny_cart_save_for_later_ui_ux"}]}
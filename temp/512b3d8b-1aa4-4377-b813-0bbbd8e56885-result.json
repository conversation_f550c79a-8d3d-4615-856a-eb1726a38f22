{"name": "【100830】 H5-Marketplace Coupon List 点击验证", "status": "passed", "description": "\n        【100830】 H5-Marketplace Coupon List 点击验证\n        测试步骤：\n        1. 直接访问 mkpl/waterfall 页面\n        2. 等待页面加载完成后等待5秒\n        3. 检查是否出现全球购介绍弹窗，如果有则关闭\n        4. 在页面中找到class=\"w-11 h-11 rounded-700 mr-2.5 overflow-hidden flex-none\"对应的元素\n        5. 点击该元素\n        6. 验证跳转到包含 \"mkpl/coupon/landing\" 的页面\n        ", "start": 1750920005724, "stop": 1750920023160, "uuid": "2e894d3f-756e-46d8-abd3-3e07be1d64fd", "historyId": "077901487029dc69ca8ec365f1d6f35d", "testCaseId": "077901487029dc69ca8ec365f1d6f35d", "fullName": "src.Mweb.EC.testcases.mweb_global+.mweb_mkpl_home.test_100830_waterfall_coupon_list.TestWaterfallCouponList#test_100830_click_coupon_list", "labels": [{"name": "story", "value": "H5-Marketplace Coupon List 验证"}, {"name": "tag", "value": "h5home"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "mweb_regression"}, {"name": "parentSuite", "value": "src.Mweb.EC.testcases.mweb_global+.mweb_mkpl_home"}, {"name": "suite", "value": "test_100830_waterfall_coupon_list"}, {"name": "subSuite", "value": "TestWaterfallCouponList"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "23528-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Mweb.EC.testcases.mweb_global+.mweb_mkpl_home.test_100830_waterfall_coupon_list"}]}
{"name": "H5页面元素操作", "status": "passed", "start": 1750920109356, "stop": 1750920152000, "uuid": "be354c91-bcdc-4e8c-b00e-ca4e69dd579b", "historyId": "46c4e7308e8516c797819b452890c0c2", "testCaseId": "46c4e7308e8516c797819b452890c0c2", "fullName": "src.Mweb.EC.testcases.mweb_home.test_mweb_home.TestH5HomePage#test_mweb_home_page_search_operations", "labels": [{"name": "story", "value": "H5首页操作"}, {"name": "tag", "value": "h5home"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "mweb_regression"}, {"name": "parentSuite", "value": "src.Mweb.EC.testcases.mweb_home"}, {"name": "suite", "value": "test_mweb_home"}, {"name": "subSuite", "value": "TestH5HomePage"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "23528-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Mweb.EC.testcases.mweb_home.test_mweb_home"}]}
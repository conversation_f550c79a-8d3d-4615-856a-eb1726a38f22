{"name": "PC端-从订单详情页新增地址UI/UX验证", "status": "skipped", "statusDetails": {"message": "Skipped: not implemented yet", "trace": "('D:\\\\MyWork\\\\Python\\\\EC-demo\\\\qa-ui-dmweb\\\\src\\\\Dweb\\\\EC\\\\testcases\\\\dweb_porder\\\\dweb_address\\\\test_110841_dWeb_add_address_ui_ux.py', 72, 'Skipped: not implemented yet')"}, "description": "\n        PC端-从订单详情页新增地址UI/UX验证\n        此用例的校验点有：\n        1. 进入订单详情页，点击更改地址（使用get_by_test_id定位）\n        2. 点击\"新增地址\"按钮（使用get_by_test_id定位），进入新增地址页面\n        3. 填写地址信息（街道地址、姓名、电话、备注）\n        4. 保存地址，验证地址簿中新增了一条地址\n        ", "start": 1750652090192, "stop": 1750652090192, "uuid": "87214e13-f84d-44a9-956f-743577e9fb40", "historyId": "4ebb1c5dcfeba70518e8f61935577599", "testCaseId": "4ebb1c5dcfeba70518e8f61935577599", "fullName": "src.Dweb.EC.testcases.dweb_porder.dweb_address.test_110841_dWeb_add_address_ui_ux.TestDWebAddAddressUIUX#test_dWeb_add_address_from_order_detail_ui_ux", "labels": [{"name": "story", "value": "PC端-新增地址UI/UX验证"}, {"name": "tag", "value": "@pytest.mark.skip(reason='not implemented yet')"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "dweb_regression"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.dweb_porder.dweb_address"}, {"name": "suite", "value": "test_110841_dWeb_add_address_ui_ux"}, {"name": "subSuite", "value": "TestDWebAddAddressUIUX"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "15096-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.dweb_porder.dweb_address.test_110841_dWeb_add_address_ui_ux"}]}
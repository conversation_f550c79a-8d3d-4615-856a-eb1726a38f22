{"name": "【112065】 PC-PDP-商品组-只有一个属性模块UI/UX验证", "status": "broken", "statusDetails": {"message": "AttributeError: 'NoneType' object has no attribute 'is_visible'", "trace": "self = <src.Dweb.EC.testcases.dweb_product.dweb_pdp.test_112065_dWeb_pdp_product_group_ui_ux.TestDWebPDPProductGroupUIUX object at 0x000001FDD7190990>\npage = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppDat...> version=120.0.6099.28>>, 'page': <Page url='https://www.sayweee.com/en/product/Vita-Honey-Chrysanthemum-Tea/106422'>}\npc_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...sQTAcnJR8A7JAOh1ZSD-Pa4ZLso29CFKKRkuhCy5rGmgdHxwcT2I5ZgjWzyOj35XTNm6dqAXzkKCpOvX2w-xkB_i5g-cBs2cJEMKdLXqajR1j72s', ...}\nlogin_trace = None\n\n    @allure.title(\"【112065】 PC-PDP-商品组-只有一个属性模块UI/UX验证\")\n    def test_112065_dWeb_pdp_product_group_ui_ux(self, page: dict, pc_autotest_header, login_trace):\n        \"\"\"\n        【112065】 PC-PDP-商品组-只有一个属性模块UI/UX验证\n        \"\"\"\n        p: Page = page.get(\"page\")\n        c = page.get(\"context\")\n        # 1.直接进入指定pdp页面\n        pdp_page = DWebPDPPage(p, pc_autotest_header, browser_context=c,\n                               page_url=\"/product/Vita-Honey-Chrysanthemum-Tea-250ml-6/106422\")\n        initial_url = p.url\n        p.wait_for_timeout(5000)\n        # 2.滚动到指定位置\n        scroll_one_page_until(p, dweb_pdp_ele.ele_product_group)\n        # 3.断言product group 文案存在\n>       assert pdp_page.FE.ele(dweb_pdp_ele.ele_product_group_title).is_visible()\nE       AttributeError: 'NoneType' object has no attribute 'is_visible'\n\nsrc\\Dweb\\EC\\testcases\\dweb_product\\dweb_pdp\\test_112065_dWeb_pdp_product_group_ui_ux.py:29: AttributeError"}, "description": "\n        【112065】 PC-PDP-商品组-只有一个属性模块UI/UX验证\n        ", "start": 1750652219052, "stop": 1750652282005, "uuid": "3e57f775-9629-4ca3-a7c4-25a875e02f55", "historyId": "e22e665f5c2bc7f79f746203f0239764", "testCaseId": "e22e665f5c2bc7f79f746203f0239764", "fullName": "src.Dweb.EC.testcases.dweb_product.dweb_pdp.test_112065_dWeb_pdp_product_group_ui_ux.TestDWebPDPProductGroupUIUX#test_112065_dWeb_pdp_product_group_ui_ux", "labels": [{"name": "story", "value": "【112065】 PC-PDP-商品组-只有一个属性模块UI/UX验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "pcpdp"}, {"name": "tag", "value": "dweb_regression"}, {"name": "tag", "value": "transaction"}, {"name": "tag", "value": "suqin"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.dweb_product.dweb_pdp"}, {"name": "suite", "value": "test_112065_dWeb_pdp_product_group_ui_ux"}, {"name": "subSuite", "value": "TestDWebPDPProductGroupUIUX"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "15096-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.dweb_product.dweb_pdp.test_112065_dWeb_pdp_product_group_ui_ux"}]}
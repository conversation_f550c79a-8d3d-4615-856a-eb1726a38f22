{"name": "【108558】 购物车页面-切换日期验证", "status": "broken", "statusDetails": {"message": "playwright._impl._errors.TimeoutError: Timeout 50000ms exceeded.", "trace": "self = <src.Dweb.EC.testcases.dweb_porder.dweb_delivery_date.test_108558_dWeb_change_delivery_date_on_cart_page_ui_ux.TestDWebChangeDeliveryDateOnCartPageUIUX object at 0x000001FDD7179CD0>\npage = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppDat...ght\\chromium-1091\\chrome-win\\chrome.exe> version=120.0.6099.28>>, 'page': <Page url='https://www.sayweee.com/en/cart'>}\npc_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...sQTAcnJR8A7JAOh1ZSD-Pa4ZLso29CFKKRkuhCy5rGmgdHxwcT2I5ZgjWzyOj35XTNm6dqAXzkKCpOvX2w-xkB_i5g-cBs2cJEMKdLXqajR1j72s', ...}\nlogin_trace = None\n\n    @allure.title(\"【108558】 购物车页面-切换日期验证\")\n    def test_108558_dWeb_change_delivery_date_on_cart_page_ui_ux(self, page: dict, pc_autotest_header, login_trace):\n        \"\"\"\n        【108558】 购物车页面-切换日期验证\n        \"\"\"\n        p: Page = page.get(\"page\")\n        c = page.get(\"context\")\n        # 直接进入指定页面\n>       cart_page = DWebCartPage(p, pc_autotest_header, browser_context=c, page_url=\"/cart\")\n\nsrc\\Dweb\\EC\\testcases\\dweb_porder\\dweb_delivery_date\\test_108558_dWeb_change_delivery_date_on_cart_page_ui_ux.py:26: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\nsrc\\Dweb\\EC\\dweb_pages\\dweb_page_cart\\dweb_page_cart.py:29: in __init__\n    self.home_page_switch_lang(lang=\"English\")\nsrc\\Dweb\\EC\\dweb_pages\\dweb_common_page.py:35: in home_page_switch_lang\n    self.page.get_by_test_id(\"wid-language\").click()\nvenv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py:15784: in click\n    self._sync(\nvenv\\Lib\\site-packages\\playwright\\_impl\\_locator.py:159: in click\n    return await self._frame.click(self._selector, strict=True, **params)\nvenv\\Lib\\site-packages\\playwright\\_impl\\_frame.py:484: in click\n    await self._channel.send(\"click\", locals_to_params(locals()))\nvenv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:62: in send\n    return await self._connection.wrap_api_call(\nvenv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:492: in wrap_api_call\n    return await cb()\n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n\nself = <playwright._impl._connection.Channel object at 0x000001FDD792DE90>\nmethod = 'click'\nparams = {'selector': 'internal:testid=[data-testid=\"wid-language\"s]', 'strict': True}\nreturn_as_dict = False\n\n    async def inner_send(\n        self, method: str, params: Optional[Dict], return_as_dict: bool\n    ) -> Any:\n        if params is None:\n            params = {}\n        callback = self._connection._send_message_to_server(\n            self._object, method, params\n        )\n        if self._connection._error:\n            error = self._connection._error\n            self._connection._error = None\n            raise error\n        done, _ = await asyncio.wait(\n            {\n                self._connection._transport.on_error_future,\n                callback.future,\n            },\n            return_when=asyncio.FIRST_COMPLETED,\n        )\n        if not callback.future.done():\n            callback.future.cancel()\n>       result = next(iter(done)).result()\nE       playwright._impl._errors.TimeoutError: Timeout 50000ms exceeded.\n\nvenv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:100: TimeoutError"}, "description": "\n        【108558】 购物车页面-切换日期验证\n        ", "start": 1750652090254, "stop": 1750652141694, "uuid": "768da462-26e9-4db5-ae49-2b240ea4bd01", "historyId": "9bc577db4c25d102d5cd0dafd868a360", "testCaseId": "9bc577db4c25d102d5cd0dafd868a360", "fullName": "src.Dweb.EC.testcases.dweb_porder.dweb_delivery_date.test_108558_dWeb_change_delivery_date_on_cart_page_ui_ux.TestDWebChangeDeliveryDateOnCartPageUIUX#test_108558_dWeb_change_delivery_date_on_cart_page_ui_ux", "labels": [{"name": "story", "value": "【108558】 购物车页面-切换日期验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "dweb_porder"}, {"name": "tag", "value": "dweb_regression"}, {"name": "tag", "value": "transaction"}, {"name": "tag", "value": "suqin"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.dweb_porder.dweb_delivery_date"}, {"name": "suite", "value": "test_108558_dWeb_change_delivery_date_on_cart_page_ui_ux"}, {"name": "subSuite", "value": "TestDWebChangeDeliveryDateOnCartPageUIUX"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "15096-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.dweb_porder.dweb_delivery_date.test_108558_dWeb_change_delivery_date_on_cart_page_ui_ux"}]}
{"name": "首页按任意关键字搜索，关键字为tofu", "status": "broken", "statusDetails": {"message": "playwright._impl._errors.TimeoutError: Timeout 50000ms exceeded.", "trace": "self = <src.Dweb.EC.testcases.dweb_home.dweb_search.test_002_home_search_page.TestSearchAtHome object at 0x000001FDD7156910>\npage = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppDat...aywright\\chromium-1091\\chrome-win\\chrome.exe> version=120.0.6099.28>>, 'page': <Page url='https://www.sayweee.com/en'>}\ndata = 'tofu'\npc_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...sQTAcnJR8A7JAOh1ZSD-Pa4ZLso29CFKKRkuhCy5rGmgdHxwcT2I5ZgjWzyOj35XTNm6dqAXzkKCpOvX2w-xkB_i5g-cBs2cJEMKdLXqajR1j72s', ...}\nlogin_trace = None\n\n    @allure.title(\"首页按任意关键字搜索，关键字为{data}\")\n    @pytest.mark.parametrize(\"data\", [\"fruit\", \"tofu\", \"rice\"])\n    def test_search_by_category(self, page: dict, data, pc_autotest_header, login_trace):\n        hsp = HomeSearchPage(page[\"page\"], pc_autotest_header)\n>       hsp.search(data)\n\nsrc\\Dweb\\EC\\testcases\\dweb_home\\dweb_search\\test_002_home_search_page.py:14: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\nsrc\\Dweb\\EC\\dweb_pages\\dweb_page_home\\dweb_page_home_search_by_category_at_home.py:34: in search\n    self.page.get_by_test_id(\"wid-main-search-no-fouce\").click()\nvenv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py:15784: in click\n    self._sync(\nvenv\\Lib\\site-packages\\playwright\\_impl\\_locator.py:159: in click\n    return await self._frame.click(self._selector, strict=True, **params)\nvenv\\Lib\\site-packages\\playwright\\_impl\\_frame.py:484: in click\n    await self._channel.send(\"click\", locals_to_params(locals()))\nvenv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:62: in send\n    return await self._connection.wrap_api_call(\nvenv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:492: in wrap_api_call\n    return await cb()\n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n\nself = <playwright._impl._connection.Channel object at 0x000001FDD792DE90>\nmethod = 'click'\nparams = {'selector': 'internal:testid=[data-testid=\"wid-main-search-no-fouce\"s]', 'strict': True}\nreturn_as_dict = False\n\n    async def inner_send(\n        self, method: str, params: Optional[Dict], return_as_dict: bool\n    ) -> Any:\n        if params is None:\n            params = {}\n        callback = self._connection._send_message_to_server(\n            self._object, method, params\n        )\n        if self._connection._error:\n            error = self._connection._error\n            self._connection._error = None\n            raise error\n        done, _ = await asyncio.wait(\n            {\n                self._connection._transport.on_error_future,\n                callback.future,\n            },\n            return_when=asyncio.FIRST_COMPLETED,\n        )\n        if not callback.future.done():\n            callback.future.cancel()\n>       result = next(iter(done)).result()\nE       playwright._impl._errors.TimeoutError: Timeout 50000ms exceeded.\n\nvenv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:100: TimeoutError"}, "parameters": [{"name": "data", "value": "'tofu'"}], "start": 1750651030282, "stop": 1750651101765, "uuid": "1dd2eef2-8784-4cb2-ad8d-522db8b3e987", "historyId": "cbbd8a15fdf7db05ff35392238a7bf97", "testCaseId": "676dd91d2fdd3f97f8c22ddee4e2ae59", "fullName": "src.Dweb.EC.testcases.dweb_home.dweb_search.test_002_home_search_page.TestSearchAtHome#test_search_by_category", "labels": [{"name": "story", "value": "首页搜索"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "dweb_regression"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.dweb_home.dweb_search"}, {"name": "suite", "value": "test_002_home_search_page"}, {"name": "subSuite", "value": "TestSearchAtHome"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "15096-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.dweb_home.dweb_search.test_002_home_search_page"}]}
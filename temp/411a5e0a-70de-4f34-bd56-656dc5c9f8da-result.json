{"name": "PC端-从订单详情页新增地址UI/UX验证", "status": "skipped", "statusDetails": {"message": "Skipped: not implemented yet", "trace": "('D:\\\\MyWork\\\\Python\\\\EC-demo\\\\qa-ui-dmweb\\\\src\\\\Dweb\\\\EC\\\\testcases\\\\dweb_address\\\\test_110841_dWeb_add_address_ui_ux.py', 72, 'Skipped: not implemented yet')"}, "description": "\n        PC端-从订单详情页新增地址UI/UX验证\n        此用例的校验点有：\n        1. 进入订单详情页，点击更改地址（使用get_by_test_id定位）\n        2. 点击\"新增地址\"按钮（使用get_by_test_id定位），进入新增地址页面\n        3. 填写地址信息（街道地址、姓名、电话、备注）\n        4. 保存地址，验证地址簿中新增了一条地址\n        ", "start": 1745315387043, "stop": 1745315387043, "uuid": "59393fd7-4f97-48cd-aa02-a5180a1e9692", "historyId": "0fe5272f3916f07de901a0b1999e0463", "testCaseId": "0fe5272f3916f07de901a0b1999e0463", "fullName": "src.Dweb.EC.testcases.dweb_address.test_110841_dWeb_add_address_ui_ux.TestDWebAddAddressUIUX#test_dWeb_add_address_from_order_detail_ui_ux", "labels": [{"name": "story", "value": "PC端-新增地址UI/UX验证"}, {"name": "tag", "value": "@pytest.mark.skip(reason='not implemented yet')"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "dweb_regression"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.dweb_address"}, {"name": "suite", "value": "test_110841_dWeb_add_address_ui_ux"}, {"name": "subSuite", "value": "TestDWebAddAddressUIUX"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "23072-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.dweb_address.test_110841_dWeb_add_address_ui_ux"}]}
{"name": "【112063】 H5-PDP-商品组-只有一个属性模块UI/UX验证", "status": "broken", "statusDetails": {"message": "AttributeError: 'NoneType' object has no attribute 'is_visible'", "trace": "self = <src.Mweb.EC.testcases.mweb_product.mweb_pdp.test_112063_mWeb_pdp_product_group_ui_ux.TestMWebPDPProductGroupUIUX object at 0x00000143FC26A610>\nphone_page = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppDat...0.6099.28>>, 'page': <Page url='https://www.sayweee.com/en/product/Vita-Honey-Chrysanthemum-Tea/106422?joinEnki=true'>}\nh5_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...uZBFvf9xCE7fdAfz0j0_peXZ2AmEY5oTjm3kGArdKc_-Yr8hE1AQywfTOCOeXcqcQvjW03ZcQ5ApcUVdOJgLZUVza2jic-6FwlXX08RYiCKroMT4', ...}\nh5_open_and_close_trace = None\n\n    @allure.title(\"【112063】 H5-PDP-商品组-只有一个属性模块UI/UX验证\")\n    def test_112063_mWeb_pdp_product_image_group_ui_ux(self, phone_page: dict, h5_autotest_header,\n                                                       h5_open_and_close_trace):\n        \"\"\"\n        【112063】 H5-PDP-商品组-只有一个属性模块UI/UX验证\n        \"\"\"\n        p: Page = phone_page.get(\"page\")\n        c = phone_page.get(\"context\")\n        # 直接进入指定pdp页面\n        pdp_page = MWebPDPPage(p, h5_autotest_header, browser_context=c,\n                               page_url=\"/product/Vita-Honey-Chrysanthemum-Tea-250ml-6/106422\")\n        p.wait_for_timeout(3000)\n        # 关闭pop\n        if p.locator(u\"//button[contains(text(), 'Continue')]\").all():\n            p.locator(u\"//button[contains(text(), 'Continue')]\").click()\n    \n        # 滚动到指定位置\n        scroll_one_page_until(p, mweb_pdp_ele.ele_product_group)\n        # 断言product group 文案存在\n>       assert pdp_page.FE.ele(mweb_pdp_ele.ele_product_group_select).is_visible()\nE       AttributeError: 'NoneType' object has no attribute 'is_visible'\n\nsrc\\Mweb\\EC\\testcases\\mweb_product\\mweb_pdp\\test_112063_mWeb_pdp_product_group_ui_ux.py:34: AttributeError"}, "description": "\n        【112063】 H5-PDP-商品组-只有一个属性模块UI/UX验证\n        ", "start": 1750920284863, "stop": 1750920349875, "uuid": "d9699a7a-b027-45a7-80a9-c1e8dda9d5e7", "historyId": "887fa89451073fcad35567098e7d7eab", "testCaseId": "887fa89451073fcad35567098e7d7eab", "fullName": "src.Mweb.EC.testcases.mweb_product.mweb_pdp.test_112063_mWeb_pdp_product_group_ui_ux.TestMWebPDPProductGroupUIUX#test_112063_mWeb_pdp_product_image_group_ui_ux", "labels": [{"name": "story", "value": "【112063】 H5-PDP-商品组-只有一个属性模块UI/UX验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "h5pdp"}, {"name": "tag", "value": "mweb_regression"}, {"name": "tag", "value": "transaction"}, {"name": "tag", "value": "suqin"}, {"name": "parentSuite", "value": "src.Mweb.EC.testcases.mweb_product.mweb_pdp"}, {"name": "suite", "value": "test_112063_mWeb_pdp_product_group_ui_ux"}, {"name": "subSuite", "value": "TestMWebPDPProductGroupUIUX"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "23528-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Mweb.EC.testcases.mweb_product.mweb_pdp.test_112063_mWeb_pdp_product_group_ui_ux"}]}
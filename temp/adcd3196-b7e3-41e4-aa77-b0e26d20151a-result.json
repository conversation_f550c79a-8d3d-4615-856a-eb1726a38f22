{"name": "【108616】 订单列表已取消tab-再来一单流程验证", "status": "failed", "statusDetails": {"message": "AssertionError: 未找到已取消tab\nassert False\n +  where False = <bound method Locator.is_visible of <Locator frame=<Frame name= url='https://www.sayweee.com/en/account/my_orders'> selector='internal:testid=[data-testid=\"wid-order-list-tab-4\"s]'>>()\n +    where <bound method Locator.is_visible of <Locator frame=<Frame name= url='https://www.sayweee.com/en/account/my_orders'> selector='internal:testid=[data-testid=\"wid-order-list-tab-4\"s]'>> = <Locator frame=<Frame name= url='https://www.sayweee.com/en/account/my_orders'> selector='internal:testid=[data-testid=\"wid-order-list-tab-4\"s]'>.is_visible", "trace": "self = <src.Dweb.EC.testcases.dweb_account.dweb_order.test_108616_dWeb_my_canceled_order_buy_again_ui_ux.TestDWebMyCanceledOrderBuyAgainUIUX object at 0x000001CEEBD0ACD0>\npage = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppDat...1091\\chrome-win\\chrome.exe> version=120.0.6099.28>>, 'page': <Page url='https://www.sayweee.com/en/account/my_orders'>}\npc_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...RQsAFh_-vqtVaibJAHZ6fUZU2ihbgNHuZ1HAQHfikwvAVCLwu8HBe8kp0Uw55Q2PxMsytyFlKXDwb8NWQkcoJjcyEaZxZrv76sytbUZ9GukJZEnU', ...}\nlogin_trace = None\n\n    @allure.title(\"【108616】 订单列表已取消tab-再来一单流程验证\")\n    def test_108616_dWeb_my_canceled_order_buy_again_ui_ux(self, page: dict, pc_autotest_header, login_trace):\n        \"\"\"\n        【108616】 订单列表已取消tab-再来一单流程验证\n        测试步骤：\n        1、进入订单列表页面，点击切换到已取消tab下\n        2、找到订单列表下再来一单按钮，右侧会撤拉出商品选择页面\n        3、商品选择页面，默认勾选全选按钮，勾选掉某个商品，全选去掉，加入购物车按钮置灰，点击全选商品全部选中，这里如果选中几个商品，加入购物车按钮上就会显示几个\n        4、点击加入购物车按钮，自动回到订单列表页面\n        3\\4 两条在 再来一单case 已包含\n        \"\"\"\n        p: Page = page.get(\"page\")\n        c = page.get(\"context\")\n    \n        # 1. 进入订单列表页面\n        order_page = DWebOrderListPage(p, pc_autotest_header, browser_context=c, page_url=\"/account/my_orders\")\n        p.wait_for_timeout(3000)\n        log.info(\"成功进入订单列表页面\")\n    \n        # 切换到已取消tab\n        canceled_tab = p.get_by_test_id(dweb_order_list_ele.order_cancelled_tab_ele)\n>       assert canceled_tab.is_visible(), \"未找到已取消tab\"\nE       AssertionError: 未找到已取消tab\nE       assert False\nE        +  where False = <bound method Locator.is_visible of <Locator frame=<Frame name= url='https://www.sayweee.com/en/account/my_orders'> selector='internal:testid=[data-testid=\"wid-order-list-tab-4\"s]'>>()\nE        +    where <bound method Locator.is_visible of <Locator frame=<Frame name= url='https://www.sayweee.com/en/account/my_orders'> selector='internal:testid=[data-testid=\"wid-order-list-tab-4\"s]'>> = <Locator frame=<Frame name= url='https://www.sayweee.com/en/account/my_orders'> selector='internal:testid=[data-testid=\"wid-order-list-tab-4\"s]'>.is_visible\n\nsrc\\Dweb\\EC\\testcases\\dweb_account\\dweb_order\\test_108616_dWeb_my_canceled_order_buy_again_ui_ux.py:35: AssertionError"}, "description": "\n        【108616】 订单列表已取消tab-再来一单流程验证\n        测试步骤：\n        1、进入订单列表页面，点击切换到已取消tab下\n        2、找到订单列表下再来一单按钮，右侧会撤拉出商品选择页面\n        3、商品选择页面，默认勾选全选按钮，勾选掉某个商品，全选去掉，加入购物车按钮置灰，点击全选商品全部选中，这里如果选中几个商品，加入购物车按钮上就会显示几个\n        4、点击加入购物车按钮，自动回到订单列表页面\n        3\u0004 两条在 再来一单case 已包含\n        ", "start": *************, "stop": *************, "uuid": "dd3a5b18-1631-400c-b2d7-77bffb13d8c0", "historyId": "c07889fa49f3f486089ccb09940f7445", "testCaseId": "c07889fa49f3f486089ccb09940f7445", "fullName": "src.Dweb.EC.testcases.dweb_account.dweb_order.test_108616_dWeb_my_canceled_order_buy_again_ui_ux.TestDWebMyCanceledOrderBuyAgainUIUX#test_108616_dWeb_my_canceled_order_buy_again_ui_ux", "labels": [{"name": "story", "value": "【108616】 订单列表已取消tab-再来一单流程验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "dweb_porder"}, {"name": "tag", "value": "dweb_regression"}, {"name": "tag", "value": "transaction"}, {"name": "tag", "value": "suqin"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.dweb_account.dweb_order"}, {"name": "suite", "value": "test_108616_dWeb_my_canceled_order_buy_again_ui_ux"}, {"name": "subSuite", "value": "TestDWebMyCanceledOrderBuyAgainUIUX"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "18600-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.dweb_account.dweb_order.test_108616_dWeb_my_canceled_order_buy_again_ui_ux"}]}
{"name": "首页切换相同zipcode", "status": "passed", "description": "\n        MS用例号: 110756: 切换zipcode逻辑\n        ", "start": 1745316753036, "stop": 1745316773936, "uuid": "4a8c598b-c3a3-4ca4-abf1-d257c1817b6b", "historyId": "7df87e45d9848f0d009f6a6319b56faa", "testCaseId": "7df87e45d9848f0d009f6a6319b56faa", "fullName": "src.Dweb.EC.testcases.dweb_home.test_003_page_home.TestAtHome#test_switch_same_zipcode", "labels": [{"name": "story", "value": "首页搜索by category"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "dweb_regression"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.dweb_home"}, {"name": "suite", "value": "test_003_page_home"}, {"name": "subSuite", "value": "TestAtHome"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "23072-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.dweb_home.test_003_page_home"}]}
{"name": "【110171】 PC/mobile购物车-匿名用户操作稍后再买", "status": "broken", "statusDetails": {"message": "AttributeError: 'NoneType' object has no attribute 'is_visible'", "trace": "self = <src.Mweb.EC.testcases.mweb_cart.test_110171_mWeb_anny_cart_save_for_later_ui_ux.TestMWebAnnyCartSaveForLaterUIUX object at 0x0000023289191590>\nnot_login_phone_page = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppDat...091\\chrome-win\\chrome.exe> version=120.0.6099.28>>, 'page': <Page url='https://www.sayweee.com/en/cart?joinEnki=true'>}\nh5_anony_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...4SI2DUxEBRHOB5ixtNByJwOWTTmq94tJIgDDJSKvfTgDCFlB8Q4EE_O3eOiQOwgXZI4YdLFxQM0DBm_nGy96vlmm8vvzQbD13-4cKi-TwDQzlLx0', ...}\n\n    @allure.title(\"【110171】 PC/mobile购物车-匿名用户操作稍后再买\")\n    def test_110171_mWeb_anny_cart_save_for_later_ui_ux(self, not_login_phone_page: dict, h5_anony_header):\n        \"\"\"\n        【110171】 PC/mobile购物车-匿名用户操作稍后再买\n        \"\"\"\n        p: Page = not_login_phone_page.get(\"page\")\n        c = not_login_phone_page.get(\"context\")\n    \n        # 直接进入指定页面\n        cart_page = MWebCartPage(p, h5_anony_header, browser_context=c,\n                                 page_url=\"/cart\")\n        p.wait_for_timeout(3000)\n        # 滚动到指定位置-猜你喜欢\n        scroll_one_page_until(p, mweb_cart_ele.ele_recommend_tab)\n    \n        # 获取猜你喜欢商品\n        recommend_card = cart_page.FE.eles(mweb_cart_ele.ele_recommend_module_card)\n        for index1, item1 in enumerate(recommend_card):\n            # 加购推荐商品\n            item1.query_selector(u\"//div[@data-testid='btn-atc-plus']\").click()\n            p.wait_for_timeout(1000)\n            if index1 == 2:\n                break\n        # 回到购物车第一个商品位置\n        p.query_selector(f\"{mweb_cart_ele.ele_cart_normal_card}\" + \"[1]\").scroll_into_view_if_needed()\n        p.wait_for_timeout(2000)\n        # 点击购物车商品 稍后购买按钮\n        normal_card = cart_page.FE.eles(mweb_cart_ele.ele_cart_normal_card)\n        for index2, item2 in enumerate(normal_card):\n            item2.query_selector(u\"//div[@data-testid='btn-save-for-later']\").click()\n            if index2 == 0:\n                break\n        p.wait_for_timeout(1000)\n        # 匿名用户，断言拉起注册登录页面\n>       assert p.query_selector(u\"//span[@data-role='tag-item-active']\").is_visible()\nE       AttributeError: 'NoneType' object has no attribute 'is_visible'\n\nsrc\\Mweb\\EC\\testcases\\mweb_cart\\test_110171_mWeb_anny_cart_save_for_later_ui_ux.py:49: AttributeError"}, "description": "\n        【110171】 PC/mobile购物车-匿名用户操作稍后再买\n        ", "start": 1745320009005, "stop": 1745320025168, "uuid": "92396474-57ef-40a9-ae2d-84e7c48683df", "historyId": "9cadfdcd50937c19f8c3ca3df1968d1b", "testCaseId": "9cadfdcd50937c19f8c3ca3df1968d1b", "fullName": "src.Mweb.EC.testcases.mweb_cart.test_110171_mWeb_anny_cart_save_for_later_ui_ux.TestMWebAnnyCartSaveForLaterUIUX#test_110171_mWeb_anny_cart_save_for_later_ui_ux", "labels": [{"name": "story", "value": "【110171】 PC/mobile购物车-匿名用户操作稍后再买"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "h5cart"}, {"name": "tag", "value": "mweb_regression"}, {"name": "tag", "value": "transaction"}, {"name": "tag", "value": "suqin"}, {"name": "parentSuite", "value": "src.Mweb.EC.testcases.mweb_cart"}, {"name": "suite", "value": "test_110171_mWeb_anny_cart_save_for_later_ui_ux"}, {"name": "subSuite", "value": "TestMWebAnnyCartSaveForLaterUIUX"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "11500-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Mweb.EC.testcases.mweb_cart.test_110171_mWeb_anny_cart_save_for_later_ui_ux"}]}
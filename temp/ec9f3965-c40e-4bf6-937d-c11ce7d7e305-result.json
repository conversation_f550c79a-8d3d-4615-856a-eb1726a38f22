{"name": "【108560】 再来一单-切换日期验证", "status": "passed", "description": "\n        【108560】 再来一单-切换日期验证\n        ", "start": 1750920191628, "stop": 1750920210353, "uuid": "6cc76004-e55d-4a26-beea-9a0d500073e1", "historyId": "aec360d267f1d777c0d042c13de701af", "testCaseId": "aec360d267f1d777c0d042c13de701af", "fullName": "src.Mweb.EC.testcases.mweb_home.mweb_porder.test_108560_mWeb_change_delivery_date_on_buy_again_page_ui_ux.TestMWebChangeDeliveryDateOnBuyAgainPageUIUX#test_108560_mWeb_change_delivery_date_on_buy_again_page_ui_ux", "labels": [{"name": "story", "value": "【108560】 再来一单-切换日期验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "h5order"}, {"name": "tag", "value": "mweb_regression"}, {"name": "tag", "value": "transaction"}, {"name": "tag", "value": "suqin"}, {"name": "parentSuite", "value": "src.Mweb.EC.testcases.mweb_home.mweb_porder"}, {"name": "suite", "value": "test_108560_mWeb_change_delivery_date_on_buy_again_page_ui_ux"}, {"name": "subSuite", "value": "TestMWebChangeDeliveryDateOnBuyAgainPageUIUX"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "23528-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Mweb.EC.testcases.mweb_home.mweb_porder.test_108560_mWeb_change_delivery_date_on_buy_again_page_ui_ux"}]}
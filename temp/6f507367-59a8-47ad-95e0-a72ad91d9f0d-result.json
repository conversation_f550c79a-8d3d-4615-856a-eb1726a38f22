{"name": "【108701】 PDP-分享-商品图片分享UI/UX验证", "status": "passed", "description": "\n        【108701】 PDP-分享-商品图片分享UI/UX验证\n        ", "start": 1750920254188, "stop": 1750920284348, "uuid": "d390aa2d-3a15-458b-bceb-9bdb33e18aeb", "historyId": "7d61ac0ef40923e66e307db3f1c7a7c4", "testCaseId": "7d61ac0ef40923e66e307db3f1c7a7c4", "fullName": "src.Mweb.EC.testcases.mweb_product.mweb_pdp.test_108701_mWeb_pdp_product_image_share_ui_ux.TestMWebPDPProductImgShareUIUX#test_112063_mWeb_pdp_product_group_ui_ux", "labels": [{"name": "story", "value": "【108701】 PDP-分享-商品图片分享UI/UX验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "h5pdp"}, {"name": "tag", "value": "mweb_regression"}, {"name": "tag", "value": "transaction"}, {"name": "tag", "value": "suqin"}, {"name": "parentSuite", "value": "src.Mweb.EC.testcases.mweb_product.mweb_pdp"}, {"name": "suite", "value": "test_108701_mWeb_pdp_product_image_share_ui_ux"}, {"name": "subSuite", "value": "TestMWebPDPProductImgShareUIUX"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "23528-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Mweb.EC.testcases.mweb_product.mweb_pdp.test_108701_mWeb_pdp_product_image_share_ui_ux"}]}
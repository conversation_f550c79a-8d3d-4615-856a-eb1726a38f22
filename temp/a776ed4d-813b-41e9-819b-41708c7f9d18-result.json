{"name": "【112145】 PC-PDP-video list UI/UX验证证", "status": "broken", "statusDetails": {"message": "playwright._impl._errors.TimeoutError: Timeout 50000ms exceeded.", "trace": "self = <src.Dweb.EC.testcases.dweb_product.dweb_pdp.test_112145_dWeb_pdp_video_list_ui_ux.TestDWebPDPVideoListUIUX object at 0x000001FDD7191B10>\npage = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppDat...6099.28>>, 'page': <Page url='https://www.sayweee.com/en/product/DuoDuo-Vegetarian-Rice-Mushroom-Buns-Frozen-1/34460'>}\npc_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...sQTAcnJR8A7JAOh1ZSD-Pa4ZLso29CFKKRkuhCy5rGmgdHxwcT2I5ZgjWzyOj35XTNm6dqAXzkKCpOvX2w-xkB_i5g-cBs2cJEMKdLXqajR1j72s', ...}\nlogin_trace = None\n\n    @allure.title(\"【112145】 PC-PDP-video list UI/UX验证证\")\n    def test_112145_dWeb_pdp_video_list_ui_ux(self, page: dict, pc_autotest_header, login_trace):\n        \"\"\"\n        【112145】 PC-PDP-video list UI/UX验证\n        \"\"\"\n        p: Page = page.get(\"page\")\n        c = page.get(\"context\")\n        # 1.直接进入指定pdp页面\n        pdp_page = DWebPDPPage(p, pc_autotest_header, browser_context=c,\n                               page_url=\"/product/DuoDuo-Vegetarian-Rice-Mushroom-Buns-Frozen-1/34460\")\n        # 2.滚动到指定位置\n        scroll_one_page_until(p, dweb_pdp_ele.ele_mod_videos)\n        pdp_page.FE.ele(dweb_pdp_ele.ele_video_card).hover()\n>       p.get_by_test_id(\"wid-slide-panel-arrow-right\").click()\n\nsrc\\Dweb\\EC\\testcases\\dweb_product\\dweb_pdp\\test_112145_dWeb_pdp_video_list_ui_ux.py:27: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\nvenv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py:15784: in click\n    self._sync(\nvenv\\Lib\\site-packages\\playwright\\_impl\\_locator.py:159: in click\n    return await self._frame.click(self._selector, strict=True, **params)\nvenv\\Lib\\site-packages\\playwright\\_impl\\_frame.py:484: in click\n    await self._channel.send(\"click\", locals_to_params(locals()))\nvenv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:62: in send\n    return await self._connection.wrap_api_call(\nvenv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:492: in wrap_api_call\n    return await cb()\n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n\nself = <playwright._impl._connection.Channel object at 0x000001FDD792DE90>\nmethod = 'click'\nparams = {'selector': 'internal:testid=[data-testid=\"wid-slide-panel-arrow-right\"s]', 'strict': True}\nreturn_as_dict = False\n\n    async def inner_send(\n        self, method: str, params: Optional[Dict], return_as_dict: bool\n    ) -> Any:\n        if params is None:\n            params = {}\n        callback = self._connection._send_message_to_server(\n            self._object, method, params\n        )\n        if self._connection._error:\n            error = self._connection._error\n            self._connection._error = None\n            raise error\n        done, _ = await asyncio.wait(\n            {\n                self._connection._transport.on_error_future,\n                callback.future,\n            },\n            return_when=asyncio.FIRST_COMPLETED,\n        )\n        if not callback.future.done():\n            callback.future.cancel()\n>       result = next(iter(done)).result()\nE       playwright._impl._errors.TimeoutError: Timeout 50000ms exceeded.\n\nvenv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:100: TimeoutError"}, "description": "\n        【112145】 PC-PDP-video list UI/UX验证\n        ", "start": 1750652287510, "stop": 1750652390316, "uuid": "734eab21-3bac-4ec3-920b-aa547c06a4ea", "historyId": "ac510d4824b2db0898354c282778e421", "testCaseId": "ac510d4824b2db0898354c282778e421", "fullName": "src.Dweb.EC.testcases.dweb_product.dweb_pdp.test_112145_dWeb_pdp_video_list_ui_ux.TestDWebPDPVideoListUIUX#test_112145_dWeb_pdp_video_list_ui_ux", "labels": [{"name": "story", "value": "【112145】 PC-PDP-video list UI/UX验证证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "pcpdp"}, {"name": "tag", "value": "dweb_regression"}, {"name": "tag", "value": "transaction"}, {"name": "tag", "value": "suqin"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.dweb_product.dweb_pdp"}, {"name": "suite", "value": "test_112145_dWeb_pdp_video_list_ui_ux"}, {"name": "subSuite", "value": "TestDWebPDPVideoListUIUX"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "15096-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.dweb_product.dweb_pdp.test_112145_dWeb_pdp_video_list_ui_ux"}]}
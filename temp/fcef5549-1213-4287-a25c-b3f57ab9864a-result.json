{"name": "进入fbw pdp页面检查元素并加购", "status": "broken", "statusDetails": {"message": "IndexError: list index out of range", "trace": "self = <src.Dweb.EC.testcases.dweb_product.dweb_pdp.test_100_pdp_page_check.TestAtPDP object at 0x000001FDD71D7610>\nsetup = <src.Dweb.EC.dweb_pages.dweb_page_pdp.dweb_page_pdp.DWebPDPPage object at 0x000001FDD7DED090>\npdp_fbw_url = 'https://www.sayweee.com/en/product/Pork-floss-with-mochi-cake-2pc/2044455?category=freshbakery02&parent_category=freshbakery'\nlogin_trace = None\n\n    @allure.title(\"进入fbw pdp页面检查元素并加购\")\n    @pytest.mark.parametrize(\"pdp_fbw_url\", pdp_fbw_url)\n    def test_check_fbw_pdp_and_add_to_cart(self, setup, pdp_fbw_url, login_trace):\n        \"\"\"\n        100616 验证Global+ FBW 商品PDP UI-UX\n        \"\"\"\n>       setup.goto_fbw_pdp_and_check(pdp_fbw_url)\n\nsrc\\Dweb\\EC\\testcases\\dweb_product\\dweb_pdp\\test_100_pdp_page_check.py:37: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\nsrc\\Dweb\\EC\\dweb_pages\\dweb_page_pdp\\dweb_page_pdp.py:64: in goto_fbw_pdp_and_check\n    self._check_fbw_pdp_page()\nsrc\\Dweb\\EC\\dweb_pages\\dweb_page_pdp\\dweb_page_pdp.py:94: in _check_fbw_pdp_page\n    self.pdp_dweb_page_common_check()\n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n\nself = <src.Dweb.EC.dweb_pages.dweb_page_pdp.dweb_page_pdp.DWebPDPPage object at 0x000001FDD7DED090>\n\n    def pdp_dweb_page_common_check(self):\n        assert self.page.get_by_test_id(\"wid-pdp-thumbnail-0\").is_visible()\n        assert self.page.get_by_test_id(\"wid-pdp-zoom-img\").is_visible()\n>       assert self.page.get_by_test_id(\"btn-favorite\").all()[0].is_visible()\nE       IndexError: list index out of range\n\nsrc\\Dweb\\EC\\dweb_pages\\dweb_common_page.py:52: IndexError"}, "description": "\n        100616 验证Global+ FBW 商品PDP UI-UX\n        ", "parameters": [{"name": "pdp_fbw_url", "value": "'https://www.sayweee.com/en/product/Pork-floss-with-mochi-cake-2pc/2044455?category=freshbakery02&parent_category=freshbakery'"}], "start": 1750652152274, "stop": 1750652157239, "uuid": "7a3351ac-c3da-4ee1-ae20-101a797c3180", "historyId": "2616b7d26f14542c21024f4b175c2c85", "testCaseId": "1e65c3fcc2524476b1e50f8ab45318e1", "fullName": "src.Dweb.EC.testcases.dweb_product.dweb_pdp.test_100_pdp_page_check.TestAtPDP#test_check_fbw_pdp_and_add_to_cart", "labels": [{"name": "story", "value": "产品PDP页面校验"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "pdp"}, {"name": "tag", "value": "smoke"}, {"name": "tag", "value": "dweb_regression"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.dweb_product.dweb_pdp"}, {"name": "suite", "value": "test_100_pdp_page_check"}, {"name": "subSuite", "value": "TestAtPDP"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "15096-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.dweb_product.dweb_pdp.test_100_pdp_page_check"}]}
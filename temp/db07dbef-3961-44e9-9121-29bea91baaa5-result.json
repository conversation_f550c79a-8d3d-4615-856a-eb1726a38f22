{"name": "【108220】 订单列表已发货 tab订单流程验证", "status": "failed", "statusDetails": {"message": "AssertionError: 未找到已发货tab\nassert False\n +  where False = <bound method Locator.is_visible of <Locator frame=<Frame name= url='https://www.sayweee.com/en/account/my_orders'> selector='internal:testid=[data-testid=\"wid-order-list-tab-3\"s]'>>()\n +    where <bound method Locator.is_visible of <Locator frame=<Frame name= url='https://www.sayweee.com/en/account/my_orders'> selector='internal:testid=[data-testid=\"wid-order-list-tab-3\"s]'>> = <Locator frame=<Frame name= url='https://www.sayweee.com/en/account/my_orders'> selector='internal:testid=[data-testid=\"wid-order-list-tab-3\"s]'>.is_visible", "trace": "self = <src.Dweb.EC.testcases.dweb_account.dweb_order.test_108220_dWeb_my_shipped_order_ui_ux.TestDWebMyShippedOrderUIUX object at 0x000001CEEBDF8450>\npage = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppDat...1091\\chrome-win\\chrome.exe> version=120.0.6099.28>>, 'page': <Page url='https://www.sayweee.com/en/account/my_orders'>}\npc_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...RQsAFh_-vqtVaibJAHZ6fUZU2ihbgNHuZ1HAQHfikwvAVCLwu8HBe8kp0Uw55Q2PxMsytyFlKXDwb8NWQkcoJjcyEaZxZrv76sytbUZ9GukJZEnU', ...}\nlogin_trace = None\n\n    @allure.title(\"【108220】 订单列表已发货 tab订单流程验证\")\n    def test_108220_dWeb_my_shipped_order_ui_ux(self, page: dict, pc_autotest_header, login_trace):\n        \"\"\"\n        【108220】 订单列表已发货 tab订单流程验证\n        测试步骤：\n        1、进入订单列表页面，点击切换到已发货tab下\n        2、如果存在订单，订单上会存在各种按钮\n        3、点击 Return/ Refund 按钮，进入售后退款页面，再返回已送达页面\n        4、点击Refund details 按钮，进入售后退款详情页面，再返回已送达页面\n        5、点击Track items 按钮，弹出物流pop，点击conform 按钮，回到已送达页面\n        6、点击reorder 按钮 ，右侧拉出再来一单选择商品页面，关闭返回已送达页面\n        3、4、5、 依赖测试数据\n        \"\"\"\n        p: Page = page.get(\"page\")\n        c = page.get(\"context\")\n    \n        # 1. 进入订单列表页面\n        order_page = DWebOrderListPage(p, pc_autotest_header, browser_context=c, page_url=\"/account/my_orders\")\n        p.wait_for_timeout(3000)\n        log.info(\"成功进入订单列表页面\")\n        # 切换到已发货tab\n        shipped_tab = p.get_by_test_id(dweb_order_list_ele.order_shipped_tab_ele)\n>       assert shipped_tab.is_visible(), \"未找到已发货tab\"\nE       AssertionError: 未找到已发货tab\nE       assert False\nE        +  where False = <bound method Locator.is_visible of <Locator frame=<Frame name= url='https://www.sayweee.com/en/account/my_orders'> selector='internal:testid=[data-testid=\"wid-order-list-tab-3\"s]'>>()\nE        +    where <bound method Locator.is_visible of <Locator frame=<Frame name= url='https://www.sayweee.com/en/account/my_orders'> selector='internal:testid=[data-testid=\"wid-order-list-tab-3\"s]'>> = <Locator frame=<Frame name= url='https://www.sayweee.com/en/account/my_orders'> selector='internal:testid=[data-testid=\"wid-order-list-tab-3\"s]'>.is_visible\n\nsrc\\Dweb\\EC\\testcases\\dweb_account\\dweb_order\\test_108220_dWeb_my_shipped_order_ui_ux.py:37: AssertionError"}, "description": "\n        【108220】 订单列表已发货 tab订单流程验证\n        测试步骤：\n        1、进入订单列表页面，点击切换到已发货tab下\n        2、如果存在订单，订单上会存在各种按钮\n        3、点击 Return/ Refund 按钮，进入售后退款页面，再返回已送达页面\n        4、点击Refund details 按钮，进入售后退款详情页面，再返回已送达页面\n        5、点击Track items 按钮，弹出物流pop，点击conform 按钮，回到已送达页面\n        6、点击reorder 按钮 ，右侧拉出再来一单选择商品页面，关闭返回已送达页面\n        3、4、5、 依赖测试数据\n        ", "start": *************, "stop": *************, "uuid": "5c131e18-14de-40c7-86c0-b57d3c25be91", "historyId": "ecca26e2f200918b1ccfe550cd430b36", "testCaseId": "ecca26e2f200918b1ccfe550cd430b36", "fullName": "src.Dweb.EC.testcases.dweb_account.dweb_order.test_108220_dWeb_my_shipped_order_ui_ux.TestDWebMyShippedOrderUIUX#test_108220_dWeb_my_shipped_order_ui_ux", "labels": [{"name": "story", "value": "【108220】 订单列表已发货 tab订单流程验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "dweb_porder"}, {"name": "tag", "value": "dweb_regression"}, {"name": "tag", "value": "transaction"}, {"name": "tag", "value": "suqin"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.dweb_account.dweb_order"}, {"name": "suite", "value": "test_108220_dWeb_my_shipped_order_ui_ux"}, {"name": "subSuite", "value": "TestDWebMyShippedOrderUIUX"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "18600-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.dweb_account.dweb_order.test_108220_dWeb_my_shipped_order_ui_ux"}]}
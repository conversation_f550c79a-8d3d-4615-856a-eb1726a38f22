{"name": "【109549】 PC购物车-单个生鲜购物车样式", "status": "passed", "description": "\n        【109549】 PC购物车-单个生鲜购物车样式\n        该测试用例的测试点有：\n        1. 进入分类页面，加购Local类型的商品进购物车\n        2. 进入购物车，校验购物车的样式\n        3. 校验购物车中商品的样式\n        ", "start": 1750919735852, "stop": 1750919739119, "uuid": "a99bd8ae-2b0b-4bce-9bbf-936e9228d173", "testCaseId": "3041a3b5149b5d2fe5d8247e89f34a4b", "fullName": "src.Dweb.EC.testcases.dweb_cart.test_109549_dWeb_single_grocery_cart_ui_ux.TestDWebSingleGroceryCartUIUX#test_109549_dWeb_single_grocery_cart_ui_ux"}
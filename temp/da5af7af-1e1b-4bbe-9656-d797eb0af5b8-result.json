{"name": "PC端-订单页面状态标签切换UI/UX验证", "status": "failed", "statusDetails": {"message": "AssertionError: Cancelled 标签未被选中", "trace": "self = <test_111250_dWeb_orders_ui_ux.TestDWebOrdersUIUX object at 0x000001FDD71644D0>\npage = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppDat...> version=120.0.6099.28>>, 'page': <Page url='https://www.sayweee.com/en/account/my_orders?page_no=1&filter_status=4'>}\npc_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...sQTAcnJR8A7JAOh1ZSD-Pa4ZLso29CFKKRkuhCy5rGmgdHxwcT2I5ZgjWzyOj35XTNm6dqAXzkKCpOvX2w-xkB_i5g-cBs2cJEMKdLXqajR1j72s', ...}\nlogin_trace = None\n\n    @allure.title(\"PC端-订单页面状态标签切换UI/UX验证\")\n    def test_111250_dWeb_orders_status_tabs_ui_ux(self, page: dict, pc_autotest_header, login_trace):\n        \"\"\"\n        case_id: 111250 查看我的订单\n        PC端-订单页面状态标签切换UI/UX验证\n        此用例的校验点有：\n        1. 进入account页面\n        2. 点击my orders，进入order页面\n        3. 依次点击\"pending, unshipped, shipped, to review, cancelled\"标签，查看订单\n        4. 验证每个标签页面正确加载并显示相应状态的订单\n        \"\"\"\n        p: Page = page.get(\"page\")\n        c = page.get(\"context\")\n    \n        # 创建订单页面对象\n        orders_page = DWebOrdersPage(p, pc_autotest_header, browser_context=c)\n    \n        # 1. 进入订单页面\n        log.info(\"步骤1: 进入订单页面\")\n        orders_page.navigate_to_orders_page()\n        assert p.url.endswith(\"/account/my_orders\"), \"未成功进入订单页面\"\n    \n        # 2. 依次点击订单状态标签并验证\n        log.info(\"步骤2: 依次点击订单状态标签并验证\")\n    \n        # 定义要测试的标签\n        status_tabs = [\"All\", \"Pending\", \"Unshipped\", \"Shipped\", \"Review\", \"Cancelled\"]\n    \n        # 遍历每个标签并点击\n        for tab_name in status_tabs:\n            log.info(f\"点击 {tab_name} 标签\")\n    \n            # 切换到指定标签\n>           orders_page.switch_to_tab(tab_name)\n\nsrc\\Dweb\\EC\\testcases\\dweb_order\\test_111250_dWeb_orders_ui_ux.py:46: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n\nself = <src.Dweb.EC.dweb_pages.dweb_page_orders.dweb_page_orders.DWebOrdersPage object at 0x000001FDD79A4650>\ntab_name = 'Cancelled'\n\n    def switch_to_tab(self, tab_name):\n        \"\"\"\n        切换到指定的订单状态标签\n    \n        参数:\n            tab_name: 标签名称，可选值为 \"All\", \"Pending\", \"Unshipped\", \"Shipped\", \"To Review\", \"Cancelled\"\n        \"\"\"\n        # 定义标签名称与测试ID的映射\n        # 目前开发未就此页面添加id\n        tab_test_ids = {\n            \"All\": \"//li[text()='All']\",\n            \"Pending\": \"//li[text()='Pending']\",\n            \"Unshipped\": \"//li[text()='Unshipped']\",\n            \"Shipped\": \"//li[text()='Shipped']\",\n            \"Review\": \"//li[text()='Review']\",\n            \"Cancelled\": \"//li[text()='Cancelled']\"\n        }\n    \n        # 获取对应的测试ID\n        test_id = tab_test_ids.get(tab_name)\n        if not test_id:\n            raise ValueError(f\"无效的标签名称: {tab_name}\")\n    \n        # 点击标签\n        self.page.locator(test_id).click()\n        self.page.wait_for_timeout(2000)\n    \n        if tab_name == \"Cancelled\":\n>           assert (self.page.locator(\"div[class*='OrderCard_orderCard']\").all() and\n                    self.page.locator(\"//div[text()='Canceled']\").all() and\n                    self.page.locator(\"//a[text()='Details']\").all()), f\"{tab_name} 标签未被选中\"\nE           AssertionError: Cancelled 标签未被选中\n\nsrc\\Dweb\\EC\\dweb_pages\\dweb_page_orders\\dweb_page_orders.py:60: AssertionError"}, "description": "\n        case_id: 111250 查看我的订单\n        PC端-订单页面状态标签切换UI/UX验证\n        此用例的校验点有：\n        1. 进入account页面\n        2. 点击my orders，进入order页面\n        3. 依次点击\"pending, unshipped, shipped, to review, cancelled\"标签，查看订单\n        4. 验证每个标签页面正确加载并显示相应状态的订单\n        ", "start": *************, "stop": *************, "uuid": "b516d2ca-ee49-4dcb-ad8f-8d32e374d92f", "historyId": "9f85c03db51a9190887cad32e395acd3", "testCaseId": "9f85c03db51a9190887cad32e395acd3", "fullName": "src.Dweb.EC.testcases.dweb_order.test_111250_dWeb_orders_ui_ux.TestDWebOrdersUIUX#test_111250_dWeb_orders_status_tabs_ui_ux", "labels": [{"name": "story", "value": "PC端-订单页面UI/UX验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "pcorders"}, {"name": "tag", "value": "dweb_regression"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.dweb_order"}, {"name": "suite", "value": "test_111250_dWeb_orders_ui_ux"}, {"name": "subSuite", "value": "TestDWebOrdersUIUX"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "15096-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.dweb_order.test_111250_dWeb_orders_ui_ux"}]}
{"name": "H5购物车-空购物车UI/UX验证", "status": "passed", "description": "\n        【109506】 H5购物车-空购物车UI/UX验证\n        ", "start": 1750919780097, "stop": 1750919797047, "uuid": "c95bedec-3ce5-4f80-83b0-51caea5b9123", "historyId": "1dcab1cf173a3b6662d27a634c98c137", "testCaseId": "1dcab1cf173a3b6662d27a634c98c137", "fullName": "src.Mweb.EC.testcases.mweb_cart.test_109506_mWeb_empty_cart_ui_ux.TestMwebEmptyCartUIUX#test_109506_MWeb_empty_cart_ui_ux", "labels": [{"name": "story", "value": "H5购物车-空购物车UI/UX验证"}, {"name": "tag", "value": "present"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "mweb_regression"}, {"name": "tag", "value": "<PERSON><PERSON><PERSON>"}, {"name": "parentSuite", "value": "src.Mweb.EC.testcases.mweb_cart"}, {"name": "suite", "value": "test_109506_mWeb_empty_cart_ui_ux"}, {"name": "subSuite", "value": "TestMwebEmptyCartUIUX"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "23528-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Mweb.EC.testcases.mweb_cart.test_109506_mWeb_empty_cart_ui_ux"}]}
{"name": "PC-订单成功页样式验证", "status": "broken", "statusDetails": {"message": "playwright._impl._errors.TimeoutError: Timeout 50000ms exceeded.", "trace": "self = <src.Dweb.EC.testcases.dweb_order_confimation.test_112730_dweb_order_confirmation_ui_ux.TestWebOrderConfirmationUIUX object at 0x000001FDD7166DD0>\npage = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppDat...ome-win\\chrome.exe> version=120.0.6099.28>>, 'page': <Page url='https://www.sayweee.com/en/order/success/v2/69581331'>}\npc_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...sQTAcnJR8A7JAOh1ZSD-Pa4ZLso29CFKKRkuhCy5rGmgdHxwcT2I5ZgjWzyOj35XTNm6dqAXzkKCpOvX2w-xkB_i5g-cBs2cJEMKdLXqajR1j72s', ...}\nlogin_trace = None\n\n    @allure.title(\"PC-订单成功页样式验证\")\n    @pytest.mark.present\n    def test_112730_dweb_order_confirmation_ui_ux(self, page: dict, pc_autotest_header, login_trace):\n        \"\"\"\n        【112730】 PC-订单成功页样式验证\n    \n        测试步骤:\n        1. 打开订单成功页\n        2. 验证弹窗存在并且标题正常展示\n        3. 点击开始赚取积分按钮打开弹窗\n        4. 点击关闭按钮关闭弹窗\n        5. 验证订单摘要信息\n        6. 验证操作按钮\n        \"\"\"\n        p: Page = page.get(\"page\")\n        c = page.get(\"context\")\n    \n        # 1. 创建订单成功页面对象\n>       order_confirmation_page = DWebOrderComfirmationPage(p, pc_autotest_header, browser_context=c, page_url=\"/order/success/v2/69581331\")\n\nsrc\\Dweb\\EC\\testcases\\dweb_order_confimation\\test_112730_dweb_order_confirmation_ui_ux.py:43: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\nsrc\\Dweb\\EC\\dweb_pages\\dweb_page_order_confirmation\\dweb_page_order_confirmation.py:44: in __init__\n    page_zipcode = self.page.locator(dweb_home_ele.ele_zipcode).text_content()\nvenv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py:18050: in text_content\n    self._sync(self._impl_obj.text_content(timeout=timeout))\nvenv\\Lib\\site-packages\\playwright\\_impl\\_locator.py:607: in text_content\n    return await self._frame.text_content(\nvenv\\Lib\\site-packages\\playwright\\_impl\\_frame.py:609: in text_content\n    return await self._channel.send(\"textContent\", locals_to_params(locals()))\nvenv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:62: in send\n    return await self._connection.wrap_api_call(\nvenv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:492: in wrap_api_call\n    return await cb()\n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n\nself = <playwright._impl._connection.Channel object at 0x000001FDD792DE90>\nmethod = 'textContent'\nparams = {'selector': \"//div[@id='changeZipCode']/div[position()=1]\", 'strict': True}\nreturn_as_dict = False\n\n    async def inner_send(\n        self, method: str, params: Optional[Dict], return_as_dict: bool\n    ) -> Any:\n        if params is None:\n            params = {}\n        callback = self._connection._send_message_to_server(\n            self._object, method, params\n        )\n        if self._connection._error:\n            error = self._connection._error\n            self._connection._error = None\n            raise error\n        done, _ = await asyncio.wait(\n            {\n                self._connection._transport.on_error_future,\n                callback.future,\n            },\n            return_when=asyncio.FIRST_COMPLETED,\n        )\n        if not callback.future.done():\n            callback.future.cancel()\n>       result = next(iter(done)).result()\nE       playwright._impl._errors.TimeoutError: Timeout 50000ms exceeded.\n\nvenv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:100: TimeoutError"}, "description": "\n        【112730】 PC-订单成功页样式验证\n        \n        测试步骤:\n        1. 打开订单成功页\n        2. 验证弹窗存在并且标题正常展示\n        3. 点击开始赚取积分按钮打开弹窗\n        4. 点击关闭按钮关闭弹窗\n        5. 验证订单摘要信息\n        6. 验证操作按钮\n        ", "start": 1750651286703, "stop": 1750651338574, "uuid": "9434ae8b-225b-4995-bd9f-55dbcb7ad6fa", "historyId": "14acb919d8aea31aaed162d76e04a062", "testCaseId": "14acb919d8aea31aaed162d76e04a062", "fullName": "src.Dweb.EC.testcases.dweb_order_confimation.test_112730_dweb_order_confirmation_ui_ux.TestWebOrderConfirmationUIUX#test_112730_dweb_order_confirmation_ui_ux", "labels": [{"name": "story", "value": "PC-订单成功页样式验证"}, {"name": "tag", "value": "present"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "dweb_regression"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.dweb_order_confimation"}, {"name": "suite", "value": "test_112730_dweb_order_confirmation_ui_ux"}, {"name": "subSuite", "value": "TestWebOrderConfirmationUIUX"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "15096-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.dweb_order_confimation.test_112730_dweb_order_confirmation_ui_ux"}]}
{"name": "【102677】 PC-Account 页面-我的订单-UI/UX验证", "status": "failed", "statusDetails": {"message": "AssertionError: 未找到全部订单tab\nassert False\n +  where False = <bound method Locator.is_visible of <Locator frame=<Frame name= url='https://www.sayweee.com/en/account/my_orders'> selector='internal:testid=[data-testid=\"wid-order-list-tab-all\"s]'>>()\n +    where <bound method Locator.is_visible of <Locator frame=<Frame name= url='https://www.sayweee.com/en/account/my_orders'> selector='internal:testid=[data-testid=\"wid-order-list-tab-all\"s]'>> = <Locator frame=<Frame name= url='https://www.sayweee.com/en/account/my_orders'> selector='internal:testid=[data-testid=\"wid-order-list-tab-all\"s]'>.is_visible", "trace": "self = <src.Dweb.EC.testcases.dweb_account.dweb_order.test_102677_dWeb_account_my_order_ui_ux.TestDWebAccountAllOrderUIUX object at 0x000001CEE624FC50>\npage = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppDat...1091\\chrome-win\\chrome.exe> version=120.0.6099.28>>, 'page': <Page url='https://www.sayweee.com/en/account/my_orders'>}\npc_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...RQsAFh_-vqtVaibJAHZ6fUZU2ihbgNHuZ1HAQHfikwvAVCLwu8HBe8kp0Uw55Q2PxMsytyFlKXDwb8NWQkcoJjcyEaZxZrv76sytbUZ9GukJZEnU', ...}\nlogin_trace = None\n\n    @allure.title(\"【102677】 PC-Account 页面-我的订单-UI/UX验证\")\n    def test_102677_dWeb_account_my_order_ui_ux(self, page: dict, pc_autotest_header, login_trace):\n        \"\"\"\n        【102677】 PC-Account 页面-我的订单-UI/UX验证\n        测试步骤：\n       1、依次显示：今日订单（如有）、全部订单、待付款、待发货、已发货、待晒单、已取消按钮\n        2、点击我的订单，进入我的订单页面\n        3、并默认选中今日订单（如有），否则默认“全部订单”tab\n        4、切换各订单类型的tab\n        5、切换成功，数据跟随订单类型过滤正确 --这一步在每个订单case 里已包含\n        6、切换右上角 订单时间范围\n        \"\"\"\n        p: Page = page.get(\"page\")\n        c = page.get(\"context\")\n    \n        # 1. 进入订单列表页面\n        order_page = DWebOrderListPage(p, pc_autotest_header, browser_context=c, page_url=\"/account/my_orders\")\n        p.wait_for_timeout(3000)\n        log.info(\"成功进入订单列表页面\")\n    \n        # all全部订单 tab\n        all_tab = p.get_by_test_id(dweb_order_list_ele.order_all_tab_ele)\n>       assert all_tab.is_visible(), \"未找到全部订单tab\"\nE       AssertionError: 未找到全部订单tab\nE       assert False\nE        +  where False = <bound method Locator.is_visible of <Locator frame=<Frame name= url='https://www.sayweee.com/en/account/my_orders'> selector='internal:testid=[data-testid=\"wid-order-list-tab-all\"s]'>>()\nE        +    where <bound method Locator.is_visible of <Locator frame=<Frame name= url='https://www.sayweee.com/en/account/my_orders'> selector='internal:testid=[data-testid=\"wid-order-list-tab-all\"s]'>> = <Locator frame=<Frame name= url='https://www.sayweee.com/en/account/my_orders'> selector='internal:testid=[data-testid=\"wid-order-list-tab-all\"s]'>.is_visible\n\nsrc\\Dweb\\EC\\testcases\\dweb_account\\dweb_order\\test_102677_dWeb_account_my_order_ui_ux.py:36: AssertionError"}, "description": "\n        【102677】 PC-Account 页面-我的订单-UI/UX验证\n        测试步骤：\n       1、依次显示：今日订单（如有）、全部订单、待付款、待发货、已发货、待晒单、已取消按钮\n        2、点击我的订单，进入我的订单页面\n        3、并默认选中今日订单（如有），否则默认“全部订单”tab\n        4、切换各订单类型的tab\n        5、切换成功，数据跟随订单类型过滤正确 --这一步在每个订单case 里已包含\n        6、切换右上角 订单时间范围\n        ", "start": *************, "stop": *************, "uuid": "9dadca94-7a6a-4500-b14b-df44796b9744", "historyId": "c68b473d986cff28f10f9290cb7edd42", "testCaseId": "c68b473d986cff28f10f9290cb7edd42", "fullName": "src.Dweb.EC.testcases.dweb_account.dweb_order.test_102677_dWeb_account_my_order_ui_ux.TestDWebAccountAllOrderUIUX#test_102677_dWeb_account_my_order_ui_ux", "labels": [{"name": "story", "value": "【102677】 PC-Account 页面-我的订单-UI/UX验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "dweb_porder"}, {"name": "tag", "value": "dweb_regression"}, {"name": "tag", "value": "transaction"}, {"name": "tag", "value": "suqin"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.dweb_account.dweb_order"}, {"name": "suite", "value": "test_102677_dWeb_account_my_order_ui_ux"}, {"name": "subSuite", "value": "TestDWebAccountAllOrderUIUX"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "18600-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.dweb_account.dweb_order.test_102677_dWeb_account_my_order_ui_ux"}]}
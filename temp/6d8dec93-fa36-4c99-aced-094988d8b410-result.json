{"name": "【109549】 PC购物车-单个生鲜购物车样式", "status": "broken", "statusDetails": {"message": "TypeError: object of type 'ElementHandle' has no len()", "trace": "self = <test_109549_dWeb_single_grocery_cart_ui_ux.TestDWebSingleGroceryCartUIUX object at 0x00000200915F8D10>\npage = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppDat...ght\\chromium-1091\\chrome-win\\chrome.exe> version=120.0.6099.28>>, 'page': <Page url='https://www.sayweee.com/en/cart'>}\npc_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...Ch_XDtTRiTT0F-HdoCYtZmiNQ5eW_hZIcwZn0K6BNkpcTwgzcOrHrTo2SA8a_v0CemgMlw357PQdr6DZ9RXZOfv1H5jaUHbsOcYrpnAWcz2mtU60', ...}\nlogin_trace = None\n\n    @allure.title(\"【109549】 PC购物车-单个生鲜购物车样式\")\n    def test_109549_dWeb_single_grocery_cart_ui_ux(self, page: dict, pc_autotest_header, login_trace):\n        \"\"\"\n        【109549】 PC购物车-单个生鲜购物车样式\n        该测试用例的测试点有：\n        1. 进入分类页面，加购Local类型的商品进购物车\n        2. 进入购物车，校验购物车的样式\n        3. 校验购物车中商品的样式\n        \"\"\"\n        p: Page = page.get(\"page\")\n        c = page.get(\"context\")\n        # 构造的购物车页面\n        cart_page = DWebCartPage(p, pc_autotest_header, browser_context=c, page_url=\"/cart\")\n        # 清空购物车\n        try:\n            empty_cart(pc_autotest_header)\n            # 清空购物车之后刷新页面\n            p.reload()\n            p.wait_for_timeout(2000)\n        except Exception as e:\n            log.info(\"清空购物车发生异常\" + str(e))\n    \n        # 构造的分类页面\n        category_page = DWebCategorypage(p, pc_autotest_header, browser_context=c)\n        # 去分类页加购Local类型的商品进购物车\n        category_page.add_to_local_product_cart_from_category()\n        # 去购物车结算\n        category_page.go_to_cart_from_category()\n        # 光标移到global+ 位置\n        p.get_by_test_id(\"wid-direct-from-japan\").hover()\n    \n        # 购物车断言\n        p.wait_for_selector(dweb_cart_ele.ele_cart_summary)\n        assert cart_page.FE.ele(dweb_cart_ele.ele_cart_summary).is_visible()\n        assert \"title\" in cart_page.FE.ele(dweb_cart_ele.ele_cart_summary).get_attribute(\"class\")\n        # 判断只有一个购物车\n>       assert len(cart_page.FE.ele(dweb_cart_ele.ele_cart_summary_list)) == 1 and cart_page.FE.ele(\n            dweb_cart_ele.ele_cart_summary_list)\nE       TypeError: object of type 'ElementHandle' has no len()\n\nsrc\\Dweb\\EC\\testcases\\dweb_cart\\test_109549_dWeb_single_grocery_cart_ui_ux.py:56: TypeError"}, "description": "\n        【109549】 PC购物车-单个生鲜购物车样式\n        该测试用例的测试点有：\n        1. 进入分类页面，加购Local类型的商品进购物车\n        2. 进入购物车，校验购物车的样式\n        3. 校验购物车中商品的样式\n        ", "start": 1745315504677, "stop": 1745315592095, "uuid": "49d047b3-e1f5-491e-801a-6c01e9d7ed5a", "historyId": "3041a3b5149b5d2fe5d8247e89f34a4b", "testCaseId": "3041a3b5149b5d2fe5d8247e89f34a4b", "fullName": "src.Dweb.EC.testcases.dweb_cart.test_109549_dWeb_single_grocery_cart_ui_ux.TestDWebSingleGroceryCartUIUX#test_109549_dWeb_single_grocery_cart_ui_ux", "labels": [{"name": "story", "value": "【109549】 PC购物车-单个生鲜购物车样式"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "pccart"}, {"name": "tag", "value": "dweb_regression"}, {"name": "tag", "value": "transaction"}, {"name": "tag", "value": "suqin"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.dweb_cart"}, {"name": "suite", "value": "test_109549_dWeb_single_grocery_cart_ui_ux"}, {"name": "subSuite", "value": "TestDWebSingleGroceryCartUIUX"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "23072-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.dweb_cart.test_109549_dWeb_single_grocery_cart_ui_ux"}]}
{"name": "【112223】 购物车-中间-验证中间页的交互", "status": "failed", "statusDetails": {"message": "AssertionError: 关闭按钮点击后中间页仍然可见\nassert not True\n +  where True = <bound method Locator.is_visible of <Locator frame=<Frame name= url='https://www.sayweee.com/en/cart'> selector=\"//h2[text()='Select carts for checkout']\">>(timeout=3000)\n +    where <bound method Locator.is_visible of <Locator frame=<Frame name= url='https://www.sayweee.com/en/cart'> selector=\"//h2[text()='Select carts for checkout']\">> = <Locator frame=<Frame name= url='https://www.sayweee.com/en/cart'> selector=\"//h2[text()='Select carts for checkout']\">.is_visible\n +      where <Locator frame=<Frame name= url='https://www.sayweee.com/en/cart'> selector=\"//h2[text()='Select carts for checkout']\"> = <bound method Page.locator of <Page url='https://www.sayweee.com/en/cart'>>(\"//h2[text()='Select carts for checkout']\")\n +        where <bound method Page.locator of <Page url='https://www.sayweee.com/en/cart'>> = <Page url='https://www.sayweee.com/en/cart'>.locator\n +        and   \"//h2[text()='Select carts for checkout']\" = mweb_cart_ele.ele_cart_middle_title", "trace": "self = <src.Mweb.EC.testcases.mweb_cart.test_112223_mweb_cart_intermediate_ui_ux.TestMwebCartIntermediatePage object at 0x00000143FC109A10>\nphone_page = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppDat...ght\\chromium-1091\\chrome-win\\chrome.exe> version=120.0.6099.28>>, 'page': <Page url='https://www.sayweee.com/en/cart'>}\nh5_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...uZBFvf9xCE7fdAfz0j0_peXZ2AmEY5oTjm3kGArdKc_-Yr8hE1AQywfTOCOeXcqcQvjW03ZcQ5ApcUVdOJgLZUVza2jic-6FwlXX08RYiCKroMT4', ...}\nh5_open_and_close_trace = None\n\n    @allure.title(\"【112223】 购物车-中间-验证中间页的交互\")\n    @pytest.mark.present\n    def test_112223_mweb_cart_intermediate_ui_ux(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):\n        \"\"\"\n        【112223】 购物车-中间-验证中间页的交互\n        测试步骤：\n        1. 清空购物车\n        2. 从首页进入Deals分类，添加多种类型的商品到购物车（至少3种不同类型）\n           - 通过分类筛选加购本地配送商品\n           - 通过分类筛选加购Global+商品\n           - 通过分类筛选加购Pantry商品\n        3. 点击结算，验证中间页面交互\n        4. 清空购物车，添加单一类型商品，验证直接跳转结算页\n        5. 再次添加多种类型商品，验证勾选一个购物车后的结算流程\n        \"\"\"\n        p: Page = phone_page.get(\"page\")\n        c = phone_page.get(\"context\")\n    \n        # 0. 切换zipcode到98011\n        with allure.step(\"切换zipcode到98011\"):\n            try:\n                # 如果zipcode不是98011， 则切换到98011\n                if not p.locator(\"//span[text()='98011']\").all():\n                    switch_zipcode(h5_autotest_header, \"98011\")\n                    p.reload()\n                    p.wait_for_load_state(\"networkidle\", timeout=30000)\n                    log.info(\"成功切换zipcode到98011\")\n            except Exception as e:\n                log.warning(f\"切换zipcode失败，继续测试: {str(e)}\")\n    \n        # 1. 清空购物车\n        with allure.step(\"清空购物车\"):\n            try:\n                empty_cart(h5_autotest_header)\n                log.info(\"购物车清空成功\")\n            except Exception as e:\n                log.error(f\"清空购物车失败: {str(e)}\")\n                # 继续测试，不中断\n    \n        # 从首页进入Deals分类\n        with allure.step(\"从首页进入Deals分类\"):\n            # 创建首页对象\n            home_page = MWebPageHome(p, h5_autotest_header, bc=c)\n            home_page.go_to_special_category_from_home(mweb_home_ele.ele_deals)\n            p.wait_for_load_state(\"networkidle\", timeout=30000)\n            p.wait_for_timeout(2000)\n    \n            # 创建分类页面对象\n            category_page = MWebCategorypage(p, h5_autotest_header, browser_context=c, page_url=\"/category/sale\")\n    \n            log.info(\"成功从首页进入Deals分类\")\n    \n        # 3. 添加多种类型的商品\n        with allure.step(\"添加多种类型的商品\"):\n            # 配置多个筛选条件\n            filters_config = [\n                {\n                    \"filter_type\": \"Local Delivery\",\n                    \"test_id\": mweb_category_ele.ele_local_delivery_xpath,\n                    \"items_to_add\": 1\n                },\n                {\n                    \"filter_type\": \"Pantry\",\n                    \"test_id\": mweb_category_ele.ele_pantry_delivery_xpath,\n                    \"items_to_add\": 1\n                }\n            ]\n    \n            # 应用多个筛选条件并加购商品\n            results = apply_multiple_filters_and_add_products(p, filters_config)\n    \n            # 验证结果\n            local_added = results.get(\"Local Delivery\", 0)\n            pantry_added = results.get(\"Pantry\", 0)\n    \n            assert local_added > 0, \"未能添加Local Delivery商品\"\n            assert pantry_added > 0, \"未能添加Pantry商品\"\n    \n            total_added = local_added + pantry_added\n            log.info(f\"成功添加{local_added}个Local Delivery商品和{pantry_added}个Pantry商品，总共{total_added}个商品\")\n    \n        # 5. 进入购物车页面，验证商品已添加成功\n        with allure.step(\"进入购物车页面，验证商品已添加成功\"):\n            # 进入购物车页面\n            p.goto(f\"{TEST_URL}/cart\")\n            p.wait_for_load_state(\"networkidle\", timeout=30000)\n            p.wait_for_timeout(3000)\n    \n            # 验证购物车中的商品\n            assert verify_cart_items(p), \"购物车中没有找到商品，加购可能失败\"\n    \n            # 获取购物车商品\n            cart_items = p.query_selector_all(mweb_cart_ele.ele_cart_normal_card)\n            log.info(f\"购物车中有{len(cart_items)}个商品\")\n    \n            # 验证购物车中商品数量与加购数量一致\n            # assert len(cart_items) >= total_added, f\"购物车中商品数量({len(cart_items)})少于加购数量({total_added})\"\n    \n            # 验证购物车中有不同类型的商品\n            normal_cart = p.locator(mweb_cart_ele.ele_cart_normal).is_visible(timeout=3000)\n            pantry_cart = p.locator(mweb_cart_ele.ele_cart_pantry).is_visible(timeout=3000)\n    \n            if local_added > 0 and pantry_added > 0:\n                assert normal_cart and pantry_cart, \"购物车中未显示所有类型的商品\"\n            elif local_added > 0:\n                assert normal_cart, \"购物车中未显示Local Delivery商品\"\n            elif pantry_added > 0:\n                assert pantry_cart, \"购物车中未显示Pantry商品\"\n    \n            log.info(\"成功验证购物车中的商品\")\n    \n        # 3. 点击结算，验证中间页面交互\n        with (allure.step(\"点击结算按钮，验证中间页面元素\")):\n            # 滚动到页面底部找到结算按钮\n            p.evaluate('window.scrollTo(0, document.body.scrollHeight)')\n            p.wait_for_timeout(2000)\n    \n            # 点击结算按钮\n            checkout_btn =p.get_by_test_id(\"btn-checkout\")\n            p.locator(mweb_cart_ele.ele_cart_checkout_button)\n            assert checkout_btn.is_visible(timeout=2000), \"结算按钮不可见\"\n    \n            log.info(\"找到结算按钮，准备点击\")\n            checkout_btn.click()\n            p.wait_for_timeout(2000)\n    \n            # 验证中间页面元素\n            # 1. 验证标题\n            title = p.locator(mweb_cart_ele.ele_cart_middle_title)\n            assert title.is_visible(timeout=2000), \"中间页标题不可见\"\n            assert title.text_content() == \"Select carts for checkout\", f\"中间页标题文本不正确: {title.text_content()}\"\n            log.info(\"验证中间页标题成功\")\n    \n            # 2. 验证全选按钮\n            select_all = p.locator(mweb_cart_ele.ele_cart_select_all)\n            assert select_all.is_visible(timeout=5000), \"全选按钮不可见\"\n    \n            select_all_text = p.locator(mweb_cart_ele.ele_cart_select_all_text)\n            assert select_all_text.is_visible(), \"全选文本不可见\"\n            assert \"Select all carts\" in select_all_text.text_content(), f\"全选文本不正确: {select_all_text.text_content()}\"\n            log.info(\"验证全选按钮成功\")\n    \n            # 3. 验证购物车类型\n            normal_cart = p.locator(mweb_cart_ele.ele_cart_normal_id)\n            pantry_cart = p.locator(mweb_cart_ele.ele_cart_pantry_id)\n    \n            assert normal_cart.is_visible(timeout=3000), \"Local Delivery购物车不可见\"\n            assert pantry_cart.is_visible(timeout=3000), \"Pantry购物车不可见\"\n            log.info(\"验证购物车类型成功\")\n    \n            # 4. 验证购物车选择框\n            normal_select = p.locator(mweb_cart_ele.ele_cart_select_normal)\n            pantry_select = p.locator(mweb_cart_ele.ele_cart_select_pantry)\n    \n            assert normal_select.is_visible(timeout=3000), \"Local Delivery购物车选择框不可见\"\n            assert pantry_select.is_visible(timeout=3000), \"Pantry购物车选择框不可见\"\n            log.info(\"验证购物车选择框成功\")\n    \n            # 5. 验证底部提示文本\n            tip = p.locator(mweb_cart_ele.ele_cart_middle_tip)\n            assert tip.is_visible(timeout=3000), \"底部提示文本不可见\"\n            assert \"You can select multiple carts for checkout\" in tip.text_content(), f\"底部提示文本不正确: {tip.text_content()}\"\n            log.info(\"验证底部提示文本成功\")\n    \n            # 6. 验证结算按钮-- 有2个一模一样的test_id后续优化\n            checkout_btn = p.locator(mweb_cart_ele.ele_cart_middle_checkout)\n            assert checkout_btn.is_visible(timeout=3000), \"结算按钮不可见\"\n    \n            # 验证结算按钮是否禁用\n            btn_class = checkout_btn.get_attribute(\"class\") or \"\"\n            assert \"disabled\" in btn_class or \"btn-disabled\" in btn_class, f\"结算按钮未被禁用: {btn_class}\"\n            log.info(\"验证结算按钮成功\")\n    \n            # 7. 验证小计金额--小计购物车跟中间页有重复的test_id后续优化\n            subtotal = p.locator(mweb_cart_ele.ele_cart_middle_subtotal)\n            assert subtotal.is_visible(timeout=3000), \"小计金额不可见\"\n            assert \"$0.00\" in subtotal.text_content(), f\"小计金额不正确: {subtotal.text_content()}\"\n            log.info(\"验证小计金额成功\")\n    \n            # 8. 验证关闭按钮\n            close_btn = p.locator(mweb_cart_ele.ele_cart_middle_close)\n            assert close_btn.is_visible(timeout=3000), \"关闭按钮不可见\"\n            log.info(\"验证关闭按钮成功\")\n    \n            # 点击关闭按钮\n            close_btn.click()\n            p.wait_for_timeout(2000)\n    \n            # 验证中间页已关闭\n>           assert not p.locator(mweb_cart_ele.ele_cart_middle_title).is_visible(timeout=3000), \"关闭按钮点击后中间页仍然可见\"\nE           AssertionError: 关闭按钮点击后中间页仍然可见\nE           assert not True\nE            +  where True = <bound method Locator.is_visible of <Locator frame=<Frame name= url='https://www.sayweee.com/en/cart'> selector=\"//h2[text()='Select carts for checkout']\">>(timeout=3000)\nE            +    where <bound method Locator.is_visible of <Locator frame=<Frame name= url='https://www.sayweee.com/en/cart'> selector=\"//h2[text()='Select carts for checkout']\">> = <Locator frame=<Frame name= url='https://www.sayweee.com/en/cart'> selector=\"//h2[text()='Select carts for checkout']\">.is_visible\nE            +      where <Locator frame=<Frame name= url='https://www.sayweee.com/en/cart'> selector=\"//h2[text()='Select carts for checkout']\"> = <bound method Page.locator of <Page url='https://www.sayweee.com/en/cart'>>(\"//h2[text()='Select carts for checkout']\")\nE            +        where <bound method Page.locator of <Page url='https://www.sayweee.com/en/cart'>> = <Page url='https://www.sayweee.com/en/cart'>.locator\nE            +        and   \"//h2[text()='Select carts for checkout']\" = mweb_cart_ele.ele_cart_middle_title\n\nsrc\\Mweb\\EC\\testcases\\mweb_cart\\test_112223_mweb_cart_intermediate_ui_ux.py:221: AssertionError"}, "description": "\n        【112223】 购物车-中间-验证中间页的交互\n        测试步骤：\n        1. 清空购物车\n        2. 从首页进入Deals分类，添加多种类型的商品到购物车（至少3种不同类型）\n           - 通过分类筛选加购本地配送商品\n           - 通过分类筛选加购Global+商品\n           - 通过分类筛选加购Pantry商品\n        3. 点击结算，验证中间页面交互\n        4. 清空购物车，添加单一类型商品，验证直接跳转结算页\n        5. 再次添加多种类型商品，验证勾选一个购物车后的结算流程\n        ", "steps": [{"name": "切换zipcode到98011", "status": "passed", "start": 1750919797558, "stop": 1750919797576}, {"name": "清空购物车", "status": "passed", "start": 1750919797576, "stop": 1750919799176}, {"name": "从首页进入Deals分类", "status": "passed", "start": 1750919799176, "stop": 1750919814897}, {"name": "添加多种类型的商品", "status": "passed", "steps": [{"name": "应用多个筛选条件并加购商品", "status": "passed", "steps": [{"name": "应用Local Delivery筛选条件并加购商品", "status": "passed", "steps": [{"name": "重置筛选条件", "status": "passed", "start": 1750919824021, "stop": 1750919829781}], "start": 1750919814898, "stop": 1750919829781}, {"name": "应用Pantry筛选条件并加购商品", "status": "passed", "start": 1750919829782, "stop": 1750919838881}], "start": 1750919814897, "stop": 1750919838881}], "start": 1750919814897, "stop": 1750919838881}, {"name": "进入购物车页面，验证商品已添加成功", "status": "passed", "steps": [{"name": "验证购物车中的商品", "status": "passed", "start": 1750919846314, "stop": 1750919846370}], "start": 1750919838881, "stop": 1750919846432}, {"name": "点击结算按钮，验证中间页面元素", "status": "failed", "statusDetails": {"message": "AssertionError: 关闭按钮点击后中间页仍然可见\nassert not True\n +  where True = <bound method Locator.is_visible of <Locator frame=<Frame name= url='https://www.sayweee.com/en/cart'> selector=\"//h2[text()='Select carts for checkout']\">>(timeout=3000)\n +    where <bound method Locator.is_visible of <Locator frame=<Frame name= url='https://www.sayweee.com/en/cart'> selector=\"//h2[text()='Select carts for checkout']\">> = <Locator frame=<Frame name= url='https://www.sayweee.com/en/cart'> selector=\"//h2[text()='Select carts for checkout']\">.is_visible\n +      where <Locator frame=<Frame name= url='https://www.sayweee.com/en/cart'> selector=\"//h2[text()='Select carts for checkout']\"> = <bound method Page.locator of <Page url='https://www.sayweee.com/en/cart'>>(\"//h2[text()='Select carts for checkout']\")\n +        where <bound method Page.locator of <Page url='https://www.sayweee.com/en/cart'>> = <Page url='https://www.sayweee.com/en/cart'>.locator\n +        and   \"//h2[text()='Select carts for checkout']\" = mweb_cart_ele.ele_cart_middle_title\n", "trace": "  File \"D:\\MyWork\\Python\\EC-demo\\qa-ui-dmweb\\src\\Mweb\\EC\\testcases\\mweb_cart\\test_112223_mweb_cart_intermediate_ui_ux.py\", line 221, in test_112223_mweb_cart_intermediate_ui_ux\n    assert not p.locator(mweb_cart_ele.ele_cart_middle_title).is_visible(timeout=3000), \"关闭按钮点击后中间页仍然可见\"\n"}, "start": 1750919846432, "stop": 1750919853468}], "start": 1750919797558, "stop": 1750919853469, "uuid": "7c5ea1f0-4832-4a21-b8b3-6ef276394bbb", "historyId": "b3be6026ff4b5aab9f5d2cee2b723df5", "testCaseId": "b3be6026ff4b5aab9f5d2cee2b723df5", "fullName": "src.Mweb.EC.testcases.mweb_cart.test_112223_mweb_cart_intermediate_ui_ux.TestMwebCartIntermediatePage#test_112223_mweb_cart_intermediate_ui_ux", "labels": [{"name": "story", "value": "购物车-中间-验证中间页的交互"}, {"name": "tag", "value": "present"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "mweb_regression"}, {"name": "tag", "value": "<PERSON><PERSON><PERSON>"}, {"name": "parentSuite", "value": "src.Mweb.EC.testcases.mweb_cart"}, {"name": "suite", "value": "test_112223_mweb_cart_intermediate_ui_ux"}, {"name": "subSuite", "value": "TestMwebCartIntermediatePage"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "23528-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Mweb.EC.testcases.mweb_cart.test_112223_mweb_cart_intermediate_ui_ux"}]}
{"name": "【108558】 购物车页面-切换日期验证 V2", "status": "failed", "statusDetails": {"message": "AssertionError: 配送日期未成功更新\nassert 'Fri, 06/27' != 'Fri, 06/27'", "trace": "self = <test_108558_mWeb_change_delivery_date_on_cart_page_ui_ux_v2.TestMWebChangeDeliveryDateOnCartPageUIUXV2 object at 0x00000143FC256F90>\nphone_page = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppDat...091\\chrome-win\\chrome.exe> version=120.0.6099.28>>, 'page': <Page url='https://www.sayweee.com/en/cart?joinEnki=true'>}\nh5_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...uZBFvf9xCE7fdAfz0j0_peXZ2AmEY5oTjm3kGArdKc_-Yr8hE1AQywfTOCOeXcqcQvjW03ZcQ5ApcUVdOJgLZUVza2jic-6FwlXX08RYiCKroMT4', ...}\nh5_open_and_close_trace = None\n\n    @allure.title(\"【108558】 购物车页面-切换日期验证 V2\")\n    def test_108558_mWeb_change_delivery_date_on_cart_page_ui_ux_v2(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):\n        \"\"\"\n        【108558】 购物车页面-切换日期验证\n        测试步骤：\n        1、进入购物车页面\n        2、检查并切换zipcode为98011\n        3、如果购物车为空，从推荐模块加购商品\n        4、点击生鲜购物车上的切换日期按钮\n        5、弹出切换日期pop\n        6、点击pop里的日期进行切换日期\n        7、验证购物车日期切换成功\n        \"\"\"\n        p: Page = phone_page.get(\"page\")\n        c = phone_page.get(\"context\")\n    \n        # 1. 直接进入指定页面页面\n        cart_page = MWebCartPage(p, h5_autotest_header, browser_context=c, page_url=\"/cart\")\n        p.wait_for_timeout(3000)\n        log.info(\"成功进入购物车页面\")\n    \n        # 2. 检查并切换zipcode为98011\n        update_zipcode_v1(h5_autotest_header, \"98011\")\n        p.reload()\n        p.wait_for_timeout(3000)\n    \n        # 3. 如果购物车为空，从推荐模块加购商品\n    \n    \n        self.add_items_if_cart_empty(p,cart_page)\n        p.reload()\n        p.wait_for_timeout(3000)\n        # 4. 点击生鲜购物车上的切换日期按钮\n        # 使用data-testid定位切换日期按钮\n        delivery_date_btn = p.get_by_test_id(\"btn-change-delivery-date\")\n        assert delivery_date_btn.is_visible(), \"购物车页面切换日期按钮未显示\"\n    \n        # 获取当前显示的配送日期文本，用于后续验证\n        current_date_text = delivery_date_btn.text_content()\n        log.info(f\"当前配送日期: {current_date_text}\")\n    \n        # 点击切换日期按钮\n        delivery_date_btn.click()\n        p.wait_for_timeout(1000)\n        log.info(\"点击切换日期按钮\")\n    \n        # 5. 断言进入切换日期pop页面\n        # 断言进入切换日期pop页面\n        assert cart_page.FE.ele(mweb_common_ele.ele_delivery_date_popup).is_visible(), \"切换日期弹窗未显示\"\n    \n        # date_popup = p.get_by_test_id(\"delivery-date-popup\")\n        # assert date_popup.is_visible(), \"切换日期弹窗未显示\"\n        log.info(\"成功弹出切换日期弹窗\")\n    \n        # 6. 点击pop里的日期进行切换\n        # 获取所有可选日期\n        date_options = p.get_by_test_id(\"wid-delivery-date-item\").all()\n        assert len(date_options) > 0, \"没有可选的配送日期\"\n    \n        # 选择第一个不同于当前日期的选项\n        selected_new_date = False\n        for date_option in date_options:\n            date_text = date_option.text_content()\n            if date_text != current_date_text:\n                date_option.click()\n                selected_new_date = True\n                log.info(f\"选择新的配送日期: {date_text}\")\n                break\n    \n        # 如果所有日期都与当前日期相同，则选择第一个日期\n        if not selected_new_date and len(date_options) > 0:\n            date_options[0].click()\n            log.info(f\"选择第一个配送日期: {date_options[0].text_content()}\")\n    \n        p.wait_for_timeout(2000)\n    \n        # 7. 验证购物车日期切换成功\n        # 验证弹窗已关闭\n        # assert not date_popup.is_visible(), \"切换日期后弹窗未关闭\"\n    \n        # 验证日期已更新\n        updated_date_btn = p.get_by_test_id(\"btn-change-delivery-date\")\n        assert updated_date_btn.is_visible(), \"购物车页面切换日期按钮未显示\"\n    \n        # 获取更新后的日期文本\n        updated_date_text = updated_date_btn.text_content()\n        log.info(f\"更新后的配送日期: {updated_date_text}\")\n    \n        # 如果选择了新日期，验证日期已更改\n        if selected_new_date:\n>           assert updated_date_text != current_date_text, \"配送日期未成功更新\"\nE           AssertionError: 配送日期未成功更新\nE           assert 'Fri, 06/27' != 'Fri, 06/27'\n\nsrc\\Mweb\\EC\\testcases\\mweb_home\\mweb_porder\\test_108558_mWeb_change_delivery_date_on_cart_page_ui_ux_v2.py:109: AssertionError"}, "description": "\n        【108558】 购物车页面-切换日期验证\n        测试步骤：\n        1、进入购物车页面\n        2、检查并切换zipcode为98011\n        3、如果购物车为空，从推荐模块加购商品\n        4、点击生鲜购物车上的切换日期按钮\n        5、弹出切换日期pop\n        6、点击pop里的日期进行切换日期\n        7、验证购物车日期切换成功\n        ", "start": 1750920166044, "stop": 1750920190390, "uuid": "f21f2ff3-a6d8-4bf8-9646-3c1584793b58", "historyId": "cf7afd47f620a8379254a81626a76086", "testCaseId": "cf7afd47f620a8379254a81626a76086", "fullName": "src.Mweb.EC.testcases.mweb_home.mweb_porder.test_108558_mWeb_change_delivery_date_on_cart_page_ui_ux_v2.TestMWebChangeDeliveryDateOnCartPageUIUXV2#test_108558_mWeb_change_delivery_date_on_cart_page_ui_ux_v2", "labels": [{"name": "story", "value": "【108558】 购物车页面-切换日期验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "h5order"}, {"name": "tag", "value": "mweb_regression"}, {"name": "tag", "value": "transaction"}, {"name": "tag", "value": "suqin"}, {"name": "parentSuite", "value": "src.Mweb.EC.testcases.mweb_home.mweb_porder"}, {"name": "suite", "value": "test_108558_mWeb_change_delivery_date_on_cart_page_ui_ux_v2"}, {"name": "subSuite", "value": "TestMWebChangeDeliveryDateOnCartPageUIUXV2"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "23528-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Mweb.EC.testcases.mweb_home.mweb_porder.test_108558_mWeb_change_delivery_date_on_cart_page_ui_ux_v2"}]}
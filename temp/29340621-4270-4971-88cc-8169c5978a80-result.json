{"name": "首页按任意关键字搜索，关键字为rice", "status": "passed", "parameters": [{"name": "data", "value": "'rice'"}], "start": 1745316153585, "stop": 1745316182283, "uuid": "7a09f908-0f79-42c7-a56f-28c56ab5fe18", "historyId": "fb641cca5a1abd56b2162149e893c582", "testCaseId": "d4087ce8b18cc14c6e866655283f4378", "fullName": "src.Dweb.EC.testcases.dweb_home.test_002_home_search_page.TestSearchAtHome#test_search_by_category", "labels": [{"name": "story", "value": "首页搜索"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "dweb_regression"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.dweb_home"}, {"name": "suite", "value": "test_002_home_search_page"}, {"name": "subSuite", "value": "TestSearchAtHome"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "23072-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.dweb_home.test_002_home_search_page"}]}
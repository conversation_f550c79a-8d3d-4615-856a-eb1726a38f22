{"name": "【108209】 PDP-分享-商品分享流程验证", "status": "passed", "description": "\n        【108209】 PDP-分享-商品分享流程验证\n        ", "start": 1750920225339, "stop": 1750920253583, "uuid": "18cd0245-a971-43ca-ae9e-29277ed923cb", "historyId": "267f76995fda263539332b8b765e5ea2", "testCaseId": "267f76995fda263539332b8b765e5ea2", "fullName": "src.Mweb.EC.testcases.mweb_product.mweb_pdp.test_108209_mWeb_pdp_product_share_ui_ux.TestMWebPDPProductShareUIUX#test_108209_mWeb_pdp_product_share_ui_ux", "labels": [{"name": "story", "value": "【108209】 PDP-分享-商品分享流程验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "h5pdp"}, {"name": "tag", "value": "mweb_regression"}, {"name": "tag", "value": "transaction"}, {"name": "tag", "value": "suqin"}, {"name": "parentSuite", "value": "src.Mweb.EC.testcases.mweb_product.mweb_pdp"}, {"name": "suite", "value": "test_108209_mWeb_pdp_product_share_ui_ux"}, {"name": "subSuite", "value": "TestMWebPDPProductShareUIUX"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "23528-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Mweb.EC.testcases.mweb_product.mweb_pdp.test_108209_mWeb_pdp_product_share_ui_ux"}]}
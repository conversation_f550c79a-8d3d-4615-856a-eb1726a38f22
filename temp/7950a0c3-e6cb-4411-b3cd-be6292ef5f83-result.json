{"name": "WEB-注册-onboarding-页面UI/UX验证", "status": "broken", "statusDetails": {"message": "playwright._impl._errors.TimeoutError: Timeout 50000ms exceeded.", "trace": "self = <src.Dweb.EC.testcases.dweb_signup_signout.test_102480_web_signup_onboarding_ui_ux.TestWebSignupOnboardingUIUX object at 0x000001FDD71A0FD0>\npage = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppDat...aywright\\chromium-1091\\chrome-win\\chrome.exe> version=120.0.6099.28>>, 'page': <Page url='https://www.sayweee.com/en'>}\npc_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...sQTAcnJR8A7JAOh1ZSD-Pa4ZLso29CFKKRkuhCy5rGmgdHxwcT2I5ZgjWzyOj35XTNm6dqAXzkKCpOvX2w-xkB_i5g-cBs2cJEMKdLXqajR1j72s', ...}\nlogin_trace = None\n\n    @allure.title(\"WEB-注册-onboarding-页面UI/UX验证\")\n    def test_102480_web_signup_onboarding_ui_ux(self, page: dict, pc_autotest_header,login_trace):\n        \"\"\"\n        【102480】 WEB-注册-onboarding-页面UI/UX验证\n        \"\"\"\n        p:Page = page.get(\"page\")\n        c = page.get(\"context\")\n>       home_page = DWebHomePage(p, pc_autotest_header, browser_context=c)\n\nsrc\\Dweb\\EC\\testcases\\dweb_signup_signout\\test_102480_web_signup_onboarding_ui_ux.py:21: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\nsrc\\Dweb\\EC\\dweb_pages\\dweb_page_home\\dweb_page_home.py:35: in __init__\n    zipcode = self.page.locator(\"//div[@id='changeZipCode']/div[position()=1]\").text_content()\nvenv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py:18050: in text_content\n    self._sync(self._impl_obj.text_content(timeout=timeout))\nvenv\\Lib\\site-packages\\playwright\\_impl\\_locator.py:607: in text_content\n    return await self._frame.text_content(\nvenv\\Lib\\site-packages\\playwright\\_impl\\_frame.py:609: in text_content\n    return await self._channel.send(\"textContent\", locals_to_params(locals()))\nvenv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:62: in send\n    return await self._connection.wrap_api_call(\nvenv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:492: in wrap_api_call\n    return await cb()\n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n\nself = <playwright._impl._connection.Channel object at 0x000001FDD792DE90>\nmethod = 'textContent'\nparams = {'selector': \"//div[@id='changeZipCode']/div[position()=1]\", 'strict': True}\nreturn_as_dict = False\n\n    async def inner_send(\n        self, method: str, params: Optional[Dict], return_as_dict: bool\n    ) -> Any:\n        if params is None:\n            params = {}\n        callback = self._connection._send_message_to_server(\n            self._object, method, params\n        )\n        if self._connection._error:\n            error = self._connection._error\n            self._connection._error = None\n            raise error\n        done, _ = await asyncio.wait(\n            {\n                self._connection._transport.on_error_future,\n                callback.future,\n            },\n            return_when=asyncio.FIRST_COMPLETED,\n        )\n        if not callback.future.done():\n            callback.future.cancel()\n>       result = next(iter(done)).result()\nE       playwright._impl._errors.TimeoutError: Timeout 50000ms exceeded.\n\nvenv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:100: TimeoutError"}, "description": "\n        【102480】 WEB-注册-onboarding-页面UI/UX验证\n        ", "start": 1750652649584, "stop": 1750652716090, "uuid": "a556aa7a-7a05-42eb-ba1f-799da23b6a2f", "historyId": "dd853a281e58da1a39ee1a39f237c7f7", "testCaseId": "dd853a281e58da1a39ee1a39f237c7f7", "fullName": "src.Dweb.EC.testcases.dweb_signup_signout.test_102480_web_signup_onboarding_ui_ux.TestWebSignupOnboardingUIUX#test_102480_web_signup_onboarding_ui_ux", "labels": [{"name": "story", "value": "WEB-注册-onboarding-页面UI/UX验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "dweb_regression"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.dweb_signup_signout"}, {"name": "suite", "value": "test_102480_web_signup_onboarding_ui_ux"}, {"name": "subSuite", "value": "TestWebSignupOnboardingUIUX"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "15096-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.dweb_signup_signout.test_102480_web_signup_onboarding_ui_ux"}]}
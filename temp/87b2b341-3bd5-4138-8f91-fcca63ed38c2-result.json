{"name": "H5-首页分类验证", "status": "passed", "start": 1750920023879, "stop": 1750920107594, "uuid": "6a6a20cb-07fa-47d5-b3bd-83dcfd048f05", "historyId": "f63eb5d3679fa3c877592007134945db", "testCaseId": "f63eb5d3679fa3c877592007134945db", "fullName": "src.Mweb.EC.testcases.mweb_home.test_112811_h5_home_page_category_verify.TestH5HomePageCategoryVerify#test_112811_h5_home_page_category_verify", "labels": [{"name": "story", "value": "H5-首页分类验证"}, {"name": "tag", "value": "h5home"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "mweb_regression"}, {"name": "parentSuite", "value": "src.Mweb.EC.testcases.mweb_home"}, {"name": "suite", "value": "test_112811_h5_home_page_category_verify"}, {"name": "subSuite", "value": "TestH5HomePageCategoryVerify"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "23528-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Mweb.EC.testcases.mweb_home.test_112811_h5_home_page_category_verify"}]}
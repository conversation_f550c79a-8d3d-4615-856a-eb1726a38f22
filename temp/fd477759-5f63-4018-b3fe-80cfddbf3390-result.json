{"name": "首页按热词搜索", "status": "passed", "start": 1745316183697, "stop": 1745316419870, "uuid": "33dc3333-1b47-42c5-a359-9282fc0d0b79", "historyId": "c9dc06194ce82e25ae7ae1b1c9bccba0", "testCaseId": "c9dc06194ce82e25ae7ae1b1c9bccba0", "fullName": "src.Dweb.EC.testcases.dweb_home.test_002_home_search_page.TestSearchAtHome#test_search_by_hotkey", "labels": [{"name": "story", "value": "首页搜索"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "dweb_regression"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.dweb_home"}, {"name": "suite", "value": "test_002_home_search_page"}, {"name": "subSuite", "value": "TestSearchAtHome"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "23072-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.dweb_home.test_002_home_search_page"}]}
{"name": "H5生鲜购物车UI自动化验证", "status": "passed", "description": "\n        生鲜购物车UI自动化验证:\n        1. 调用切换zipcode接口切换到98011\n        2. 加购为你推荐商品\n        3. 遍历购物车商品\n        4. 获取购物车商品标题和价格(带$符号)\n        ", "start": 1750919906407, "stop": 1750919931073, "uuid": "ab1369ad-0865-468d-be81-a123c89574f3", "historyId": "dc1a5eb8e3188c27ba150905e1a34d3d", "testCaseId": "dc1a5eb8e3188c27ba150905e1a34d3d", "fullName": "src.Mweb.EC.testcases.mweb_cart.test_112719_mweb_grocery_cart_ui_ux.TestMwebGroceryCartUIUX#test_fresh_grocery_cart_ui", "labels": [{"name": "story", "value": "H5购物车-生鲜购物车UI/UX验证"}, {"name": "tag", "value": "fresh"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "mweb_regression"}, {"name": "tag", "value": "<PERSON><PERSON><PERSON>"}, {"name": "parentSuite", "value": "src.Mweb.EC.testcases.mweb_cart"}, {"name": "suite", "value": "test_112719_mweb_grocery_cart_ui_ux"}, {"name": "subSuite", "value": "TestMwebGroceryCartUIUX"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "23528-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Mweb.EC.testcases.mweb_cart.test_112719_mweb_grocery_cart_ui_ux"}]}
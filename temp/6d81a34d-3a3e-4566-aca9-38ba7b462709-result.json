{"name": "【108221】 订单列表待付款 tab-订单流程验证", "status": "failed", "statusDetails": {"message": "AssertionError: 未找到待付款tab\nassert False\n +  where False = <bound method Locator.is_visible of <Locator frame=<Frame name= url='https://www.sayweee.com/en/account/my_orders'> selector='internal:testid=[data-testid=\"wid-order-list-tab-1\"s]'>>()\n +    where <bound method Locator.is_visible of <Locator frame=<Frame name= url='https://www.sayweee.com/en/account/my_orders'> selector='internal:testid=[data-testid=\"wid-order-list-tab-1\"s]'>> = <Locator frame=<Frame name= url='https://www.sayweee.com/en/account/my_orders'> selector='internal:testid=[data-testid=\"wid-order-list-tab-1\"s]'>.is_visible", "trace": "self = <src.Dweb.EC.testcases.dweb_account.dweb_order.test_108221_dWeb_my_pending_order_ui_ux.TestDWebMyPendingOrderUIUX object at 0x000001CEEBD83590>\npage = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppDat...1091\\chrome-win\\chrome.exe> version=120.0.6099.28>>, 'page': <Page url='https://www.sayweee.com/en/account/my_orders'>}\npc_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...RQsAFh_-vqtVaibJAHZ6fUZU2ihbgNHuZ1HAQHfikwvAVCLwu8HBe8kp0Uw55Q2PxMsytyFlKXDwb8NWQkcoJjcyEaZxZrv76sytbUZ9GukJZEnU', ...}\nlogin_trace = None\n\n    @allure.title(\"【108221】 订单列表待付款 tab-订单流程验证\")\n    def test_108221_dWeb_my_pending_order_ui_ux(self, page: dict, pc_autotest_header, login_trace):\n        \"\"\"\n        【108221】 订单列表待付款 tab-订单流程验证\n        测试步骤：\n        1、切换到待付款tab 下，检查是否有待付款订单\n        2、如果没有，点击start shopping，进入首页\n        3、如果有查看待付款订单，验证订单信息正确，订单状态正确\n        4、点击订单列表下各按钮\n        5、点击订单进入订单详情，验证订单详情信息正确，各交互正确\n        3、4、5、 依赖测试数据\n        \"\"\"\n        p: Page = page.get(\"page\")\n        c = page.get(\"context\")\n    \n        # 1. 进入订单列表页面\n        order_page = DWebOrderListPage(p, pc_autotest_header, browser_context=c, page_url=\"/account/my_orders\")\n        p.wait_for_timeout(3000)\n        log.info(\"成功进入订单列表页面\")\n        # 切换到待付款tab\n        pending_tab = p.get_by_test_id(dweb_order_list_ele.order_pending_tab_ele)\n>       assert pending_tab.is_visible(), \"未找到待付款tab\"\nE       AssertionError: 未找到待付款tab\nE       assert False\nE        +  where False = <bound method Locator.is_visible of <Locator frame=<Frame name= url='https://www.sayweee.com/en/account/my_orders'> selector='internal:testid=[data-testid=\"wid-order-list-tab-1\"s]'>>()\nE        +    where <bound method Locator.is_visible of <Locator frame=<Frame name= url='https://www.sayweee.com/en/account/my_orders'> selector='internal:testid=[data-testid=\"wid-order-list-tab-1\"s]'>> = <Locator frame=<Frame name= url='https://www.sayweee.com/en/account/my_orders'> selector='internal:testid=[data-testid=\"wid-order-list-tab-1\"s]'>.is_visible\n\nsrc\\Dweb\\EC\\testcases\\dweb_account\\dweb_order\\test_108221_dWeb_my_pending_order_ui_ux.py:36: AssertionError"}, "description": "\n        【108221】 订单列表待付款 tab-订单流程验证\n        测试步骤：\n        1、切换到待付款tab 下，检查是否有待付款订单\n        2、如果没有，点击start shopping，进入首页\n        3、如果有查看待付款订单，验证订单信息正确，订单状态正确\n        4、点击订单列表下各按钮\n        5、点击订单进入订单详情，验证订单详情信息正确，各交互正确\n        3、4、5、 依赖测试数据\n        ", "start": *************, "stop": *************, "uuid": "0170b6a3-fa45-44f4-888a-0fe3b1c1e198", "historyId": "b8b2dd200916e62ee41bd4e8ae07bd45", "testCaseId": "b8b2dd200916e62ee41bd4e8ae07bd45", "fullName": "src.Dweb.EC.testcases.dweb_account.dweb_order.test_108221_dWeb_my_pending_order_ui_ux.TestDWebMyPendingOrderUIUX#test_108221_dWeb_my_pending_order_ui_ux", "labels": [{"name": "story", "value": "【108221】 订单列表待付款 tab-订单流程验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "dweb_porder"}, {"name": "tag", "value": "dweb_regression"}, {"name": "tag", "value": "transaction"}, {"name": "tag", "value": "suqin"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.dweb_account.dweb_order"}, {"name": "suite", "value": "test_108221_dWeb_my_pending_order_ui_ux"}, {"name": "subSuite", "value": "TestDWebMyPendingOrderUIUX"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "18600-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.dweb_account.dweb_order.test_108221_dWeb_my_pending_order_ui_ux"}]}
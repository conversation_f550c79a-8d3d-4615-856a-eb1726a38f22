{"name": "PC购物车-空购物车UI/UX验证", "status": "passed", "description": "\n        【113360】 PC购物车-空购物车UI/UX验证\n        此用例的校验点有：\n        1. 进入购物车，清空购物车\n        2. 断言空购物车img存在\n        3. 断言空购物车 文案存在\n        4. 点击空购物车的start_shopping按钮\n        5. 断言跳转到了首页\n        ", "start": 1745315771228, "stop": 1745315807565, "uuid": "6f97bb0a-941e-48b6-ba69-5f93605cb935", "historyId": "7d9aebe8791734c5fc119ff1c2a50334", "testCaseId": "7d9aebe8791734c5fc119ff1c2a50334", "fullName": "src.Dweb.EC.testcases.dweb_cart.test_113360_dWeb_empty_cart_ui_ux.TestDWebEmptyCartUIUX#test_113360_dWeb_empty_cart_ui_ux", "labels": [{"name": "story", "value": "PC购物车-空购物车UI/UX验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "pccart"}, {"name": "tag", "value": "dweb_regression"}, {"name": "tag", "value": "transaction"}, {"name": "tag", "value": "suqin"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.dweb_cart"}, {"name": "suite", "value": "test_113360_dWeb_empty_cart_ui_ux"}, {"name": "subSuite", "value": "TestDWebEmptyCartUIUX"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "23072-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.dweb_cart.test_113360_dWeb_empty_cart_ui_ux"}]}
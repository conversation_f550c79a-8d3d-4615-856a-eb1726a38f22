{"name": "【109167】 购物车-换购模块加购金额 大于35 ，小于68 UX验证", "status": "passed", "description": "\n        【109167】 购物车-换购模块加购金额 大于35 ，小于68 UX验证\n        ", "start": 1745319966562, "stop": 1745320004608, "uuid": "8b513d1f-3092-45a3-83d5-011f7b0de9e5", "historyId": "c590b5ed9d8d53c41c359574e3e3e0cf", "testCaseId": "c590b5ed9d8d53c41c359574e3e3e0cf", "fullName": "src.Mweb.EC.testcases.mweb_cart.test_109167_mWeb_cart_trade_in_35_x_68__ui_ux.TestMWebEmptyCartUIUX#test_109167_mWeb_cart_trade_in_35_x_68__ui_ux", "labels": [{"name": "story", "value": "【109167】 购物车-换购模块加购金额 大于35 ，小于68 UX验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "mweb_cart"}, {"name": "tag", "value": "mweb_regression"}, {"name": "tag", "value": "transaction"}, {"name": "tag", "value": "suqin"}, {"name": "parentSuite", "value": "src.Mweb.EC.testcases.mweb_cart"}, {"name": "suite", "value": "test_109167_mWeb_cart_trade_in_35_x_68__ui_ux"}, {"name": "subSuite", "value": "TestMWebEmptyCartUIUX"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "11500-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Mweb.EC.testcases.mweb_cart.test_109167_mWeb_cart_trade_in_35_x_68__ui_ux"}]}
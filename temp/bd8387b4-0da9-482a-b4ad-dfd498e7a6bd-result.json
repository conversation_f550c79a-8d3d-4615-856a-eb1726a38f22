{"name": "H5购物车-MO购物车UI/UX验证", "status": "failed", "statusDetails": {"message": "AssertionError: Price 价格不是两位小数\nassert None\n +  where None = <function match at 0x00000143F5FF7E20>('^\\\\$\\\\d+\\\\.\\\\d{2}$', '$9.88$25.00')\n +    where <function match at 0x00000143F5FF7E20> = re.match", "trace": "self = <src.Mweb.EC.testcases.mweb_cart.test_112719_mweb_mo_cart_ui_ux.TestMwebMoCartUIUX object at 0x00000143FC1CD590>\nphone_page = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppDat...091\\chrome-win\\chrome.exe> version=120.0.6099.28>>, 'page': <Page url='https://www.sayweee.com/en/cart?joinEnki=true'>}\nh5_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...uZBFvf9xCE7fdAfz0j0_peXZ2AmEY5oTjm3kGArdKc_-Yr8hE1AQywfTOCOeXcqcQvjW03ZcQ5ApcUVdOJgLZUVza2jic-6FwlXX08RYiCKroMT4', ...}\nh5_open_and_close_trace = None\n\n    @allure.title(\"H5购物车-MO购物车UI/UX验证\")\n    @pytest.mark.present\n    def test_112719_MWeb_mo_cart_ui_ux(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):\n        \"\"\"\n        【112719】 H5购物车-MO购物车UI/UX验证\n        \"\"\"\n        p: Page = phone_page.get(\"page\")\n        c = phone_page.get(\"context\")\n        switch_zipcode(h5_autotest_header, 99348)\n        p.wait_for_timeout(2000)\n        cart_page = MWebCartPage(p, h5_autotest_header, browser_context=c,\n                                 page_url=\"/cart\")\n    \n        try:\n            empty_cart(h5_autotest_header)\n            log.info(\"清空购物车成功\")\n            p.wait_for_timeout(2000)\n        except Exception as e:\n            log.info(\"清空购物车发生异常\" + str(e))\n            p.wait_for_timeout(3000)\n        # 滚动到指定位置-猜你喜欢\n        scroll_one_page_until(p, mweb_cart_ele.ele_recommend_tab)\n        p.wait_for_timeout(2000)\n        # 获取猜你喜欢商品\n        recommend_card = cart_page.FE.eles(mweb_cart_ele.ele_recommend_module_card)\n        for index1, item1 in enumerate(recommend_card):\n            # 加购推荐商品\n            item1.query_selector(\"//div[@data-testid='btn-atc-plus']\").click()\n            # item1.query_selector(mweb_cart_ele.ele_cart_atc_normal_plus).click()\n            p.wait_for_timeout(1000)\n            if index1 == 2:\n                break\n        # 滚动到指定位置-购物车顶部\n        scroll_one_page_until(p, mweb_cart_ele.ele_cart_normal_card)\n        assert cart_page.FE.ele(mweb_cart_ele.ele_cart_normal).is_visible(), \"直邮购物车不存在\"\n    \n        # 判断直邮购物车的标题=Direct mail\n        assert \"Direct mail\" == cart_page.FE.ele(mweb_cart_ele.ele_cart_pantry_top_title).text_content()\n        # 判断只有购物车标题下面的文案显示正确\n        assert \"Shipping via FedEx, UPS, etc.\" == cart_page.FE.ele(mweb_cart_ele.ele_mo_cart_text).text_content()\n    \n        # 判断shipping_fee中有美元符号存在或为free\n        shipping_fee = cart_page.FE.eles(mweb_cart_ele.ele_shipping_fee)\n        for sf in shipping_fee:\n            log.debug(\"delivery_fee的content===>\" + sf.text_content())\n            assert \"$\" in sf.text_content() or 'Free delivery' == sf.text_content()\n        # 获取所有的items total\n        items_total = cart_page.FE.eles(mweb_cart_ele.ele_items_total)\n        assert items_total, f\"items_total={items_total}\"\n        # 3. 判断items_total中有美元符号存在\n        for item in items_total:\n            log.debug(\"item.text_content===>\" + item.text_content())\n            assert \"$\" in item.text_content()\n            p.wait_for_timeout(2000)\n        cart_page.FE.ele(mweb_cart_ele.ele_cart_quality_click).click()\n        p.wait_for_timeout(2000)\n        # 查到购物车商品卡片上加/减元素部分\n        # assert cart_page.FE.ele(mweb_cart_ele.ele_cart_atc_normal_minus).is_visible(), \"删减商品按钮不存在\"\n        # assert cart_page.FE.ele(mweb_cart_ele.ele_cart_atc_normal_plus).is_visible(), \"增加商品按钮不存在\"\n    \n        # 方式2：先获取购物车区域，再获取其中的商品\n        cart_div = cart_page.FE.ele(mweb_cart_ele.ele_cart_normal)\n        if cart_div:\n            # 使用商品卡片的选择器，这个选择器在mweb_cart_ele.py中定义\n            all_goods = cart_page.FE.eles(mweb_cart_ele.ele_cart_normal_card)\n            goods_count = len(all_goods) if all_goods else 0\n            log.info(f\"购物车商品总数: {goods_count}\")\n            if goods_count == 0:\n                # 添加调试信息\n                log.info(\"未找到商品，检查页面状态...\")\n                log.info(f\"购物车区域是否存在: {cart_div is not None}\")\n                log.info(f\"使用的选择器: {mweb_cart_ele.ele_cart_normal_card}\")\n                # 尝试等待商品加载\n                p.wait_for_timeout(2000)\n                # 重试一次\n                all_goods = cart_div.query_selector_all(mweb_cart_ele.ele_cart_normal_card)\n                goods_count = len(all_goods) if all_goods else 0\n                log.info(f\"重试后购物车商品总数: {goods_count}\")\n            assert goods_count > 0, \"购物车中没有商品\"\n    \n            # 记录找到的非free商品数量\n            normal_goods_count = 0\n    \n            # 如果需要遍历每个商品\n            for index, good in enumerate(all_goods):\n                log.info(f\"检查第 {index+1} 个商品\")\n    \n                # 检查当前商品是否有free标签\n                # 尝试多种可能的free标签选择器\n                free_label = None\n                free_selectors = [\n                    \"//span[contains(text(), 'Free')]\",\n                    \"//div[contains(text(), 'Free')]\",\n                    \"//div[@data-testid='gift-label']\",\n                    \"//div[contains(@class, 'gift-label')]\"\n                ]\n    \n                for selector in free_selectors:\n                    try:\n                        element = good.query_selector(selector)\n                        if element and element.is_visible():\n                            free_label = element\n                            log.info(f\"找到free标签: {element.text_content()}\")\n                            break\n                    except Exception as e:\n                        log.debug(f\"查找free标签时出错: {str(e)}\")\n    \n                # 获取价格元素\n                product_prices = good.query_selector(\"//div[@data-testid='wid-product-card-price']\")\n                if not product_prices:\n                    log.warning(f\"商品 {index+1} 未找到价格元素\")\n                    continue\n    \n                price_text = product_prices.text_content()\n                log.info(f\"商品 {index+1} 价格: {price_text}\")\n    \n                # 如果是免费/赠品商品，跳过按钮检查\n                if free_label or \"Free\" in price_text:\n                    log.info(f\"商品 {index+1} 是免费/赠品商品，跳过按钮检查\")\n                    # 对于免费/赠品商品，只检查价格显示正确\n                    assert \"Free\" in price_text or \"$\" in price_text, \"免费/赠品商品价格显示不正确\"\n                    continue  # 跳过此商品的其他检查\n    \n                # 计数非free商品\n                normal_goods_count += 1\n    \n                # 以下是对非免费/赠品商品的检查\n                price_pattern = r'^\\$\\d+\\.\\d{2}$'\n    \n                # 查到购物车商品卡片上加/减元素部分\n                quality_click_element = good.query_selector(\"//div[@data-testid='btn-quantity-click']\")\n                if not quality_click_element:\n                    # 尝试备用选择器\n                    quality_click_element = good.query_selector(\"//div[@class='absolute bottom-0 right-0']\")\n    \n                if quality_click_element:\n                    log.info(f\"点击商品 {index+1} 的数量区域\")\n                    quality_click_element.click()\n                    p.wait_for_timeout(1000)\n                else:\n                    log.warning(f\"商品 {index+1} 未找到数量点击元素，跳过加减按钮检查\")\n                    continue\n    \n                # 检查加减按钮 - 使用相对于当前商品的选择器\n                minus_btn = good.query_selector(\"//div[@data-testid='btn-atc-minus']\")\n                plus_btn = good.query_selector(\"//div[@data-testid='btn-atc-plus']\")\n    \n                if minus_btn and plus_btn:\n                    assert minus_btn.is_visible(), \"删减商品按钮不存在\"\n                    assert plus_btn.is_visible(), \"增加商品按钮不存在\"\n                    log.info(f\"商品 {index+1} 的加减按钮检查通过\")\n                else:\n                    log.warning(f\"商品 {index+1} 未找到加减按钮，可能是特殊商品\")\n                    # 尝试再次点击数量区域\n                    if quality_click_element:\n                        log.info(f\"再次点击商品 {index+1} 的数量区域\")\n                        quality_click_element.click()\n                        p.wait_for_timeout(1000)\n    \n                        # 重新检查加减按钮\n                        minus_btn = good.query_selector(\"//div[@data-testid='btn-atc-minus']\")\n                        plus_btn = good.query_selector(\"//div[@data-testid='btn-atc-plus']\")\n    \n                        if minus_btn and plus_btn:\n                            assert minus_btn.is_visible(), \"删减商品按钮不存在\"\n                            assert plus_btn.is_visible(), \"增加商品按钮不存在\"\n                            log.info(f\"商品 {index+1} 的加减按钮检查通过\")\n                        else:\n                            log.warning(f\"再次尝试后仍未找到商品 {index+1} 的加减按钮，跳过此商品\")\n                            continue\n                    else:\n                        continue\n    \n                # 校验商品的价格以$开头\n                assert \"$\" in price_text, \"商品价格不包含$符号\"\n                # 校验价格是两位小数\n>               assert re.match(price_pattern, price_text), f\"Price 价格不是两位小数\"\nE               AssertionError: Price 价格不是两位小数\nE               assert None\nE                +  where None = <function match at 0x00000143F5FF7E20>('^\\\\$\\\\d+\\\\.\\\\d{2}$', '$9.88$25.00')\nE                +    where <function match at 0x00000143F5FF7E20> = re.match\n\nsrc\\Mweb\\EC\\testcases\\mweb_cart\\test_112719_mweb_mo_cart_ui_ux.py:205: AssertionError"}, "description": "\n        【112719】 H5购物车-MO购物车UI/UX验证\n        ", "start": 1750919931380, "stop": 1750919961518, "uuid": "c9725eff-a621-45cb-8723-c2552dc16f2e", "historyId": "d6dfb11cf9db1f5284d63669a3daccc5", "testCaseId": "d6dfb11cf9db1f5284d63669a3daccc5", "fullName": "src.Mweb.EC.testcases.mweb_cart.test_112719_mweb_mo_cart_ui_ux.TestMwebMoCartUIUX#test_112719_MWeb_mo_cart_ui_ux", "labels": [{"name": "story", "value": "H5购物车-MO购物车UI/UX验证"}, {"name": "tag", "value": "present"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "mweb_regression"}, {"name": "tag", "value": "<PERSON><PERSON><PERSON>"}, {"name": "parentSuite", "value": "src.Mweb.EC.testcases.mweb_cart"}, {"name": "suite", "value": "test_112719_mweb_mo_cart_ui_ux"}, {"name": "subSuite", "value": "TestMwebMoCartUIUX"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "23528-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Mweb.EC.testcases.mweb_cart.test_112719_mweb_mo_cart_ui_ux"}]}
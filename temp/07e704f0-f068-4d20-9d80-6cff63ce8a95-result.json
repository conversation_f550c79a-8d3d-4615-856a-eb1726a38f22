{"name": "【108558】 购物车页面-切换日期验证", "status": "passed", "description": "\n        【108558】 购物车页面-切换日期验证\n        ", "start": 1750920152884, "stop": 1750920165740, "uuid": "40a953bf-63f6-4524-ab7f-e9235c5448a9", "historyId": "8c1bd682b7f125b48eb71a9d4b13bf55", "testCaseId": "8c1bd682b7f125b48eb71a9d4b13bf55", "fullName": "src.Mweb.EC.testcases.mweb_home.mweb_porder.test_108558_mWeb_change_delivery_date_on_cart_page_ui_ux.TestMWebChangeDeliveryDateOnCartPageUIUX#test_108558_mWeb_change_delivery_date_on_cart_page_ui_ux", "labels": [{"name": "story", "value": "【108558】 购物车页面-切换日期验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "h5order"}, {"name": "tag", "value": "mweb_regression"}, {"name": "tag", "value": "transaction"}, {"name": "tag", "value": "suqin"}, {"name": "parentSuite", "value": "src.Mweb.EC.testcases.mweb_home.mweb_porder"}, {"name": "suite", "value": "test_108558_mWeb_change_delivery_date_on_cart_page_ui_ux"}, {"name": "subSuite", "value": "TestMWebChangeDeliveryDateOnCartPageUIUX"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "23528-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Mweb.EC.testcases.mweb_home.mweb_porder.test_108558_mWeb_change_delivery_date_on_cart_page_ui_ux"}]}
{"name": "H5购物车-稍后再买功能验证", "status": "broken", "statusDetails": {"message": "AttributeError: 'NoneType' object has no attribute 'click'", "trace": "self = <src.Mweb.EC.testcases.mweb_cart.test_112719_mweb_mo_cart_ui_ux.TestMwebMoCartUIUX object at 0x00000143FC1CDC50>\nphone_page = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppDat...091\\chrome-win\\chrome.exe> version=120.0.6099.28>>, 'page': <Page url='https://www.sayweee.com/en/cart?joinEnki=true'>}\nh5_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...uZBFvf9xCE7fdAfz0j0_peXZ2AmEY5oTjm3kGArdKc_-Yr8hE1AQywfTOCOeXcqcQvjW03ZcQ5ApcUVdOJgLZUVza2jic-6FwlXX08RYiCKroMT4', ...}\n\n    @allure.title(\"H5购物车-稍后再买功能验证\")\n    def test_112719_MWeb_mo_cart_save_for_later(self, phone_page: dict, h5_autotest_header):\n        \"\"\"\n        验证购物车稍后再买功能:\n        1. 添加推荐商品到购物车\n        2. 将商品移到稍后再买\n        3. 验证稍后再买区域显示\n        4. 将商品移回购物车\n        5. 验证商品回到购物车\n        \"\"\"\n        p: Page = phone_page.get(\"page\")\n        c = phone_page.get(\"context\")\n    \n        # 进入购物车页面\n        cart_page = MWebCartPage(p, h5_autotest_header, browser_context=c, page_url=\"/cart\")\n        p.wait_for_timeout(2000)\n    \n        # 滚动到推荐商品区域并添加商品\n        recommend_card = cart_page.FE.eles(mweb_cart_ele.ele_recommend_module_card)\n        for index1, item1 in enumerate(recommend_card):\n            # 加购推荐商品\n>           item1.query_selector(mweb_cart_ele.ele_cart_atc_normal_plus).click()\nE           AttributeError: 'NoneType' object has no attribute 'click'\n\nsrc\\Mweb\\EC\\testcases\\mweb_cart\\test_112719_mweb_mo_cart_ui_ux.py:261: AttributeError"}, "description": "\n        验证购物车稍后再买功能:\n        1. 添加推荐商品到购物车\n        2. 将商品移到稍后再买\n        3. 验证稍后再买区域显示\n        4. 将商品移回购物车\n        5. 验证商品回到购物车\n        ", "start": 1750919961893, "stop": 1750919966975, "uuid": "d2239b90-9220-45cc-afa7-7e95e8bdee2a", "historyId": "295483ef5d454799341984c0055ddd8e", "testCaseId": "295483ef5d454799341984c0055ddd8e", "fullName": "src.Mweb.EC.testcases.mweb_cart.test_112719_mweb_mo_cart_ui_ux.TestMwebMoCartUIUX#test_112719_MWeb_mo_cart_save_for_later", "labels": [{"name": "story", "value": "H5购物车-MO购物车UI/UX验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "mweb_regression"}, {"name": "tag", "value": "<PERSON><PERSON><PERSON>"}, {"name": "parentSuite", "value": "src.Mweb.EC.testcases.mweb_cart"}, {"name": "suite", "value": "test_112719_mweb_mo_cart_ui_ux"}, {"name": "subSuite", "value": "TestMwebMoCartUIUX"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "23528-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Mweb.EC.testcases.mweb_cart.test_112719_mweb_mo_cart_ui_ux"}]}
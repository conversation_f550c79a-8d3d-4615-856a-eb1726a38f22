{"name": "【107320】 $35<X<$68-换购页面UI/UX验证", "status": "broken", "statusDetails": {"message": "TypeError: MWebTradeInPage.__init__() missing 1 required positional argument: 'header'", "trace": "self = <src.Mweb.EC.testcases.mweb_cart.mweb_trade_in.test_107320_mWeb_trade_in_35_x_68_ui_ux.TestMWebTradeInUIUX object at 0x00000143FC1EC410>\nphone_page = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppDat...091\\chrome-win\\chrome.exe> version=120.0.6099.28>>, 'page': <Page url='https://www.sayweee.com/en/cart?joinEnki=true'>}\nh5_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...uZBFvf9xCE7fdAfz0j0_peXZ2AmEY5oTjm3kGArdKc_-Yr8hE1AQywfTOCOeXcqcQvjW03ZcQ5ApcUVdOJgLZUVza2jic-6FwlXX08RYiCKroMT4', ...}\n\n    @allure.title(\"【107320】 $35<X<$68-换购页面UI/UX验证\")\n    def test_107320_mWeb_trade_in_35_x_68_ui_ux(self, phone_page: dict, h5_autotest_header):\n        \"\"\"\n        【107320】 $35<X<$68-换购页面UI/UX验证\n        测试步骤：\n        1. 清空购物车后从推荐模块加购商品\n        2. 验证购物车换购模块状态\n        3. 进入换购页面，验证按钮状态和提示信息\n        4. 进入凑单页面，验证各项功能\n        5. 完成凑单后返回购物车，验证状态\n        \"\"\"\n        p: Page = phone_page.get(\"page\")\n        c = phone_page.get(\"context\")\n    \n        # 初始化页面和清空购物车\n        try:\n            CommonCheck().set_porder(h5_autotest_header, 98011)\n            empty_cart(h5_autotest_header)\n        except Exception as e:\n            log.info(f\"初始化失败: {str(e)}\")\n    \n        # 初始化页面对象\n        cart_page = MWebCartPage(p, h5_autotest_header, browser_context=c, page_url=\"/cart\")\n>       trade_in_page = MWebTradeInPage(p)\nE       TypeError: MWebTradeInPage.__init__() missing 1 required positional argument: 'header'\n\nsrc\\Mweb\\EC\\testcases\\mweb_cart\\mweb_trade_in\\test_107320_mWeb_trade_in_35_x_68_ui_ux.py:42: TypeError"}, "description": "\n        【107320】 $35<X<$68-换购页面UI/UX验证\n        测试步骤：\n        1. 清空购物车后从推荐模块加购商品\n        2. 验证购物车换购模块状态\n        3. 进入换购页面，验证按钮状态和提示信息\n        4. 进入凑单页面，验证各项功能\n        5. 完成凑单后返回购物车，验证状态\n        ", "start": 1750919966984, "stop": 1750919976809, "uuid": "023dcf03-1a1c-41b5-a871-45e24d432972", "historyId": "b9bbd4f5b8f519686e11987833541b42", "testCaseId": "b9bbd4f5b8f519686e11987833541b42", "fullName": "src.Mweb.EC.testcases.mweb_cart.mweb_trade_in.test_107320_mWeb_trade_in_35_x_68_ui_ux.TestMWebTradeInUIUX#test_107320_mWeb_trade_in_35_x_68_ui_ux", "labels": [{"name": "story", "value": "【107320】 $35<X<$68-换购页面UI/UX验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "h5cart"}, {"name": "tag", "value": "mweb_regression"}, {"name": "tag", "value": "transaction"}, {"name": "parentSuite", "value": "src.Mweb.EC.testcases.mweb_cart.mweb_trade_in"}, {"name": "suite", "value": "test_107320_mWeb_trade_in_35_x_68_ui_ux"}, {"name": "subSuite", "value": "TestMWebTradeInUIUX"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "23528-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Mweb.EC.testcases.mweb_cart.mweb_trade_in.test_107320_mWeb_trade_in_35_x_68_ui_ux"}]}
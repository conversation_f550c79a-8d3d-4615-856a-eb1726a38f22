{"name": "【108222】 订单列表已发货 tab订单流程验证", "status": "failed", "statusDetails": {"message": "AssertionError: 未找到待发货tab\nassert False\n +  where False = <bound method Locator.is_visible of <Locator frame=<Frame name= url='https://www.sayweee.com/en/account/my_orders'> selector='internal:testid=[data-testid=\"wid-order-list-tab-2\"s]'>>()\n +    where <bound method Locator.is_visible of <Locator frame=<Frame name= url='https://www.sayweee.com/en/account/my_orders'> selector='internal:testid=[data-testid=\"wid-order-list-tab-2\"s]'>> = <Locator frame=<Frame name= url='https://www.sayweee.com/en/account/my_orders'> selector='internal:testid=[data-testid=\"wid-order-list-tab-2\"s]'>.is_visible", "trace": "self = <src.Dweb.EC.testcases.dweb_account.dweb_order.test_108222_dWeb_my_unshipped_order_ui_ux.TestDWebMyUnShippedOrderUIUX object at 0x000001CEEBE01550>\npage = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppDat...1091\\chrome-win\\chrome.exe> version=120.0.6099.28>>, 'page': <Page url='https://www.sayweee.com/en/account/my_orders'>}\npc_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...RQsAFh_-vqtVaibJAHZ6fUZU2ihbgNHuZ1HAQHfikwvAVCLwu8HBe8kp0Uw55Q2PxMsytyFlKXDwb8NWQkcoJjcyEaZxZrv76sytbUZ9GukJZEnU', ...}\nlogin_trace = None\n\n    @allure.title(\"【108222】 订单列表已发货 tab订单流程验证\")\n    def test_108222_dWeb_my_unshipped_order_ui_ux(self, page: dict, pc_autotest_header, login_trace):\n        \"\"\"\n        【108222】 订单列表待发货 tab-订单流程验证\n        测试步骤：\n        1、切换到待发货tab 下，检查是否有待发货订单\n        2、如果没有，点击start shopping，进入首页\n        3、如果有查看待发货订单，验证订单信息正确，订单状态正确\n        4、点击订单列表下各按钮\n        5、点击订单进入订单详情，验证订单详情信息正确，各交互正确\n        3、4、5、 依赖测试数据\n        \"\"\"\n        p: Page = page.get(\"page\")\n        c = page.get(\"context\")\n    \n        # 1. 进入订单列表页面\n        order_page = DWebOrderListPage(p, pc_autotest_header, browser_context=c, page_url=\"/account/my_orders\")\n        p.wait_for_timeout(3000)\n        log.info(\"成功进入订单列表页面\")\n        # 切换到待发货tab\n        unshipped_tab = p.get_by_test_id(dweb_order_list_ele.order_unshipped_tab_ele)\n>       assert unshipped_tab.is_visible(), \"未找到待发货tab\"\nE       AssertionError: 未找到待发货tab\nE       assert False\nE        +  where False = <bound method Locator.is_visible of <Locator frame=<Frame name= url='https://www.sayweee.com/en/account/my_orders'> selector='internal:testid=[data-testid=\"wid-order-list-tab-2\"s]'>>()\nE        +    where <bound method Locator.is_visible of <Locator frame=<Frame name= url='https://www.sayweee.com/en/account/my_orders'> selector='internal:testid=[data-testid=\"wid-order-list-tab-2\"s]'>> = <Locator frame=<Frame name= url='https://www.sayweee.com/en/account/my_orders'> selector='internal:testid=[data-testid=\"wid-order-list-tab-2\"s]'>.is_visible\n\nsrc\\Dweb\\EC\\testcases\\dweb_account\\dweb_order\\test_108222_dWeb_my_unshipped_order_ui_ux.py:36: AssertionError"}, "description": "\n        【108222】 订单列表待发货 tab-订单流程验证\n        测试步骤：\n        1、切换到待发货tab 下，检查是否有待发货订单\n        2、如果没有，点击start shopping，进入首页\n        3、如果有查看待发货订单，验证订单信息正确，订单状态正确\n        4、点击订单列表下各按钮\n        5、点击订单进入订单详情，验证订单详情信息正确，各交互正确\n        3、4、5、 依赖测试数据\n        ", "start": *************, "stop": *************, "uuid": "089d2690-d819-4561-acdb-53c5cb1908f9", "historyId": "a0c51497791add562ca258431f860f56", "testCaseId": "a0c51497791add562ca258431f860f56", "fullName": "src.Dweb.EC.testcases.dweb_account.dweb_order.test_108222_dWeb_my_unshipped_order_ui_ux.TestDWebMyUnShippedOrderUIUX#test_108222_dWeb_my_unshipped_order_ui_ux", "labels": [{"name": "story", "value": "【108222】 订单列表已发货 tab订单流程验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "dweb_porder"}, {"name": "tag", "value": "dweb_regression"}, {"name": "tag", "value": "transaction"}, {"name": "tag", "value": "suqin"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.dweb_account.dweb_order"}, {"name": "suite", "value": "test_108222_dWeb_my_unshipped_order_ui_ux"}, {"name": "subSuite", "value": "TestDWebMyUnShippedOrderUIUX"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "18600-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.dweb_account.dweb_order.test_108222_dWeb_my_unshipped_order_ui_ux"}]}
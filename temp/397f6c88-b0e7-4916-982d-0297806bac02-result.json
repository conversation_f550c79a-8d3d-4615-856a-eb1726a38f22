{"name": "test_home_banner", "status": "passed", "description": "\n        测试Home_banner\n        ", "start": 1745316834343, "stop": 1745316876506, "uuid": "00e141e4-8b1d-4305-b0ce-d7116073149a", "historyId": "a38f09bc6691c63af0e35f2ba5af7fde", "testCaseId": "a38f09bc6691c63af0e35f2ba5af7fde", "fullName": "src.Dweb.EC.testcases.dweb_home.test_003_page_home.TestAtHome#test_home_banner", "labels": [{"name": "story", "value": "首页搜索by category"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "dweb_regression"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.dweb_home"}, {"name": "suite", "value": "test_003_page_home"}, {"name": "subSuite", "value": "TestAtHome"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "23072-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.dweb_home.test_003_page_home"}]}
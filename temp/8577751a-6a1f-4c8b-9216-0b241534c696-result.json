{"name": "PC购物车-多种类型购物车点击结算验证中间页默认状态", "status": "broken", "statusDetails": {"message": "playwright._impl._errors.TimeoutError: Timeout 50000ms exceeded.", "trace": "self = <test_112223_dweb_multi_cart_checkout.TestDWebMultiCartCheckout object at 0x000001FDD7109110>\npage = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppDat...aywright\\chromium-1091\\chrome-win\\chrome.exe> version=120.0.6099.28>>, 'page': <Page url='https://www.sayweee.com/en'>}\npc_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...sQTAcnJR8A7JAOh1ZSD-Pa4ZLso29CFKKRkuhCy5rGmgdHxwcT2I5ZgjWzyOj35XTNm6dqAXzkKCpOvX2w-xkB_i5g-cBs2cJEMKdLXqajR1j72s', ...}\nlogin_trace = None\n\n    @allure.title(\"PC购物车-多种类型购物车点击结算验证中间页默认状态\")\n    @pytest.mark.present\n    def test_dweb_multi_cart_checkout_flow(self, page: dict, pc_autotest_header, login_trace):\n        \"\"\"\n        用户购物车有多种类型购物车点击结算验证中间页默认状态:\n        步骤1: 在首页加购2个商品，然后点击Global+按钮进入Global+页面加购2个商品\n        步骤2: 点击首页Cart按钮，进入购物车页面进行校验，最后点击Checkout按钮\n        步骤3: 弹出中间页面，验证购物车默认是不勾选状态，提示用户没有勾选购物车的提示\n        步骤4: 底部结算页显示灰色样式顶部展示Select all carts默认页面\n        步骤5: 底部展示You can select multiple carts for checkout.蓝色的文案一直展示\n        \"\"\"\n        p: Page = page.get(\"page\")\n        c = page.get(\"context\")\n    \n        with allure.step(\"步骤1：创建多种类型购物车\"):\n            # 初始化首页页面对象（会自动清空购物车并进入首页）\n>           home_page = DWebHomePage(p, pc_autotest_header, c)\n\nsrc\\Dweb\\EC\\testcases\\dweb_cart\\test_112223_dweb_multi_cart_checkout.py:30: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\nsrc\\Dweb\\EC\\dweb_pages\\dweb_page_home\\dweb_page_home.py:35: in __init__\n    zipcode = self.page.locator(\"//div[@id='changeZipCode']/div[position()=1]\").text_content()\nvenv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py:18050: in text_content\n    self._sync(self._impl_obj.text_content(timeout=timeout))\nvenv\\Lib\\site-packages\\playwright\\_impl\\_locator.py:607: in text_content\n    return await self._frame.text_content(\nvenv\\Lib\\site-packages\\playwright\\_impl\\_frame.py:609: in text_content\n    return await self._channel.send(\"textContent\", locals_to_params(locals()))\nvenv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:62: in send\n    return await self._connection.wrap_api_call(\nvenv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:492: in wrap_api_call\n    return await cb()\n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n\nself = <playwright._impl._connection.Channel object at 0x000001FDD792DE90>\nmethod = 'textContent'\nparams = {'selector': \"//div[@id='changeZipCode']/div[position()=1]\", 'strict': True}\nreturn_as_dict = False\n\n    async def inner_send(\n        self, method: str, params: Optional[Dict], return_as_dict: bool\n    ) -> Any:\n        if params is None:\n            params = {}\n        callback = self._connection._send_message_to_server(\n            self._object, method, params\n        )\n        if self._connection._error:\n            error = self._connection._error\n            self._connection._error = None\n            raise error\n        done, _ = await asyncio.wait(\n            {\n                self._connection._transport.on_error_future,\n                callback.future,\n            },\n            return_when=asyncio.FIRST_COMPLETED,\n        )\n        if not callback.future.done():\n            callback.future.cancel()\n>       result = next(iter(done)).result()\nE       playwright._impl._errors.TimeoutError: Timeout 50000ms exceeded.\n\nvenv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:100: TimeoutError"}, "description": "\n        用户购物车有多种类型购物车点击结算验证中间页默认状态:\n        步骤1: 在首页加购2个商品，然后点击Global+按钮进入Global+页面加购2个商品\n        步骤2: 点击首页Cart按钮，进入购物车页面进行校验，最后点击Checkout按钮\n        步骤3: 弹出中间页面，验证购物车默认是不勾选状态，提示用户没有勾选购物车的提示\n        步骤4: 底部结算页显示灰色样式顶部展示Select all carts默认页面\n        步骤5: 底部展示You can select multiple carts for checkout.蓝色的文案一直展示\n        ", "steps": [{"name": "步骤1：创建多种类型购物车", "status": "broken", "statusDetails": {"message": "playwright._impl._errors.TimeoutError: Timeout 50000ms exceeded.\n", "trace": "  File \"D:\\MyWork\\Python\\EC-demo\\qa-ui-dmweb\\src\\Dweb\\EC\\testcases\\dweb_cart\\test_112223_dweb_multi_cart_checkout.py\", line 30, in test_dweb_multi_cart_checkout_flow\n    home_page = DWebHomePage(p, pc_autotest_header, c)\n                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\MyWork\\Python\\EC-demo\\qa-ui-dmweb\\src\\Dweb\\EC\\dweb_pages\\dweb_page_home\\dweb_page_home.py\", line 35, in __init__\n    zipcode = self.page.locator(\"//div[@id='changeZipCode']/div[position()=1]\").text_content()\n              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\MyWork\\Python\\EC-demo\\qa-ui-dmweb\\venv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py\", line 18050, in text_content\n    self._sync(self._impl_obj.text_content(timeout=timeout))\n  File \"D:\\MyWork\\Python\\EC-demo\\qa-ui-dmweb\\venv\\Lib\\site-packages\\playwright\\_impl\\_sync_base.py\", line 115, in _sync\n    return task.result()\n           ^^^^^^^^^^^^^\n  File \"D:\\MyWork\\Python\\EC-demo\\qa-ui-dmweb\\venv\\Lib\\site-packages\\playwright\\_impl\\_locator.py\", line 607, in text_content\n    return await self._frame.text_content(\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\MyWork\\Python\\EC-demo\\qa-ui-dmweb\\venv\\Lib\\site-packages\\playwright\\_impl\\_frame.py\", line 609, in text_content\n    return await self._channel.send(\"textContent\", locals_to_params(locals()))\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\MyWork\\Python\\EC-demo\\qa-ui-dmweb\\venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 62, in send\n    return await self._connection.wrap_api_call(\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\MyWork\\Python\\EC-demo\\qa-ui-dmweb\\venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 492, in wrap_api_call\n    return await cb()\n           ^^^^^^^^^^\n  File \"D:\\MyWork\\Python\\EC-demo\\qa-ui-dmweb\\venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 100, in inner_send\n    result = next(iter(done)).result()\n             ^^^^^^^^^^^^^^^^^^^^^^^^^\n"}, "start": 1750650758204, "stop": 1750650825260}], "start": 1750650758204, "stop": 1750650825260, "uuid": "a09cc0f2-a464-4fa8-b4ed-7dc1c6bd3e37", "historyId": "54b8ef7874329ddd6adc77a969f480f8", "testCaseId": "54b8ef7874329ddd6adc77a969f480f8", "fullName": "src.Dweb.EC.testcases.dweb_cart.test_112223_dweb_multi_cart_checkout.TestDWebMultiCartCheckout#test_dweb_multi_cart_checkout_flow", "labels": [{"name": "story", "value": "PC购物车-多种类型购物车结算测试"}, {"name": "tag", "value": "present"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "pccart"}, {"name": "tag", "value": "dweb_regression"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.dweb_cart"}, {"name": "suite", "value": "test_112223_dweb_multi_cart_checkout"}, {"name": "subSuite", "value": "TestDWebMultiCartCheckout"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "15096-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.dweb_cart.test_112223_dweb_multi_cart_checkout"}]}
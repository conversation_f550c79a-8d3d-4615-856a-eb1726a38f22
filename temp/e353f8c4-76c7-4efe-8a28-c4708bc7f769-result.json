{"name": "【100829】 H5-Marketplace Banner Array 点击验证", "status": "passed", "description": "\n        【100829】 H5-Marketplace Banner Array 点击验证\n        测试步骤：\n        1. 直接访问 https://tb1.sayweee.net/zh/mkpl/waterfall 页面\n        2. 等待页面加载完成后等待5秒\n        3. 检查是否出现全球购介绍弹窗，如果有则关闭\n        4. 在页面中找到 Marketplace Banner Array 1 元素\n        5. 点击该元素\n        6. 验证跳转到包含 \"mkpl/top-items\" 的页面\n        7. 验证URL包含必要的参数tab=hot_selling&category=feature\n        ", "start": 1750919976834, "stop": 1750919991064, "uuid": "521fe813-5921-4d11-97f0-262892c957c7", "historyId": "77c0d1533e5c7e84915cded91c089603", "testCaseId": "77c0d1533e5c7e84915cded91c089603", "fullName": "src.Mweb.EC.testcases.mweb_global+.mweb_mkpl_home.test_100829_waterfall_banner_array_1.TestWaterfallBannerArray#test_100829_click_mkpl_banner_array_1", "labels": [{"name": "story", "value": "H5-Marketplace Banner Array 验证"}, {"name": "tag", "value": "h5home"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "mweb_regression"}, {"name": "parentSuite", "value": "src.Mweb.EC.testcases.mweb_global+.mweb_mkpl_home"}, {"name": "suite", "value": "test_100829_waterfall_banner_array_1"}, {"name": "subSuite", "value": "TestWaterfallBannerArray"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "23528-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Mweb.EC.testcases.mweb_global+.mweb_mkpl_home.test_100829_waterfall_banner_array_1"}]}
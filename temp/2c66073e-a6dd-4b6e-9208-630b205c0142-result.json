{"name": "首页按任意关键字搜索，关键字为fruit", "status": "passed", "parameters": [{"name": "data", "value": "'fruit'"}], "start": 1745316078980, "stop": 1745316120851, "uuid": "2fba3bca-bdfd-4e6b-8ac0-9303293e2d08", "historyId": "d336fb5b1a9e6d0da83f2dbd693f45a8", "testCaseId": "d4087ce8b18cc14c6e866655283f4378", "fullName": "src.Dweb.EC.testcases.dweb_home.test_002_home_search_page.TestSearchAtHome#test_search_by_category", "labels": [{"name": "story", "value": "首页搜索"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "dweb_regression"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.dweb_home"}, {"name": "suite", "value": "test_002_home_search_page"}, {"name": "subSuite", "value": "TestSearchAtHome"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "23072-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.dweb_home.test_002_home_search_page"}]}
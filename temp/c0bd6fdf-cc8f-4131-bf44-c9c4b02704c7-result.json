{"name": "test_home_login_and_place_order", "status": "broken", "statusDetails": {"message": "playwright._impl._errors.TimeoutError: Timeout 50000ms exceeded.", "trace": "self = <src.Dweb.EC.testcases.dweb_signup_signout.test_001_login_and_signin.TestLoginAndSignin object at 0x000001FDD7193590>\nsetup = <src.Dweb.EC.dweb_pages.dweb_page_home.dweb_page_login_and_signin.LoginAndSignin object at 0x000001FDD7A63FD0>\nnot_login_trace = None\n\n    def test_home_login_and_place_order(self, setup, not_login_trace):\n>       setup.login_and_place_order()\n\nsrc\\Dweb\\EC\\testcases\\dweb_signup_signout\\test_001_login_and_signin.py:18: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\nsrc\\Dweb\\EC\\dweb_pages\\dweb_page_home\\dweb_page_login_and_signin.py:41: in login_and_place_order\n    self.login()\nsrc\\Dweb\\EC\\dweb_pages\\dweb_page_home\\dweb_page_login_and_signin.py:96: in login\n    if self.page.locator(\"//div[@id='changeZipCode']/div[position()=1]\").text_content() != '98011':\nvenv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py:18050: in text_content\n    self._sync(self._impl_obj.text_content(timeout=timeout))\nvenv\\Lib\\site-packages\\playwright\\_impl\\_locator.py:607: in text_content\n    return await self._frame.text_content(\nvenv\\Lib\\site-packages\\playwright\\_impl\\_frame.py:609: in text_content\n    return await self._channel.send(\"textContent\", locals_to_params(locals()))\nvenv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:62: in send\n    return await self._connection.wrap_api_call(\nvenv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:492: in wrap_api_call\n    return await cb()\n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n\nself = <playwright._impl._connection.Channel object at 0x000001FDD7D73E50>\nmethod = 'textContent'\nparams = {'selector': \"//div[@id='changeZipCode']/div[position()=1]\", 'strict': True}\nreturn_as_dict = False\n\n    async def inner_send(\n        self, method: str, params: Optional[Dict], return_as_dict: bool\n    ) -> Any:\n        if params is None:\n            params = {}\n        callback = self._connection._send_message_to_server(\n            self._object, method, params\n        )\n        if self._connection._error:\n            error = self._connection._error\n            self._connection._error = None\n            raise error\n        done, _ = await asyncio.wait(\n            {\n                self._connection._transport.on_error_future,\n                callback.future,\n            },\n            return_when=asyncio.FIRST_COMPLETED,\n        )\n        if not callback.future.done():\n            callback.future.cancel()\n>       result = next(iter(done)).result()\nE       playwright._impl._errors.TimeoutError: Timeout 50000ms exceeded.\n\nvenv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:100: TimeoutError"}, "start": 1750652481184, "stop": 1750652559611, "uuid": "9e36f467-2c0f-419b-9adc-8a7233b92c63", "historyId": "********************************", "testCaseId": "********************************", "fullName": "src.Dweb.EC.testcases.dweb_signup_signout.test_001_login_and_signin.TestLoginAndSignin#test_home_login_and_place_order", "labels": [{"name": "story", "value": "首页登陆加购或先加购再登陆"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "coreflow"}, {"name": "tag", "value": "dweb_regression"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.dweb_signup_signout"}, {"name": "suite", "value": "test_001_login_and_signin"}, {"name": "subSuite", "value": "TestLoginAndSignin"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "15096-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.dweb_signup_signout.test_001_login_and_signin"}]}
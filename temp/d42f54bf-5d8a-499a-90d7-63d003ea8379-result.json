{"name": "进入fbw pdp页面检查元素并加购", "status": "passed", "description": "\n        100616 验证Global+ FBW 商品PDP UI-UX\n        ", "parameters": [{"name": "pdp_fbw_url", "value": "'https://www.sayweee.com/en/product/Pork-floss-with-mochi-cake-2pc/2044455?category=freshbakery02&parent_category=freshbakery'"}], "start": 1745317065027, "stop": 1745317072005, "uuid": "bb453d44-21ed-4162-af2d-eddbc43c00e6", "historyId": "af945b8541f70044793e511998b78e22", "testCaseId": "d21ae179374b15c7684288b4c24d1800", "fullName": "src.Dweb.EC.testcases.dweb_pdp.test_100_pdp_page_check.TestAtPDP#test_check_fbw_pdp_and_add_to_cart", "labels": [{"name": "story", "value": "产品PDP页面校验"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "pdp"}, {"name": "tag", "value": "smoke"}, {"name": "tag", "value": "dweb_regression"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.dweb_pdp"}, {"name": "suite", "value": "test_100_pdp_page_check"}, {"name": "subSuite", "value": "TestAtPDP"}, {"name": "host", "value": "SHLAP10880"}, {"name": "thread", "value": "23072-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.dweb_pdp.test_100_pdp_page_check"}]}
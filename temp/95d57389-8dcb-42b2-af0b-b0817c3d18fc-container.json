{"uuid": "36319066-63e4-4c83-907f-336cca74fa74", "children": ["31412d8a-c869-4353-b1df-032c6ccdae24", "f9ed84a1-62d7-4356-8cf6-ea088f7c7d25", "a0c1485d-825d-41a3-973c-a6ab6cacb314", "ef3f5e07-a7a3-405f-848a-99f1d06bd58c", "306e1cbc-e824-4368-8067-cf31236924e5"], "befores": [{"name": "setup", "status": "broken", "statusDetails": {"message": "playwright._impl._errors.TimeoutError: Timeout 50000ms exceeded.\n", "trace": "  File \"D:\\MyWork\\Python\\EC-demo\\qa-ui-dmweb\\venv\\Lib\\site-packages\\pluggy\\_callers.py\", line 77, in _multicall\n    res = hook_impl.function(*args)\n          ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\MyWork\\Python\\EC-demo\\qa-ui-dmweb\\venv\\Lib\\site-packages\\_pytest\\fixtures.py\", line 1123, in pytest_fixture_setup\n    result = call_fixture_func(fixturefunc, request, kwargs)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\MyWork\\Python\\EC-demo\\qa-ui-dmweb\\venv\\Lib\\site-packages\\_pytest\\fixtures.py\", line 895, in call_fixture_func\n    fixture_result = next(generator)\n                     ^^^^^^^^^^^^^^^\n  File \"D:\\MyWork\\Python\\EC-demo\\qa-ui-dmweb\\src\\Dweb\\EC\\testcases\\dweb_home\\test_003_page_home.py\", line 19, in setup\n    hp = DWebHomePage(page[\"page\"], pc_autotest_header, page.get(\"context\"))\n         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\MyWork\\Python\\EC-demo\\qa-ui-dmweb\\src\\Dweb\\EC\\dweb_pages\\dweb_page_home\\dweb_page_home.py\", line 35, in __init__\n    zipcode = self.page.locator(\"//div[@id='changeZipCode']/div[position()=1]\").text_content()\n              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\MyWork\\Python\\EC-demo\\qa-ui-dmweb\\venv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py\", line 18050, in text_content\n    self._sync(self._impl_obj.text_content(timeout=timeout))\n  File \"D:\\MyWork\\Python\\EC-demo\\qa-ui-dmweb\\venv\\Lib\\site-packages\\playwright\\_impl\\_sync_base.py\", line 115, in _sync\n    return task.result()\n           ^^^^^^^^^^^^^\n  File \"D:\\MyWork\\Python\\EC-demo\\qa-ui-dmweb\\venv\\Lib\\site-packages\\playwright\\_impl\\_locator.py\", line 607, in text_content\n    return await self._frame.text_content(\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\MyWork\\Python\\EC-demo\\qa-ui-dmweb\\venv\\Lib\\site-packages\\playwright\\_impl\\_frame.py\", line 609, in text_content\n    return await self._channel.send(\"textContent\", locals_to_params(locals()))\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\MyWork\\Python\\EC-demo\\qa-ui-dmweb\\venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 62, in send\n    return await self._connection.wrap_api_call(\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\MyWork\\Python\\EC-demo\\qa-ui-dmweb\\venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 492, in wrap_api_call\n    return await cb()\n           ^^^^^^^^^^\n  File \"D:\\MyWork\\Python\\EC-demo\\qa-ui-dmweb\\venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 100, in inner_send\n    result = next(iter(done)).result()\n             ^^^^^^^^^^^^^^^^^^^^^^^^^\n"}, "start": 1750650883343, "stop": 1750650949912}], "start": 1750650883343, "stop": 1750650951545}